# تحديث أداة إصلاح المودات - ملخص الميزات الجديدة

## الميزات الجديدة المضافة

### 1. عرض ملفات ZIP في قائمة المودات
- تم إضافة دعم لعرض ملفات ZIP في قائمة المودات
- تم إضافة رموز خاصة لتمييز أنواع الملفات المختلفة:
  - 📦 لملفات ZIP
  - 🧩 لملفات MCADDON
  - 📚 لملفات MCPACK
  - 📄 للملفات الأخرى

### 2. تحويل صيغ الملفات
- تم تحسين ميزة تغيير صيغة الملفات لتشمل تحويل الملفات من/إلى:
  - ZIP
  - MCADDON
  - MCPACK
- يمكن تحويل ملف واحد أو عدة ملفات في نفس الوقت

### 3. استبدال ملفات المودات
- تمت إضافة زر جديد "استبدال ملف المود" يسمح باستبدال ملف المود الحالي بملف جديد
- يمكن للمستخدم اختيار ملف جديد من جهازه
- يمكن تغيير صيغة الملف أثناء عملية الاستبدال
- يمكن حفظ الملف الجديد محليًا أو رفعه إلى قاعدة البيانات

### 4. تحسينات واجهة المستخدم
- تم توسيع نافذة التطبيق لاستيعاب الأزرار الجديدة
- تم تحسين عرض أنواع الملفات في القائمة باستخدام الرموز
- تم تحديث منطق تفعيل/تعطيل الأزرار بناءً على التحديد الحالي

## كيفية استخدام الميزات الجديدة

### لتحويل صيغة ملف من ZIP إلى MCADDON أو MCPACK:
1. قم بتحميل قائمة المودات
2. حدد المود الذي تريد تغيير صيغته
3. انقر على زر "تغيير صيغة المحدد"
4. اختر الصيغة الجديدة المطلوبة (MCADDON أو MCPACK أو ZIP)
5. اختر ما إذا كنت تريد رفع الملف الجديد إلى قاعدة البيانات أو حفظه محليًا فقط

### لاستبدال ملف مود بملف جديد:
1. قم بتحميل قائمة المودات
2. حدد مودًا واحدًا فقط تريد استبداله
3. انقر على زر "استبدال ملف المود"
4. اختر الملف الجديد من جهازك
5. اختر الصيغة النهائية للملف
6. اختر ما إذا كنت تريد رفع الملف الجديد إلى قاعدة البيانات أو حفظه محليًا فقط

## ملاحظات هامة
- عند استبدال ملف، يتم التحقق من أن الملف الجديد هو ملف ZIP صالح
- عند رفع ملف جديد إلى قاعدة البيانات، يتم حذف الملف القديم تلقائيًا
- يمكن استخدام خاصية "رفع إلى Supabase بعد المعالجة" للتحكم في ما إذا كان سيتم رفع الملفات المعالجة أو حفظها محليًا فقط
