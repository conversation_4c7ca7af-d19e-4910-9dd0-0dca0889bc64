# -*- coding: utf-8 -*-
"""
وحدة استخراج بيانات المودات من موقع mcpedl.com
تم تصميمها للتكامل مع أداة نشر المودات الموجودة
"""

import requests
import time
import re
import json
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from typing import Dict, List, Optional, Any

class MCPEDLScraper:
    """كلاس استخراج بيانات المودات من mcpedl.com"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # محددات CSS لاستخراج البيانات
        self.selectors = {
            'title': 'h1.entry-title, h1.post-title, .post-header h1, h1',
            'description': '.entry-content p, .post-content p, .content p, article p',
            'category': '.breadcrumbs a:last-of-type, .category-link, .post-categories a',
            'main_image': '.entry-content img:first-of-type, .post-content img:first-of-type, .featured-image img',
            'gallery_images': '.gallery img, .wp-block-gallery img, .entry-content img, .post-content img',
            'version': '.supported-versions, .minecraft-version, .version-info, .compatibility',
            'download_link': '.download-link, .btn-download, a[href*="download"], .download-button',
            'file_size': '.file-size, .download-size, .size-info',
            'creator': '.author-info, .post-author, .creator-name, .by-author',
            'social_links': '.social-links a, .author-social a, .contact-links a',
            'downloads_count': '.download-count, .downloads, .download-stats',
            'likes_count': '.likes-count, .rating, .votes'
        }
    
    def is_valid_mcpedl_url(self, url: str) -> bool:
        """التحقق من صحة رابط mcpedl.com"""
        try:
            parsed = urlparse(url)
            return parsed.netloc.lower() in ['mcpedl.com', 'www.mcpedl.com']
        except:
            return False
    
    def fetch_page(self, url: str, max_retries: int = 3) -> Optional[BeautifulSoup]:
        """جلب وتحليل صفحة الويب"""
        if not self.is_valid_mcpedl_url(url):
            return None
        
        for attempt in range(max_retries):
            try:
                response = self.session.get(url, timeout=15, allow_redirects=True)
                response.raise_for_status()
                
                if 'text/html' not in response.headers.get('content-type', ''):
                    return None
                
                soup = BeautifulSoup(response.text, 'html.parser')
                
                if attempt < max_retries - 1:
                    time.sleep(1)  # تأخير بين الطلبات
                
                return soup
                
            except requests.exceptions.RequestException:
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # تأخير متزايد
        
        return None
    
    def clean_text(self, text: str) -> str:
        """تنظيف النص من المسافات الزائدة والأحرف الخاصة"""
        if not text:
            return ""
        
        text = re.sub(r'\s+', ' ', text.strip())
        text = re.sub(r'[^\w\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF.,!?()-]', '', text)
        return text.strip()
    
    def extract_title(self, soup: BeautifulSoup) -> str:
        """استخراج عنوان المود"""
        selectors = self.selectors['title'].split(', ')
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                title = self.clean_text(element.get_text())
                if title:
                    return title
        
        # محاولة استخراج من title tag
        title_tag = soup.find('title')
        if title_tag:
            title = self.clean_text(title_tag.get_text())
            title = title.replace(' - MCPEDL', '').replace(' | MCPEDL', '')
            if title:
                return title
        
        return ""
    
    def extract_description(self, soup: BeautifulSoup) -> str:
        """استخراج وصف المود"""
        selectors = self.selectors['description'].split(', ')
        description_parts = []
        
        for selector in selectors:
            elements = soup.select(selector)
            for element in elements[:5]:  # أول 5 فقرات فقط
                text = self.clean_text(element.get_text())
                if text and len(text) > 20:  # تجاهل النصوص القصيرة جداً
                    description_parts.append(text)
        
        description = ' '.join(description_parts)
        if description:
            return description[:2000]  # حد أقصى للطول
        
        return ""
    
    def extract_category(self, soup: BeautifulSoup) -> str:
        """استخراج فئة المود"""
        # محاولة استخراج من breadcrumbs
        breadcrumbs = soup.select('.breadcrumbs a, .breadcrumb a')
        if breadcrumbs and len(breadcrumbs) > 1:
            category = self.clean_text(breadcrumbs[-2].get_text())
            if category and category.lower() not in ['home', 'mcpedl']:
                return category
        
        # محاولة استخراج من URL
        selectors = self.selectors['category'].split(', ')
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                category = self.clean_text(element.get_text())
                if category:
                    return category
        
        return "Addons"  # فئة افتراضية
    
    def extract_images(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """استخراج روابط الصور"""
        image_urls = []
        
        # الصورة الرئيسية
        main_img_selectors = self.selectors['main_image'].split(', ')
        for selector in main_img_selectors:
            img = soup.select_one(selector)
            if img and img.get('src'):
                url = urljoin(base_url, img['src'])
                if self.validate_image_url(url):
                    image_urls.append(url)
                    break
        
        # صور المعرض
        gallery_selectors = self.selectors['gallery_images'].split(', ')
        for selector in gallery_selectors:
            images = soup.select(selector)
            for img in images[:10]:  # حد أقصى 10 صور
                src = img.get('src') or img.get('data-src')
                if src:
                    url = urljoin(base_url, src)
                    if self.validate_image_url(url) and url not in image_urls:
                        image_urls.append(url)
        
        return image_urls
    
    def validate_image_url(self, url: str) -> bool:
        """التحقق من صحة رابط الصورة"""
        if not url:
            return False
        
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
        url_lower = url.lower()
        
        return any(ext in url_lower for ext in image_extensions)
    
    def extract_versions(self, soup: BeautifulSoup) -> str:
        """استخراج إصدارات Minecraft المدعومة"""
        version_selectors = self.selectors['version'].split(', ')
        all_versions = []
        
        for selector in version_selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text()
                versions = self.extract_version_numbers(text)
                all_versions.extend(versions)
        
        # البحث في النص العام للصفحة
        page_text = soup.get_text()
        page_versions = self.extract_version_numbers(page_text)
        all_versions.extend(page_versions)
        
        # إزالة التكرارات والترتيب
        unique_versions = sorted(list(set(all_versions)), reverse=True)
        
        if unique_versions:
            return ', '.join(unique_versions[:5])  # أول 5 إصدارات
        
        return "1.20+"  # إصدار افتراضي
    
    def extract_version_numbers(self, text: str) -> List[str]:
        """استخراج أرقام إصدارات Minecraft من النص"""
        if not text:
            return []
        
        version_pattern = r'1\.\d+(?:\.\d+)*'
        versions = re.findall(version_pattern, text)
        
        return sorted(list(set(versions)), reverse=True)
    
    def extract_download_info(self, soup: BeautifulSoup, base_url: str) -> Dict[str, Any]:
        """استخراج معلومات التحميل"""
        download_info = {
            'download_url': '',
            'size': ''
        }
        
        # البحث عن رابط التحميل
        download_selectors = self.selectors['download_link'].split(', ')
        for selector in download_selectors:
            link = soup.select_one(selector)
            if link and link.get('href'):
                download_url = urljoin(base_url, link['href'])
                download_info['download_url'] = download_url
                break
        
        # البحث عن حجم الملف
        size_selectors = self.selectors['file_size'].split(', ')
        for selector in size_selectors:
            element = soup.select_one(selector)
            if element:
                size = self.extract_file_size(element.get_text())
                if size:
                    download_info['size'] = size
                    break
        
        return download_info
    
    def extract_file_size(self, text: str) -> Optional[str]:
        """استخراج حجم الملف من النص"""
        if not text:
            return None
        
        size_pattern = r'(\d+(?:\.\d+)?)\s*(KB|MB|GB|kb|mb|gb)'
        match = re.search(size_pattern, text, re.IGNORECASE)
        
        if match:
            size, unit = match.groups()
            return f"{size} {unit.upper()}"
        
        return None
    
    def extract_creator_info(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """استخراج معلومات المطور"""
        creator_info = {
            'creator_name': '',
            'creator_contact_info': '',
            'creator_social_channels': []
        }
        
        # اسم المطور
        creator_selectors = self.selectors['creator'].split(', ')
        for selector in creator_selectors:
            element = soup.select_one(selector)
            if element:
                creator_name = self.clean_text(element.get_text())
                if creator_name:
                    creator_info['creator_name'] = creator_name
                    break
        
        # روابط التواصل الاجتماعي
        social_selectors = self.selectors['social_links'].split(', ')
        for selector in social_selectors:
            links = soup.select(selector)
            for link in links:
                href = link.get('href')
                if href and any(platform in href.lower() for platform in 
                              ['youtube', 'discord', 'twitter', 'instagram', 'facebook', 'github']):
                    platform = self.extract_social_platform(href)
                    creator_info['creator_social_channels'].append(f"{platform}: {href}")
        
        return creator_info
    
    def extract_social_platform(self, url: str) -> str:
        """تحديد منصة التواصل الاجتماعي من الرابط"""
        if not url:
            return "unknown"
        
        url_lower = url.lower()
        
        if 'youtube.com' in url_lower or 'youtu.be' in url_lower:
            return 'YouTube'
        elif 'discord' in url_lower:
            return 'Discord'
        elif 'twitter.com' in url_lower or 'x.com' in url_lower:
            return 'Twitter/X'
        elif 'instagram.com' in url_lower:
            return 'Instagram'
        elif 'facebook.com' in url_lower:
            return 'Facebook'
        elif 'tiktok.com' in url_lower:
            return 'TikTok'
        elif 'github.com' in url_lower:
            return 'GitHub'
        else:
            return 'Other'
    
    def scrape_mod_data(self, url: str) -> Optional[Dict[str, Any]]:
        """استخراج جميع بيانات المود من الرابط"""
        # جلب الصفحة
        soup = self.fetch_page(url)
        if not soup:
            return None
        
        try:
            # استخراج البيانات الأساسية
            mod_data = {
                'name': self.extract_title(soup),
                'description': self.extract_description(soup),
                'category': self.extract_category(soup),
                'image_urls': self.extract_images(soup, url),
                'version': self.extract_versions(soup),
                'source_url': url
            }
            
            # معلومات التحميل
            download_info = self.extract_download_info(soup, url)
            mod_data.update(download_info)
            
            # معلومات المطور
            creator_info = self.extract_creator_info(soup)
            mod_data.update(creator_info)
            
            # التحقق من البيانات الأساسية
            if not mod_data['name']:
                return None
            
            return mod_data
            
        except Exception:
            return None
    
    def close(self):
        """إغلاق الجلسة"""
        self.session.close()

# دالة مساعدة للتكامل مع الأداة الرئيسية
def scrape_mcpedl_mod(url: str) -> Optional[Dict[str, Any]]:
    """دالة مبسطة لاستخراج بيانات مود من mcpedl.com"""
    scraper = MCPEDLScraper()
    try:
        return scraper.scrape_mod_data(url)
    finally:
        scraper.close()
