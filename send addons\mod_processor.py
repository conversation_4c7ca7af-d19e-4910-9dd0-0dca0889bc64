# -*- coding: utf-8 -*-
import os
import requests

# تغيير المجلد الحالي إلى مجلد الأداة
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)
from PIL import Image, ImageTk
from io import BytesIO
from supabase import create_client, Client
from bs4 import BeautifulSoup
import re
import time
import random
import string
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, simpledialog
import threading
import json
import unicodedata
import mimetypes
from urllib.parse import urljoin, urlparse
import zipfile
from tkinter import filedialog
import traceback # <-- IMPORT TRACEBACK
from typing import Dict, List, Optional, Any

# --- NEW: Gemini Import ---
gemini_extraction_cache = {} # Global cache for Gemini extraction results

try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    print("Warning: google-generativeai library not found. AI description generation will be disabled.")
    print("Install it using: pip install google-generativeai")

# --- NEW: MCPEDL Scraper Import ---
try:
    from mcpedl_scraper_module import scrape_mcpedl_mod, MCPEDLScraper
    MCPEDL_SCRAPER_AVAILABLE = True
    print("MCPEDL Scraper module loaded successfully.")
except ImportError:
    MCPEDL_SCRAPER_AVAILABLE = False
    print("Warning: MCPEDL Scraper module not found. MCPEDL extraction features will be disabled.")

try:
    import pyperclip
    PYPERCLIP_AVAILABLE = True
except ImportError:
    PYPERCLIP_AVAILABLE = False
    print("Warning: pyperclip library not found. Paste/Copy buttons will be disabled.")
    print("Install it using: pip install pyperclip")


# --- Configurations ---
STORAGE_URL = "https://mwxzwfeqsashcwvqthmd.supabase.co"
STORAGE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im13eHp3ZmVxc2FzaGN3dnF0aG1kIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MzU2NDcsImV4cCI6MjA2MTQxMTY0N30.nU0smAgNsoLi1zRNKA3AFM3q112jp4fhPgYeeXqKmPU"
IMAGE_BUCKET = "image"
MOD_BUCKET = "my_new_mods_bucket"
MAX_MOD_SIZE_MB = 50
MAX_IMAGE_SIZE_BYTES = 5 * 1024 * 1024 # 1MB limit for images

# Phosus API Keys
PHOSUS_AUTO_ENHANCE_API_KEY = "c4351d23d11f0223a2a1b8d08956402d"
PHOSUS_SUPER_RES_API_KEY = "f96ebe782d59c2e1696051b51675a553"
# Placeholder Phosus API Endpoints - these will need to be updated with actual URLs
PHOSUS_AUTO_ENHANCE_ENDPOINT = "https://api.phosus.com/v1/enhancement/auto" # Placeholder
PHOSUS_SUPER_RES_ENDPOINT = "https://api.phosus.com/v1/enhancement/super-resolution" # Placeholder


APP_DB_URL = 'https://ytqxxodyecdeosnqoure.supabase.co'
APP_DB_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4'
MODS_TABLE_NAME = 'mods'
CATEGORIES = ["Addons", "Shaders", "Texture Pack"]
MAX_MODS_IN_DB = 5000

storage_client: Client | None = None
app_db_client: Client | None = None
STORAGE_CLIENT_OK = False
APP_DB_CLIENT_OK = False

try:
    if STORAGE_URL and STORAGE_KEY:
        # إنشاء عميل Supabase بدون معامل proxy
        storage_client = create_client(STORAGE_URL, STORAGE_KEY)
        # Test the connection by trying to list buckets
        try:
            storage_client.storage.list_buckets()
            print("Supabase storage client initialized and tested successfully.")
            STORAGE_CLIENT_OK = True
        except Exception as test_e:
            print(f"Supabase storage client created but connection test failed: {test_e}")
            print("This might be due to network issues or invalid credentials.")
            STORAGE_CLIENT_OK = False
    else:
        print("Storage URL or Key is missing.")
        STORAGE_CLIENT_OK = False
except Exception as e:
    print(f"Error initializing Supabase storage client: {e}")
    print(f"Storage URL: {STORAGE_URL[:50]}..." if STORAGE_URL else "Storage URL: None")
    print(f"Storage Key: {'*' * 20}..." if STORAGE_KEY else "Storage Key: None")
    STORAGE_CLIENT_OK = False

try:
    if APP_DB_URL and APP_DB_KEY:
        # إنشاء عميل قاعدة البيانات بدون معامل proxy
        app_db_client = create_client(APP_DB_URL, APP_DB_KEY)
        # Test the connection by trying to access the mods table
        try:
            app_db_client.table(MODS_TABLE_NAME).select("id").limit(1).execute()
            print("Supabase App DB client initialized and tested successfully.")
            APP_DB_CLIENT_OK = True
        except Exception as test_e:
            print(f"Supabase App DB client created but connection test failed: {test_e}")
            print("This might be due to network issues, invalid credentials, or table access permissions.")
            APP_DB_CLIENT_OK = False
    else:
        print("App DB URL or Key is missing.")
        APP_DB_CLIENT_OK = False
except Exception as e:
    print(f"Error initializing Supabase App DB client: {e}")
    print(f"App DB URL: {APP_DB_URL[:50]}..." if APP_DB_URL else "App DB URL: None")
    print(f"App DB Key: {'*' * 20}..." if APP_DB_KEY else "App DB Key: None")
    APP_DB_CLIENT_OK = False

CONFIG_FILE = "config.json"
INCOMPLETE_MODS_FILE = "incomplete_mods.json"
PENDING_REVIEW_FILE = "pending_review.json"

def load_config():
    default_config = {"gemini_api_keys": []}
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                if "gemini_api_keys" not in config or not isinstance(config["gemini_api_keys"], list):
                    config["gemini_api_keys"] = []
                return config
        else:
            save_config(default_config)
            return default_config
    except Exception:
        return default_config

def save_config(config_data):
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
    except Exception as e:
        messagebox.showerror("Config Save Error", f"Failed to save configuration: {e}")

# --- NEW: Handler and Task for Direct Mod File Upload ---
def handle_upload_mod_file():
    """Handles the 'Upload Mod File' button click."""
    filepath = filedialog.askopenfilename(
        title="اختر ملف المود (mcaddon/mcpack)",
        filetypes=[("Minecraft Mod Files", "*.mcaddon *.mcpack"), ("All files", "*.*")]
    )
    if not filepath:
        update_status("تم إلغاء اختيار ملف المود.")
        return

    update_status(f"بدء معالجة ملف المود المحلي: {filepath}")
    # ستحتاج إلى زر جديد في الواجهة، افترض اسمه upload_mod_file_button
    if 'upload_mod_file_button' in globals() and upload_mod_file_button.winfo_exists():
        upload_mod_file_button.config(state=tk.DISABLED)

    run_in_thread(upload_mod_file_task, filepath)

def get_latest_mod_from_downloads():
    """البحث عن آخر ملف مود تم تنزيله في مجلد التنزيلات"""
    try:
        # الحصول على مسار مجلد التنزيلات
        downloads_folder = os.path.join(os.path.expanduser("~"), "Downloads")

        if not os.path.exists(downloads_folder):
            messagebox.showerror("خطأ", "لم يتم العثور على مجلد التنزيلات")
            return None

        # البحث عن جميع ملفات المود في المجلد
        mod_files = []
        for file in os.listdir(downloads_folder):
            if file.lower().endswith(('.mcaddon', '.mcpack')):
                full_path = os.path.join(downloads_folder, file)
                mod_files.append((full_path, os.path.getmtime(full_path)))

        if not mod_files:
            messagebox.showinfo("معلومات", "لم يتم العثور على ملفات مود في مجلد التنزيلات")
            return None

        # ترتيب الملفات حسب وقت التعديل (الأحدث أولاً)
        mod_files.sort(key=lambda x: x[1], reverse=True)

        # إرجاع مسار أحدث ملف
        latest_mod_path = mod_files[0][0]
        update_status(f"تم العثور على آخر ملف مود: {os.path.basename(latest_mod_path)}")
        return latest_mod_path

    except Exception as e:
        update_status(f"خطأ أثناء البحث عن ملفات المود: {e}")
        messagebox.showerror("خطأ", f"حدث خطأ أثناء البحث عن ملفات المود: {e}")
        return None

def handle_get_latest_mod():
    """معالجة زر جلب آخر ملف مود من مجلد التنزيلات"""
    latest_mod_path = get_latest_mod_from_downloads()
    if latest_mod_path:
        update_status(f"بدء معالجة آخر ملف مود: {latest_mod_path}")
        # تعطيل الزر أثناء المعالجة
        if 'get_latest_mod_button' in globals() and get_latest_mod_button.winfo_exists():
            get_latest_mod_button.config(state=tk.DISABLED)

        run_in_thread(upload_mod_file_task, latest_mod_path)

def download_file_content(direct_download_url, file_type_for_log="MOD"):
    """Downloads file content from a direct URL and returns bytes, filename, and content type."""
    content_bytes = None
    original_filename = f"{file_type_for_log.lower()}_file.mcpack"  # Default fallback filename
    content_type = 'application/octet-stream'  # Default content type

    try:
        update_status(f"Downloading {file_type_for_log} from: {direct_download_url}")

        # Special handling for CurseForge URLs
        if "curseforge.com" in direct_download_url.lower():
            update_status(f"Using special handling for CurseForge download: {direct_download_url}")

            # Set up headers to mimic a browser
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'en-US,en;q=0.9',
                'Referer': 'https://www.curseforge.com/',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Cache-Control': 'max-age=0',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1',
                'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"'
            }

            # Create a session to maintain cookies
            session = requests.Session()
            session.headers.update(headers)

            # First, check if the URL is already a direct download URL from edge.forgecdn.net
            if "edge.forgecdn.net" in direct_download_url:
                update_status(f"URL is already a direct forgecdn link: {direct_download_url}")
                try:
                    file_response = session.get(direct_download_url, stream=True, timeout=60)
                    file_response.raise_for_status()
                    content_bytes = file_response.content
                    content_type = file_response.headers.get('content-type', 'application/octet-stream').split(';')[0]

                    # Try to get filename from URL
                    parsed_path = urlparse(direct_download_url).path
                    if parsed_path and parsed_path != '/':
                        original_filename = os.path.basename(parsed_path)
                        update_status(f"Guessed filename for {file_type_for_log} from URL: {original_filename}")
                    else:
                        original_filename = f"{file_type_for_log}_file.mcpack"
                except Exception as e:
                    update_status(f"Error downloading from direct forgecdn link: {e}")
                    raise
            else:
                # Visit the download page to get cookies and find the download button
                update_status("Visiting CurseForge download page to get cookies...")
                try:
                    response = session.get(direct_download_url, timeout=60)
                    response.raise_for_status()
                except Exception as e:
                    update_status(f"Error accessing CurseForge page: {e}")
                    update_status(f"Traceback: {traceback.format_exc()}")
                    raise

                # Look for the download button in the page
                soup = BeautifulSoup(response.text, 'html.parser')

                # Extract version information if available
                version_element = soup.find('div', class_='version-title')
                if version_element:
                    version_text = version_element.get_text(strip=True)
                    version_match = re.search(r'(\d+\.\d+\.\d+|\d+\.\d+)', version_text)
                    if version_match:
                        extracted_version = version_match.group(1)
                        update_status(f"Extracted version from CurseForge: {extracted_version}")
                        # Store the extracted version in a global variable for later use
                        global extracted_version_str
                        extracted_version_str = extracted_version

                # Try different patterns for download buttons
                download_button = None

                # Pattern 1: Main download button with class 'button--download'
                download_button = soup.find('a', class_='button button--hollow button--download')

                # Pattern 2: Alternative download button
                if not download_button:
                    download_button = soup.find('a', class_='button button--download')

                # Pattern 3: Look for any link with 'download' in the class and text
                if not download_button:
                    for a_tag in soup.find_all('a'):
                        if ('download' in a_tag.get('class', []) or
                            'download' in a_tag.get_text().lower()) and a_tag.has_attr('href'):
                            download_button = a_tag
                            break

                if download_button and download_button.has_attr('href'):
                    actual_download_url = urljoin(direct_download_url, download_button['href'])
                    update_status(f"Found download button with URL: {actual_download_url}")

                    # Now download the file
                    update_status("Downloading file from CurseForge...")
                    try:
                        file_response = session.get(actual_download_url, stream=True, timeout=60)
                        file_response.raise_for_status()
                        content_bytes = file_response.content
                    except Exception as e:
                        update_status(f"Error downloading file from CurseForge: {e}")
                        update_status(f"Traceback: {traceback.format_exc()}")
                        raise

                    # Get Content-Type and Filename from headers
                    content_type = file_response.headers.get('content-type', 'application/octet-stream').split(';')[0]
                    content_disposition = file_response.headers.get('content-disposition')

                    if content_disposition:
                        update_status(f"Parsing CD for {file_type_for_log}: {content_disposition}")
                        filename_match = re.search(r'filename\*=UTF-8\'\'([\S]+)', content_disposition, flags=re.IGNORECASE)
                        if filename_match:
                            original_filename = requests.utils.unquote(filename_match.group(1))  # Decode URL encoding
                            update_status(f"Filename (UTF-8) for {file_type_for_log}: {original_filename}")
                        elif 'filename=' in content_disposition:
                            fn_part = content_disposition.split('filename=')[1]
                            original_filename = fn_part.split(';')[0].strip('"\' ')
                            update_status(f"Filename (basic) for {file_type_for_log}: {original_filename}")
                    else:
                        # Try to guess from URL if no CD header
                        parsed_path = urlparse(actual_download_url).path
                        if parsed_path and parsed_path != '/':
                            original_filename = os.path.basename(parsed_path)
                            update_status(f"Guessed filename for {file_type_for_log} from URL: {original_filename}")
                        else:
                            # Use mod name from page title if available
                            title_element = soup.find('title')
                            if title_element:
                                title_text = title_element.get_text(strip=True)
                                sanitized_title = re.sub(r'[^\w\-\.]', '_', title_text)
                                original_filename = f"{sanitized_title[:30]}.mcpack"
                                update_status(f"Created filename from page title: {original_filename}")
                            else:
                                original_filename = f"{file_type_for_log}_file.mcpack"
                else:
                    # If we can't find the download button, try to extract the file ID from the URL
                    update_status("Could not find download button. Trying to extract file ID...")
                    file_id_match = re.search(r'/files/(\d+)', direct_download_url)
                    if not file_id_match:
                        file_id_match = re.search(r'/download/(\d+)', direct_download_url)

                    if file_id_match:
                        file_id = file_id_match.group(1)
                        update_status(f"Extracted file ID: {file_id}")

                        # Construct the direct download URL
                        if len(file_id) > 4:
                            actual_download_url = f"https://edge.forgecdn.net/files/{file_id[:4]}/{file_id[4:]}/{file_id}.zip"
                        else:
                            # Handle case where file_id is short
                            actual_download_url = f"https://edge.forgecdn.net/files/{file_id}/{file_id}.zip"

                        update_status(f"Constructed direct download URL: {actual_download_url}")

                        # Download the file
                        try:
                            file_response = session.get(actual_download_url, stream=True, timeout=60)
                            file_response.raise_for_status()
                            content_bytes = file_response.content
                        except Exception as e:
                            update_status(f"Error downloading from constructed URL: {e}")
                            update_status(f"Traceback: {traceback.format_exc()}")
                            raise

                        # Get Content-Type and Filename
                        content_type = file_response.headers.get('content-type', 'application/octet-stream').split(';')[0]
                        original_filename = f"{file_type_for_log}_{file_id}.mcpack"
                    else:
                        # Last resort: try to find any download link in the page
                        update_status("Could not extract file ID. Looking for any download link...")
                        download_links = []
                        for a_tag in soup.find_all('a', href=True):
                            href = a_tag['href']
                            if ('download' in href.lower() or
                                href.lower().endswith(('.zip', '.mcpack', '.mcaddon'))):
                                download_links.append(urljoin(direct_download_url, href))

                        if download_links:
                            actual_download_url = download_links[0]
                            update_status(f"Found potential download link: {actual_download_url}")

                            try:
                                file_response = session.get(actual_download_url, stream=True, timeout=60)
                                file_response.raise_for_status()
                                content_bytes = file_response.content

                                # Get Content-Type and Filename
                                content_type = file_response.headers.get('content-type', 'application/octet-stream').split(';')[0]
                                parsed_path = urlparse(actual_download_url).path
                                if parsed_path and parsed_path != '/':
                                    original_filename = os.path.basename(parsed_path)
                                else:
                                    original_filename = f"{file_type_for_log}_file.mcpack"
                            except Exception as e:
                                update_status(f"Error downloading from found link: {e}")
                                raise
                        else:
                            raise ValueError("Could not find any download link on the CurseForge page")
        else:
            # Regular handling for non-CurseForge URLs
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Cache-Control': 'max-age=0'
            }
            response = requests.get(direct_download_url, stream=True, timeout=60, headers=headers)
            response.raise_for_status()

            content_bytes = response.content
            if not content_bytes:
                raise ValueError(f"Downloaded {file_type_for_log} file is empty")

            # Get Content-Type and Filename from headers
            content_type = response.headers.get('content-type', 'application/octet-stream').split(';')[0]
            content_disposition = response.headers.get('content-disposition')
            if content_disposition:
                update_status(f"Parsing CD for {file_type_for_log}: {content_disposition}")
                filename_match = re.search(r'filename\*=UTF-8\'\'([\S]+)', content_disposition, flags=re.IGNORECASE)
                if filename_match:
                    original_filename = requests.utils.unquote(filename_match.group(1))  # Decode URL encoding
                    update_status(f"Filename (UTF-8) for {file_type_for_log}: {original_filename}")
                elif 'filename=' in content_disposition:
                    fn_part = content_disposition.split('filename=')[1]
                    original_filename = fn_part.split(';')[0].strip('"\' ')
                    update_status(f"Filename (basic) for {file_type_for_log}: {original_filename}")
            else:
                # Try to guess from URL if no CD header
                parsed_path = urlparse(direct_download_url).path
                if parsed_path and parsed_path != '/':
                    original_filename = os.path.basename(parsed_path)
                    update_status(f"Guessed filename for {file_type_for_log} from URL: {original_filename}")

        # Basic sanitization and ensure .mcpack extension if possible
        sanitized_base, _ = os.path.splitext(sanitize_filename(original_filename))
        if not sanitized_base:
            sanitized_base = f"{file_type_for_log.lower()}_pack"  # Fallback base name
        original_filename = f"{sanitized_base}.mcpack"  # Force .mcpack extension

        if not content_bytes:
            raise ValueError(f"Downloaded {file_type_for_log} file is empty")

        update_status(f"Successfully downloaded {file_type_for_log} ({format_size(len(content_bytes))})")
        return content_bytes, original_filename, content_type

    except Exception as e:
        update_status(f"Error downloading {file_type_for_log} from {direct_download_url}: {e}")
        update_status(f"Traceback: {traceback.format_exc()}")  # Add traceback for better debugging
        return None, None, None

def upload_mod_file_task(filepath: str):
    """Task to process a local mod file, upload it, and update GUI."""
    final_url = None
    mod_bytes = None
    original_filename = os.path.basename(filepath)

    try:
        update_status(f"قراءة ملف المود: {original_filename}")
        with open(filepath, 'rb') as f:
            mod_bytes = f.read()

        if not mod_bytes:
            raise ValueError("ملف المود فارغ.")

        # التحقق من حجم الملف
        max_size_bytes = MAX_MOD_SIZE_MB * 1024 * 1024
        if len(mod_bytes) > max_size_bytes:
            raise ValueError(f"حجم ملف المود ({format_size(len(mod_bytes))}) يتجاوز الحد المسموح به ({MAX_MOD_SIZE_MB}MB).")

        update_status(f"تمت قراءة ملف المود بنجاح. الحجم: {format_size(len(mod_bytes))}")

        # تنقيح اسم الملف وتحديد نوع المحتوى وامتداده (مشابه لما في process_mod)
        sanitized_original_filename = sanitize_filename(original_filename)
        base_name, original_ext = os.path.splitext(sanitized_original_filename)
        original_ext_lower = original_ext.lower()

        final_upload_filename = sanitized_original_filename
        # محاولة تخمين نوع المحتوى من الملف، أو استخدام الافتراضي
        target_upload_content_type = mimetypes.guess_type(filepath)[0] or 'application/octet-stream'

        if original_ext_lower not in [".mcpack", ".mcaddon"]:
            if not base_name: base_name = "modfile" # اسم افتراضي إذا كان الاسم فارغًا
            final_upload_filename = base_name + ".mcaddon" # إجباره على .mcaddon
            target_upload_content_type = 'application/octet-stream' # نوع عام لـ .mcaddon المفروض
            update_status(f"الامتداد الأصلي '{original_ext_lower}' ليس .mcpack/.mcaddon. سيتم فرضه إلى .mcaddon: {final_upload_filename}")
        else:
            update_status(f"استخدام الامتداد الأصلي: {final_upload_filename}")
            # إذا كان الامتداد صحيحًا ولكن نوع المحتوى عام جدًا (مثل application/zip)، اجعله أكثر تحديدًا
            if target_upload_content_type == 'application/zip' or not target_upload_content_type.startswith('application/'):
                 target_upload_content_type = 'application/octet-stream'

        update_status(f"اسم الملف النهائي للرفع: {final_upload_filename}, نوع المحتوى للرفع: {target_upload_content_type}")

        # رفع الملف إلى Supabase
        final_url = upload_to_supabase(MOD_BUCKET, mod_bytes, final_upload_filename, target_upload_content_type)

        if final_url:
            status_msg = f"--> رابط المود المرفوع (Supabase): {final_url}"
            update_status(status_msg)
            update_mod_result(final_url) # تحديث صندوق نتائج المود

            # ملء حقل رابط تحميل المود تلقائيًا في قسم النشر
            if 'publish_mod_url_entry' in globals() and publish_mod_url_entry.winfo_exists():
                auto_populate_field(publish_mod_url_entry, final_url)
                update_status("تم ملء حقل رابط تحميل المود تلقائيًا بالملف المرفوع.")

            # ملء حقل الحجم تلقائيًا
            if 'publish_size_entry' in globals() and publish_size_entry.winfo_exists():
                formatted_size = format_size(len(mod_bytes))
                publish_size_entry.config(state=tk.NORMAL)
                auto_populate_field(publish_size_entry, formatted_size)
                publish_size_entry.config(state=tk.DISABLED)
                update_status(f"تم ملء حقل الحجم تلقائيًا: {formatted_size}")
        else:
            update_status(f"!!! فشلت معالجة ورفع ملف المود {original_filename}. لم يتم إرجاع رابط.")
            update_mod_result("فشل رفع ملف المود.")

    except ValueError as ve: # أخطاء متوقعة مثل حجم الملف أو كونه فارغًا
        update_status(f"!!! خطأ في التحقق من صحة ملف المود: {ve}")
        update_mod_result(f"خطأ: {ve}")
    except Exception as e:
        error_msg = f"!!! فشل معالجة ورفع ملف المود المحلي: {e}"
        update_status(error_msg)
        update_status(f"Traceback: {traceback.format_exc()}")
        update_mod_result(f"فشل: {e}")
    finally:
        # إعادة تفعيل زر رفع الملف
        if 'upload_mod_file_button' in globals() and upload_mod_file_button.winfo_exists():
            upload_mod_file_button.config(state=tk.NORMAL)

def save_new_api_key(new_key):
    global loaded_gemini_api_keys, current_gemini_key_index, GEMINI_CLIENT_OK
    if not new_key or not isinstance(new_key, str) or not new_key.strip():
        messagebox.showwarning("Invalid Key", "Please enter a valid API key.")
        return False
    new_key = new_key.strip()
    try:
        current_config = load_config()
        if new_key not in current_config.get("gemini_api_keys", []):
            current_config.setdefault("gemini_api_keys", []).append(new_key)
            save_config(current_config)
            loaded_gemini_api_keys = current_config.get("gemini_api_keys", [])
            if not GEMINI_CLIENT_OK and GEMINI_AVAILABLE:
                if configure_gemini_client(len(loaded_gemini_api_keys) - 1):
                    update_status("تم تكوين عميل Gemini بنجاح باستخدام المفتاح الجديد.")
                else:
                    configure_gemini_client(0)
            messagebox.showinfo("Success", "New API key saved successfully.")
            return True
        else:
            messagebox.showinfo("Duplicate Key", "This API key already exists.")
            return False
    except Exception as e:
        messagebox.showerror("Error", f"Failed to save new API key: {e}")
        return False

def open_api_key_dialog():
    new_key = simpledialog.askstring("إضافة مفتاح Gemini API", "أدخل مفتاح Gemini API الجديد:", parent=window)
    if new_key:
        save_new_api_key(new_key)

GEMINI_MODEL_NAME = "gemini-1.5-flash"
gemini_model = None
GEMINI_CLIENT_OK = False
current_config = load_config()
loaded_gemini_api_keys = current_config.get("gemini_api_keys", [])
current_gemini_key_index = 0
MAX_GEMINI_RETRIES = len(loaded_gemini_api_keys)

def configure_gemini_client(key_index):
    global gemini_model, GEMINI_CLIENT_OK, current_gemini_key_index
    if not GEMINI_AVAILABLE or not loaded_gemini_api_keys:
        GEMINI_CLIENT_OK = False
        if not loaded_gemini_api_keys:
            update_status("!!! No Gemini API keys found. AI features disabled.")
        return False
    if key_index >= len(loaded_gemini_api_keys):
        print("!!! All configured Gemini API keys failed or exhausted.")
        GEMINI_CLIENT_OK = False
        return False
    api_key = loaded_gemini_api_keys[key_index]
    try:
        genai.configure(api_key=api_key)
        gemini_model = genai.GenerativeModel(GEMINI_MODEL_NAME)
        print(f"Gemini client configured with key index {key_index}.")
        GEMINI_CLIENT_OK = True
        current_gemini_key_index = key_index
        return True
    except Exception as e:
        print(f"!!! Error initializing Gemini with key index {key_index}: {e}")
        GEMINI_CLIENT_OK = False
        return configure_gemini_client(key_index + 1)

if GEMINI_AVAILABLE:
    if loaded_gemini_api_keys:
        configure_gemini_client(0)
    else:
        print("Warning: No Gemini API keys. AI features disabled.")
else:
    print("!!! Gemini library NOT available. AI features disabled.")

def generate_unique_filename(original_filename, prefix=""):
    timestamp = int(time.time())
    random_str = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    base, ext = os.path.splitext(original_filename)
    sanitized_base = re.sub(r'[^\w.\-]+', '_', base)[:50]
    if ext and not ext.startswith('.'): ext = '.' + ext
    elif not ext: ext = '.bin'
    sanitized_base = sanitized_base.strip('.-') or "file"
    return f"{prefix}{sanitized_base}_{timestamp}_{random_str}{ext}"

def sanitize_filename(filename):
    if not filename: return "default_filename"
    filename = unicodedata.normalize('NFKD', filename).encode('ascii', 'ignore').decode('ascii')
    filename = re.sub(r'[^\w.\-]+', '_', filename)
    filename = re.sub(r'^[.\-]+|[.\-]+$', '', filename)
    filename = re.sub(r'[.\-]{2,}', '_', filename)
    filename = filename[:100]
    if not filename: filename = "sanitized_file"
    base, ext = os.path.splitext(filename)
    if not ext and base: filename += ".bin"
    elif not base and ext: filename = "file" + ext
    elif not base and not ext: filename = "default_file.bin"
    return filename

def detect_category_from_url(url: str, defined_categories: list[str]) -> str | None:
    if not url: return None
    try:
        path_lower = urlparse(url).path.lower()
    except Exception: return None
    for cat in defined_categories:
        cat_keywords = [kw.lower() for kw in cat.replace("Texture Pack", "TexturePack").split()]
        if any(f"-{kw}-" in path_lower or f"/{kw}-" in path_lower or path_lower.endswith(f"/{kw}") for kw in cat_keywords):
            return cat
    if "shader" in path_lower and "Shaders" in defined_categories: return "Shaders"
    if "addon" in path_lower and "Addons" in defined_categories: return "Addons"
    if "texture" in path_lower and "Texture Pack" in defined_categories: return "Texture Pack"
    return None

def format_size(size_bytes):
    if size_bytes < 1024: return f"{size_bytes} B"
    elif size_bytes < 1024**2: return f"{size_bytes/1024:.2f} KB"
    else: return f"{size_bytes/1024**2:.2f} MB"

def update_status(message):
    if 'status_text' in globals() and status_text.winfo_exists():
        try:
            status_text.config(state=tk.NORMAL)
            status_text.insert(tk.END, f"{time.strftime('%H:%M:%S')} - {message}\n")
            status_text.see(tk.END)
            status_text.config(state=tk.DISABLED)
        except tk.TclError: print(f"Status Update (GUI Error): {message}")
    else: print(message)

def run_in_thread(target_func, *args):
    threading.Thread(target=target_func, args=args, daemon=True).start()

def auto_populate_field(widget, value):
    if widget and widget.winfo_exists() and isinstance(widget, (ttk.Entry, tk.Entry)):
        widget.delete(0, tk.END)
        widget.insert(0, value if value else "")

def auto_populate_text_widget(widget, value, append=False):
    if widget and widget.winfo_exists() and isinstance(widget, tk.Text):
        widget.config(state=tk.NORMAL)
        if not append: widget.delete("1.0", tk.END)
        elif widget.get("1.0", tk.END).strip(): widget.insert(tk.END, "\n")
        widget.insert(tk.END, value if value else "")
        widget.see(tk.END)

def clear_widget_content(widget):
    if not widget or not widget.winfo_exists(): return
    if isinstance(widget, (ttk.Entry, tk.Entry)): widget.delete(0, tk.END)
    elif isinstance(widget, tk.Text):
        widget.config(state=tk.NORMAL); widget.delete("1.0", tk.END)

def clear_all_app_fields():
    """Clears all relevant input fields in the application GUI."""
    update_status("Clearing all application input fields...")
    widgets_to_clear = [
        scrape_url_entry if 'scrape_url_entry' in globals() else None,
        new_image_url_entry if 'new_image_url_entry' in globals() else None,
        mod_page_url_entry if 'mod_page_url_entry' in globals() else None,
        bp_url_entry if 'bp_url_entry' in globals() else None,
        rp_url_entry if 'rp_url_entry' in globals() else None,
        publish_name_entry if 'publish_name_entry' in globals() else None,
        publish_version_entry if 'publish_version_entry' in globals() else None,
        publish_primary_image_entry if 'publish_primary_image_entry' in globals() else None,
        publish_mod_url_entry if 'publish_mod_url_entry' in globals() else None,
        publish_creator_name_entry if 'publish_creator_name_entry' in globals() else None,
        publish_creator_contact_entry if 'publish_creator_contact_entry' in globals() else None,
    ]
    text_widgets_to_clear = [
        mod_features_text if 'mod_features_text' in globals() else None,
        publish_desc_text if 'publish_desc_text' in globals() else None,
        publish_arabic_desc_text if 'publish_arabic_desc_text' in globals() else None,  # إضافة الوصف العربي
        publish_other_images_text if 'publish_other_images_text' in globals() else None,
        mod_result_text if 'mod_result_text' in globals() else None,

    ]

    for widget in widgets_to_clear:
        if widget: clear_widget_content(widget)
    for widget in text_widgets_to_clear:
        if widget: clear_widget_content(widget)

    # Clear custom social sites entries
    if 'custom_site_entries' in globals():
        for entry_data in custom_site_entries:
            if entry_data['name_entry'].winfo_exists():
                entry_data['name_entry'].delete(0, tk.END)
                entry_data['name_entry'].insert(0, "اسم الموقع")
            if entry_data['url_entry'].winfo_exists():
                entry_data['url_entry'].delete(0, tk.END)
                entry_data['url_entry'].insert(0, "رابط الموقع")

    if 'category_combobox' in globals() and category_combobox.winfo_exists():
        category_combobox.set(CATEGORIES[0]) # Reset to default

    # Clear managed images
    global managed_images
    managed_images = []
    if 'image_preview_inner_frame' in globals() and image_preview_inner_frame.winfo_exists():
        display_managed_images() # Update the preview area

    # Reset Phosus enhancement selection
    if 'phosus_enhancement_var' in globals():
        phosus_enhancement_var.set("none")

    # Reset direct download checkbox
    if 'direct_download_var' in globals():
        direct_download_var.set(False)

    # Clear and disable size entry
    if 'publish_size_entry' in globals() and publish_size_entry.winfo_exists():
        publish_size_entry.config(state=tk.NORMAL)
        clear_widget_content(publish_size_entry)
        publish_size_entry.config(state=tk.DISABLED)

    update_status("All input fields cleared.")

# --- NEW: Clipboard Helper Functions ---
def paste_into_widget(widget):
    """Pastes clipboard content into the specified widget (Entry or Text)."""
    if not PYPERCLIP_AVAILABLE:
        update_status("!!! Paste failed: pyperclip library not available.")
        messagebox.showwarning("Paste Error", "pyperclip library is required for pasting.")
        return
    try:
        clipboard_content = pyperclip.paste()
        if not clipboard_content:
            update_status("Clipboard is empty.")
            return

        if isinstance(widget, (tk.Entry, ttk.Entry)):
            # Append to existing content or replace? Let's replace for simplicity.
            widget.delete(0, tk.END)
            widget.insert(0, clipboard_content)
            update_status(f"Pasted content into Entry widget.")
        elif isinstance(widget, tk.Text):
            # Append to existing content or replace? Let's append.
            widget.config(state=tk.NORMAL)
            # Add newline if there's existing content
            if widget.get("1.0", tk.END).strip():
                widget.insert(tk.END, "\n" + clipboard_content)
            else:
                widget.insert(tk.END, clipboard_content)
            widget.see(tk.END)
            # widget.config(state=tk.DISABLED) # Keep enabled for text widgets usually
            update_status(f"Pasted content into Text widget.")
        else:
            update_status(f"!!! Paste failed: Unsupported widget type {type(widget)}.")

    except Exception as e:
        update_status(f"!!! Error during paste operation: {e}")
        messagebox.showerror("Paste Error", f"An error occurred during paste: {e}")

def copy_from_widget(widget):
    """Copies the content of the specified widget (Entry or Text) to the clipboard."""
    if not PYPERCLIP_AVAILABLE:
        update_status("!!! Copy failed: pyperclip library not available.")
        messagebox.showwarning("Copy Error", "pyperclip library is required for copying.")
        return
    try:
        content_to_copy = ""
        if isinstance(widget, (tk.Entry, ttk.Entry)):
            content_to_copy = widget.get()
        elif isinstance(widget, tk.Text):
            # Ensure widget is temporarily normal to get content if it was disabled
            original_state = widget.cget("state")
            widget.config(state=tk.NORMAL)
            content_to_copy = widget.get("1.0", tk.END).strip()
            widget.config(state=original_state) # Restore original state
        elif isinstance(widget, scrolledtext.ScrolledText): # Handle ScrolledText too
             original_state = widget.cget("state")
             widget.config(state=tk.NORMAL)
             content_to_copy = widget.get("1.0", tk.END).strip()
             widget.config(state=original_state)
        else:
            update_status(f"!!! Copy failed: Unsupported widget type {type(widget)}.")
            return

        if content_to_copy:
            pyperclip.copy(content_to_copy)
            update_status("Content copied to clipboard.")
        else:
            update_status("Widget is empty, nothing to copy.")

    except Exception as e:
        update_status(f"!!! Error during copy operation: {e}")
        messagebox.showerror("Copy Error", f"An error occurred during copy: {e}")

# --- Global State for Image Management ---
managed_images = [] # List to store {'path_or_url': str, 'type': 'url' | 'local', 'tk_image': ImageTk.PhotoImage, 'frame': tk.Widget, 'label': tk.Widget, 'button': tk.Widget, 'original_size': int, 'compressed_size': int}
curseforge_image_urls = [] # List of image URLs extracted from CurseForge
compression_level = "normal" # Default compression level: "normal", "medium", "high"
preview_window = None # Global variable to store the preview window
preview_images = [] # List to store preview images

def add_image_to_managed_list(path_or_url, image_type):
    """Adds an image (URL or local path) to the managed list and updates display."""
    if not path_or_url:
        update_status("!!! مسار الصورة أو الرابط فارغ.")
        return
    if len(managed_images) >= 20:
        messagebox.showwarning("الحد الأقصى للصور", "يمكنك إضافة 20 صورة كحد أقصى.")
        return

    # Prevent duplicates
    if any(item['path_or_url'] == path_or_url for item in managed_images):
        update_status(f"الصورة '{os.path.basename(path_or_url) if image_type == 'local' else path_or_url}' موجودة بالفعل.")
        return

    managed_images.append({'path_or_url': path_or_url, 'type': image_type, 'tk_image': None, 'frame': None, 'label': None, 'button': None})
    display_managed_images()
    update_status(f"تمت إضافة الصورة: {os.path.basename(path_or_url) if image_type == 'local' else path_or_url}")

def handle_add_image_url():
    url = new_image_url_entry.get().strip()
    if url:
        add_image_to_managed_list(url, 'url')
        new_image_url_entry.delete(0, tk.END)
    else:
        messagebox.showwarning("إدخال مطلوب", "الرجاء إدخال رابط الصورة.")

def handle_add_local_image():
    filepath = filedialog.askopenfilename(
        title="اختر صورة من القرص",
        filetypes=[("Image files", "*.png *.jpg *.jpeg *.webp *.gif"), ("All files", "*.*")]
    )
    if filepath:
        add_image_to_managed_list(filepath, 'local')

def delete_selected_managed_images():
    """يحذف الصور المحددة (التي تم تحديد مربع الاختيار بجانبها) من القائمة المدارة."""
    global managed_images
    if not managed_images:
        messagebox.showinfo("لا صور", "لا توجد صور لحذفها.")
        return

    # Collect items to keep (those NOT selected for deletion)
    images_to_keep = [item for item in managed_images if not item['selected_var'].get()]

    if len(images_to_keep) == len(managed_images):
        messagebox.showinfo("لا يوجد تحديد", "الرجاء تحديد الصور التي تريد حذفها أولاً.")
        return

    # Ask for confirmation before deleting
    num_to_delete = len(managed_images) - len(images_to_keep)
    if not messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف {num_to_delete} صورة محددة؟"):
        return

    # Update the managed_images list
    managed_images = images_to_keep

    # Refresh the display
    display_managed_images()
    update_status(f"تم حذف {num_to_delete} صورة محددة.")

# --- NEW: MCPEDL Scraper Functions ---
def handle_mcpedl_extraction():
    """معالجة زر استخراج البيانات من MCPEDL"""
    if not MCPEDL_SCRAPER_AVAILABLE:
        messagebox.showerror("خطأ", "وحدة استخراج MCPEDL غير متوفرة. تأكد من وجود ملف mcpedl_scraper_module.py")
        return

    url = mcpedl_url_entry.get().strip()
    if not url:
        messagebox.showwarning("إدخال مطلوب", "الرجاء إدخال رابط صفحة المود من MCPEDL")
        return

    # التحقق من صحة الرابط
    scraper = MCPEDLScraper()
    if not scraper.is_valid_mcpedl_url(url):
        messagebox.showerror("رابط غير صحيح", "الرابط يجب أن يكون من موقع mcpedl.com")
        scraper.close()
        return

    scraper.close()

    # تعطيل الزر أثناء المعالجة
    if 'mcpedl_extract_button' in globals() and mcpedl_extract_button.winfo_exists():
        mcpedl_extract_button.config(state=tk.DISABLED)

    update_status(f"بدء استخراج البيانات من MCPEDL: {url}")
    run_in_thread(mcpedl_extraction_task, url)

def mcpedl_extraction_task(url: str):
    """مهمة استخراج البيانات من MCPEDL في خيط منفصل"""
    try:
        update_status("جاري استخراج البيانات من MCPEDL...")

        # استخراج البيانات
        mod_data = scrape_mcpedl_mod(url)

        if not mod_data:
            update_status("!!! فشل في استخراج البيانات من MCPEDL")
            messagebox.showerror("خطأ", "فشل في استخراج البيانات من الصفحة. تأكد من صحة الرابط.")
            return

        update_status(f"تم استخراج البيانات بنجاح: {mod_data.get('name', 'Unknown')}")

        # ملء الحقول تلقائياً
        populate_fields_from_mcpedl_data(mod_data)

        update_status("تم ملء الحقول تلقائياً من بيانات MCPEDL")

    except Exception as e:
        update_status(f"!!! خطأ في استخراج البيانات من MCPEDL: {e}")
        messagebox.showerror("خطأ", f"حدث خطأ أثناء استخراج البيانات: {e}")

    finally:
        # إعادة تفعيل الزر
        if 'mcpedl_extract_button' in globals() and mcpedl_extract_button.winfo_exists():
            mcpedl_extract_button.config(state=tk.NORMAL)

def populate_fields_from_mcpedl_data(mod_data: Dict[str, Any]):
    """ملء حقول الواجهة من بيانات MCPEDL المستخرجة"""
    try:
        # ملء الاسم
        if mod_data.get('name') and 'publish_name_entry' in globals():
            auto_populate_field(publish_name_entry, mod_data['name'])

        # ملء الوصف
        if mod_data.get('description') and 'publish_desc_text' in globals():
            auto_populate_text_widget(publish_desc_text, mod_data['description'])

        # ملء الفئة
        if mod_data.get('category') and 'category_combobox' in globals():
            # تحويل الفئة إلى فئة متوافقة
            mcpedl_category = mod_data['category']
            mapped_category = map_mcpedl_category_to_app_category(mcpedl_category)
            if mapped_category in CATEGORIES:
                category_combobox.set(mapped_category)

        # ملء الإصدار
        if mod_data.get('version') and 'publish_version_entry' in globals():
            auto_populate_field(publish_version_entry, mod_data['version'])

        # ملء الحجم
        if mod_data.get('size') and 'publish_size_entry' in globals():
            publish_size_entry.config(state=tk.NORMAL)
            auto_populate_field(publish_size_entry, mod_data['size'])
            publish_size_entry.config(state=tk.DISABLED)

        # ملء رابط التحميل
        if mod_data.get('download_url') and 'publish_mod_url_entry' in globals():
            auto_populate_field(publish_mod_url_entry, mod_data['download_url'])

        # ملء معلومات المطور
        if mod_data.get('creator_name') and 'publish_creator_name_entry' in globals():
            auto_populate_field(publish_creator_name_entry, mod_data['creator_name'])

        # ملء معلومات التواصل
        if mod_data.get('creator_contact_info') and 'publish_creator_contact_entry' in globals():
            auto_populate_field(publish_creator_contact_entry, mod_data['creator_contact_info'])

        # ملء الصور
        if mod_data.get('image_urls') and isinstance(mod_data['image_urls'], list):
            # إضافة الصور إلى القائمة المدارة
            for image_url in mod_data['image_urls'][:10]:  # حد أقصى 10 صور
                add_image_to_managed_list(image_url, 'url')

            # تعيين الصورة الأولى كصورة رئيسية
            if mod_data['image_urls'] and 'publish_primary_image_entry' in globals():
                auto_populate_field(publish_primary_image_entry, mod_data['image_urls'][0])

        # ملء قنوات التواصل الاجتماعي
        if mod_data.get('creator_social_channels') and isinstance(mod_data['creator_social_channels'], list):
            populate_social_channels_from_mcpedl(mod_data['creator_social_channels'])

        update_status("تم ملء جميع الحقول المتاحة من بيانات MCPEDL")

    except Exception as e:
        update_status(f"!!! خطأ في ملء الحقول: {e}")

def map_mcpedl_category_to_app_category(mcpedl_category: str) -> str:
    """تحويل فئة MCPEDL إلى فئة متوافقة مع التطبيق"""
    if not mcpedl_category:
        return "Addons"

    mcpedl_category_lower = mcpedl_category.lower()

    if any(keyword in mcpedl_category_lower for keyword in ['addon', 'mod', 'behavior', 'behaviour']):
        return "Addons"
    elif any(keyword in mcpedl_category_lower for keyword in ['shader', 'shaders']):
        return "Shaders"
    elif any(keyword in mcpedl_category_lower for keyword in ['texture', 'resource', 'pack']):
        return "Texture Pack"
    else:
        return "Addons"  # افتراضي

def populate_social_channels_from_mcpedl(social_channels: List[str]):
    """ملء قنوات التواصل الاجتماعي من بيانات MCPEDL"""
    try:
        if not social_channels or 'custom_site_entries' not in globals():
            return

        # إضافة مواقع جديدة حسب الحاجة
        current_entries_count = len(custom_site_entries)
        needed_entries = len(social_channels)

        # إضافة مواقع إضافية إذا لزم الأمر
        for i in range(needed_entries - current_entries_count):
            add_custom_site_entry()

        # ملء المواقع الموجودة
        for i, channel in enumerate(social_channels[:len(custom_site_entries)]):
            if ':' in channel:
                platform, url = channel.split(':', 1)
                platform = platform.strip()
                url = url.strip()

                # ملء اسم الموقع
                if custom_site_entries[i]['name_entry'].winfo_exists():
                    custom_site_entries[i]['name_entry'].delete(0, tk.END)
                    custom_site_entries[i]['name_entry'].insert(0, platform)

                # ملء رابط الموقع
                if custom_site_entries[i]['url_entry'].winfo_exists():
                    custom_site_entries[i]['url_entry'].delete(0, tk.END)
                    custom_site_entries[i]['url_entry'].insert(0, url)

        update_status(f"تم ملء {len(social_channels)} قناة تواصل اجتماعي")

    except Exception as e:
        update_status(f"!!! خطأ في ملء قنوات التواصل: {e}")

def clear_mcpedl_fields():
    """مسح حقول MCPEDL"""
    if 'mcpedl_url_entry' in globals() and mcpedl_url_entry.winfo_exists():
        mcpedl_url_entry.delete(0, tk.END)
    update_status("تم مسح حقول MCPEDL")

def add_custom_site_entry():
    """إضافة موقع تواصل اجتماعي جديد"""
    if 'add_custom_site_row' in globals():
        add_custom_site_row()

def auto_populate_field(widget, value):
    """ملء حقل نص تلقائياً"""
    try:
        if widget and widget.winfo_exists() and value:
            widget.delete(0, tk.END)
            widget.insert(0, str(value))
    except Exception:
        pass

def auto_populate_text_widget(widget, value):
    """ملء حقل نص متعدد الأسطر تلقائياً"""
    try:
        if widget and widget.winfo_exists() and value:
            widget.delete(1.0, tk.END)
            widget.insert(1.0, str(value))
    except Exception:
        pass

def remove_managed_image(image_item_to_remove):
    global managed_images
    managed_images = [item for item in managed_images if item['path_or_url'] != image_item_to_remove['path_or_url']]
    display_managed_images()
    update_status(f"تمت إزالة الصورة: {os.path.basename(image_item_to_remove['path_or_url']) if image_item_to_remove['type'] == 'local' else image_item_to_remove['path_or_url']}")

def clear_all_managed_images():
    """حذف جميع الصور من القائمة المدارة"""
    global managed_images
    if not managed_images:
        return

    if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف جميع الصور؟"):
        managed_images = []
    display_managed_images()
    update_status(f"تم حذف جميع الصور.")

def delete_selected_managed_images():
    """يحذف الصور المحددة (التي تم تحديد مربع الاختيار بجانبها) من القائمة المدارة."""
    global managed_images
    if not managed_images:
        messagebox.showinfo("لا صور", "لا توجد صور لحذفها.")
        return

    # Collect items to keep (those NOT selected for deletion)
    images_to_keep = [item for item in managed_images if not item['selected_var'].get()]

    if len(images_to_keep) == len(managed_images):
        messagebox.showinfo("لا يوجد تحديد", "الرجاء تحديد الصور التي تريد حذفها أولاً.")
        return

    # Ask for confirmation before deleting
    num_to_delete = len(managed_images) - len(images_to_keep)
    if not messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف {num_to_delete} صورة محددة؟"):
        return

    # Update the managed_images list
    managed_images = images_to_keep

    # Refresh the display
    display_managed_images()
    update_status(f"تم حذف {num_to_delete} صورة محددة.")


def move_image_up(index):
    """تحريك الصورة للأعلى في القائمة"""
    global managed_images
    if index <= 0 or index >= len(managed_images):
        return

    # Collect items to keep (those NOT selected for deletion)
    images_to_keep = [item for item in managed_images if not item['selected_var'].get()]

    if len(images_to_keep) == len(managed_images):
        messagebox.showinfo("لا يوجد تحديد", "الرجاء تحديد الصور التي تريد حذفها أولاً.")
        return

    # Ask for confirmation before deleting
    num_to_delete = len(managed_images) - len(images_to_keep)
    if not messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف {num_to_delete} صورة محددة؟"):
        return

    # Update the managed_images list
    managed_images = images_to_keep

    # Refresh the display
    display_managed_images()
    update_status(f"تم حذف {num_to_delete} صورة محددة.")


def move_image_up(index):
    """تحريك الصورة للأعلى في القائمة"""
    global managed_images
    if index <= 0 or index >= len(managed_images):
        return

    managed_images[index], managed_images[index-1] = managed_images[index-1], managed_images[index]
    display_managed_images()
    update_status(f"تم تحريك الصورة للأعلى.")

def move_image_down(index):
    """تحريك الصورة للأسفل في القائمة"""
    global managed_images
    if index < 0 or index >= len(managed_images) - 1:
        return

    managed_images[index], managed_images[index+1] = managed_images[index+1], managed_images[index]
    display_managed_images()
    update_status(f"تم تحريك الصورة للأسفل.")

def change_image_url(index):
    """تغيير رابط الصورة مع الحفاظ على موقعها في القائمة"""
    global managed_images
    if index < 0 or index >= len(managed_images):
        return

    # إنشاء نافذة منبثقة لإدخال الرابط الجديد
    change_url_dialog = tk.Toplevel()
    change_url_dialog.title("تغيير رابط الصورة")
    change_url_dialog.geometry("500x150")
    change_url_dialog.resizable(False, False)

    # إضافة عنوان
    ttk.Label(change_url_dialog, text="أدخل رابط الصورة الجديد:").pack(pady=(10, 5))

    # إضافة حقل إدخال الرابط
    new_url_entry = ttk.Entry(change_url_dialog, width=70)
    new_url_entry.pack(pady=5, padx=10, fill="x")

    # إضافة الرابط الحالي كقيمة افتراضية
    current_url = managed_images[index]['path_or_url']
    new_url_entry.insert(0, current_url)
    new_url_entry.select_range(0, tk.END)
    new_url_entry.focus_set()

    # إطار للأزرار
    buttons_frame = ttk.Frame(change_url_dialog)
    buttons_frame.pack(pady=10, fill="x")

    # دالة لتغيير الرابط
    def apply_url_change():
        new_url = new_url_entry.get().strip()
        if not new_url:
            messagebox.showwarning("خطأ", "الرابط لا يمكن أن يكون فارغاً", parent=change_url_dialog)
            return

        if new_url == current_url:
            change_url_dialog.destroy()
            return

        # حفظ البيانات المهمة من العنصر القديم
        old_item = managed_images[index]

        # إنشاء عنصر جديد بنفس البيانات ولكن برابط مختلف
        managed_images[index] = {
            'path_or_url': new_url,
            'type': 'url',  # نفترض أن الرابط الجديد هو URL
            'tk_image': None,  # سيتم إعادة إنشاؤها عند عرض الصور
            'frame': None,
            'label': None,
            'button': None
        }

        # تحديث العرض
        display_managed_images()
        update_status(f"تم تغيير رابط الصورة من: {current_url} إلى: {new_url}")
        change_url_dialog.destroy()

    # زر تطبيق التغيير
    apply_button = ttk.Button(buttons_frame, text="تطبيق", command=apply_url_change)
    apply_button.pack(side=tk.RIGHT, padx=5)

    # زر إلغاء
    cancel_button = ttk.Button(buttons_frame, text="إلغاء", command=change_url_dialog.destroy)
    cancel_button.pack(side=tk.RIGHT, padx=5)

    # جعل النافذة مركزية
    change_url_dialog.update_idletasks()
    width = change_url_dialog.winfo_width()
    height = change_url_dialog.winfo_height()
    x = (change_url_dialog.winfo_screenwidth() // 2) - (width // 2)
    y = (change_url_dialog.winfo_screenheight() // 2) - (height // 2)
    change_url_dialog.geometry('{}x{}+{}+{}'.format(width, height, x, y))

    # جعل النافذة المنبثقة مركز الاهتمام
    change_url_dialog.transient(window)
    change_url_dialog.grab_set()
    window.wait_window(change_url_dialog)

def delete_selected_managed_images():
    """يحذف الصور المحددة (التي تم تحديد مربع الاختيار بجانبها) من القائمة المدارة."""
    global managed_images
    if not managed_images:
        messagebox.showinfo("لا صور", "لا توجد صور لحذفها.")
        return

    # Collect items to keep (those NOT selected for deletion)
    images_to_keep = [item for item in managed_images if not item['selected_var'].get()]

    if len(images_to_keep) == len(managed_images):
        messagebox.showinfo("لا يوجد تحديد", "الرجاء تحديد الصور التي تريد حذفها أولاً.")
        return

    # Ask for confirmation before deleting
    num_to_delete = len(managed_images) - len(images_to_keep)
    if not messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف {num_to_delete} صورة محددة؟"):
        return

    # Update the managed_images list
    managed_images = images_to_keep

    # Refresh the display
    display_managed_images()
    update_status(f"تم حذف {num_to_delete} صورة محددة.")


def compress_images_task(all_images=True):
    """Compress images based on the selected compression level.

    Args:
        all_images: If True, compress all images. If False, compress only selected images.
    """
    global managed_images, preview_window, preview_images

    # Check if there are any images to compress
    if not managed_images:
        messagebox.showinfo("لا صور", "لا توجد صور لضغطها.")
        return

    # Get images to compress
    images_to_compress = []
    if all_images:
        images_to_compress = managed_images
    else:
        # Get only selected images
        images_to_compress = [item for item in managed_images if item.get('selected_var', tk.BooleanVar()).get()]

        if not images_to_compress:
            messagebox.showinfo("لا يوجد تحديد", "الرجاء تحديد الصور التي تريد ضغطها أولاً.")
            return

    # Check for animated GIFs and inform user
    has_animated_gifs = False
    for item in images_to_compress:
        if item.get('is_animated', False):
            has_animated_gifs = True
            break

    if has_animated_gifs:
        update_status("تم اكتشاف صور GIF متحركة. سيتم الحفاظ على الحركة أثناء الضغط.")

    # Create preview window
    if preview_window and preview_window.winfo_exists():
        preview_window.destroy()  # Close existing preview window

    preview_window = tk.Toplevel()
    preview_window.title("معاينة الصور بعد الضغط")
    preview_window.geometry("800x600")

    # Create scrollable frame for preview images
    preview_canvas = tk.Canvas(preview_window)
    preview_scrollbar = ttk.Scrollbar(preview_window, orient="vertical", command=preview_canvas.yview)
    preview_scrollable_frame = ttk.Frame(preview_canvas)

    preview_scrollable_frame.bind(
        "<Configure>",
        lambda e: preview_canvas.configure(scrollregion=preview_canvas.bbox("all"))
    )

    preview_canvas.create_window((0, 0), window=preview_scrollable_frame, anchor="nw")
    preview_canvas.configure(yscrollcommand=preview_scrollbar.set)

    preview_canvas.pack(side="left", fill="both", expand=True)
    preview_scrollbar.pack(side="right", fill="y")

    # Add control buttons at the top
    control_frame = ttk.Frame(preview_scrollable_frame)
    control_frame.pack(fill="x", padx=10, pady=10)

    ttk.Label(control_frame, text=f"معاينة الصور بعد الضغط (المستوى: {compression_level})").pack(side="left", padx=5)

    # Add button to apply compression
    apply_button = ttk.Button(
        control_frame,
        text="تطبيق الضغط",
        command=lambda: apply_compression(images_to_compress)
    )
    apply_button.pack(side="right", padx=5)

    # Add button to cancel
    cancel_button = ttk.Button(
        control_frame,
        text="إلغاء",
        command=preview_window.destroy
    )
    cancel_button.pack(side="right", padx=5)

    # Process each image and show preview
    preview_images = []  # Clear previous preview images

    # Create a progress bar
    progress_frame = ttk.Frame(preview_scrollable_frame)
    progress_frame.pack(fill="x", padx=10, pady=5)

    progress_label = ttk.Label(progress_frame, text="جاري معالجة الصور...")
    progress_label.pack(side="left", padx=5)

    progress_bar = ttk.Progressbar(progress_frame, orient="horizontal", length=300, mode="determinate")
    progress_bar.pack(side="left", padx=5, fill="x", expand=True)

    # Update the progress bar
    total_images = len(images_to_compress)
    progress_bar["maximum"] = total_images

    # Process images in a separate thread to avoid freezing the UI
    def process_images_for_preview():
        for i, image_item in enumerate(images_to_compress):
            try:
                # Update progress
                progress_bar["value"] = i + 1
                progress_label.config(text=f"معالجة الصورة {i+1}/{total_images}...")
                preview_window.update_idletasks()

                # Get image data
                image_data = None
                if image_item['type'] == 'url':
                    # Download image from URL
                    headers = {'User-Agent': 'Mozilla/5.0'}
                    response = requests.get(image_item['path_or_url'], headers=headers, timeout=30)
                    response.raise_for_status()
                    image_data = response.content
                elif image_item['type'] == 'local':
                    # Read image from local path
                    with open(image_item['path_or_url'], 'rb') as f:
                        image_data = f.read()

                if not image_data:
                    continue

                # Open image with PIL
                img = Image.open(BytesIO(image_data))

                # Convert image to RGB if needed
                if img.mode in ("RGBA", "P", "LA"):
                    bg = Image.new("RGB", img.size, (255, 255, 255))
                    try:
                        if 'A' in img.mode: bg.paste(img, mask=img.split()[-1])
                        else: bg.paste(img)
                        img = bg
                    except:
                        img = img.convert("RGB")

                # Check if it's a GIF
                is_gif = False
                is_animated_gif = False
                if img.format == 'GIF' or (image_item['path_or_url'].lower().endswith('.gif')):
                    is_gif = True
                    # Check if it's animated
                    if img.format == 'GIF' and getattr(img, 'is_animated', False):
                        is_animated_gif = True
                        update_status(f"تم اكتشاف GIF متحرك بـ {img.n_frames} إطار")
                        # Store animation info in the image_item for future reference
                        for i, managed_item in enumerate(managed_images):
                            if managed_item['path_or_url'] == image_item['path_or_url']:
                                managed_images[i]['is_animated'] = True
                                break

                # Compress image
                compressed_bytes, img_format, _ = compress_image(img, compression_level, image_data if is_gif else None)

                # Create preview frame
                preview_frame = ttk.Frame(preview_scrollable_frame, borderwidth=1, relief="solid")
                preview_frame.pack(padx=10, pady=10, fill="x")

                # Show original and compressed images side by side
                images_frame = ttk.Frame(preview_frame)
                images_frame.pack(padx=5, pady=5)

                # Original image
                original_frame = ttk.LabelFrame(images_frame, text="الصورة الأصلية")
                original_frame.pack(side="left", padx=10, pady=5)

                # Check if it's a GIF
                is_animated_gif = False
                if img.format == 'GIF' and getattr(img, 'is_animated', False):
                    is_animated_gif = True
                    update_status(f"Displaying animated GIF with {img.n_frames} frames")

                # For animated GIFs, we need special handling
                if is_animated_gif:
                    # Create a label to show GIF info
                    ttk.Label(original_frame, text=f"GIF متحرك ({img.n_frames} إطار)").pack(pady=2)

                    # Show first frame as a static preview
                    img.seek(0)  # Go to first frame
                    first_frame = img.copy()
                    first_frame.thumbnail((200, 200))
                    tk_img_original = ImageTk.PhotoImage(first_frame)

                    original_label = ttk.Label(original_frame, image=tk_img_original)
                    original_label.image = tk_img_original  # Keep a reference
                    original_label.pack(padx=5, pady=5)

                    # Add note about animation
                    ttk.Label(original_frame, text="(معاينة للإطار الأول فقط)").pack(pady=2)
                else:
                    # Regular image handling
                    img.thumbnail((200, 200))
                    tk_img_original = ImageTk.PhotoImage(img)

                    original_label = ttk.Label(original_frame, image=tk_img_original)
                    original_label.image = tk_img_original  # Keep a reference
                    original_label.pack(padx=5, pady=5)

                # Original size
                original_size = len(image_data)
                ttk.Label(original_frame, text=f"الحجم: {format_size(original_size)}").pack(pady=2)

                # Compressed image
                compressed_frame = ttk.LabelFrame(images_frame, text="الصورة بعد الضغط")
                compressed_frame.pack(side="left", padx=10, pady=5)

                # Open compressed image
                compressed_img = Image.open(BytesIO(compressed_bytes))

                # For animated GIFs, handle similarly
                if is_animated_gif and getattr(compressed_img, 'is_animated', False):
                    # Create a label to show GIF info
                    ttk.Label(compressed_frame, text=f"GIF متحرك ({compressed_img.n_frames} إطار)").pack(pady=2)

                    # Show first frame as a static preview
                    compressed_img.seek(0)  # Go to first frame
                    first_frame = compressed_img.copy()
                    first_frame.thumbnail((200, 200))
                    tk_img_compressed = ImageTk.PhotoImage(first_frame)

                    compressed_label = ttk.Label(compressed_frame, image=tk_img_compressed)
                    compressed_label.image = tk_img_compressed  # Keep a reference
                    compressed_label.pack(padx=5, pady=5)

                    # Add note about animation
                    ttk.Label(compressed_frame, text="(معاينة للإطار الأول فقط)").pack(pady=2)
                else:
                    # Regular image handling
                    compressed_img.thumbnail((200, 200))
                    tk_img_compressed = ImageTk.PhotoImage(compressed_img)

                    compressed_label = ttk.Label(compressed_frame, image=tk_img_compressed)
                    compressed_label.image = tk_img_compressed  # Keep a reference
                    compressed_label.pack(padx=5, pady=5)

                # Compressed size and ratio
                compressed_size = len(compressed_bytes)
                compression_ratio = (1 - (compressed_size / original_size)) * 100 if original_size > 0 else 0

                ttk.Label(compressed_frame, text=f"الحجم: {format_size(compressed_size)}").pack(pady=2)
                ttk.Label(compressed_frame, text=f"نسبة الضغط: {compression_ratio:.1f}%").pack(pady=2)

                # Store preview data
                preview_images.append({
                    'index': i,
                    'item': image_item,
                    'original_size': original_size,
                    'compressed_size': compressed_size,
                    'compressed_bytes': compressed_bytes,
                    'img_format': img_format,
                    'tk_img_original': tk_img_original,
                    'tk_img_compressed': tk_img_compressed
                })

            except Exception as e:
                # Show error message
                error_frame = ttk.Frame(preview_scrollable_frame, borderwidth=1, relief="solid")
                error_frame.pack(padx=10, pady=10, fill="x")

                ttk.Label(error_frame, text=f"خطأ في معالجة الصورة: {image_item['path_or_url']}").pack(pady=2)
                ttk.Label(error_frame, text=f"الخطأ: {e}").pack(pady=2)

                update_status(f"!!! خطأ في معالجة الصورة {image_item['path_or_url']}: {e}")

        # Hide progress bar when done
        progress_frame.pack_forget()

    # Start processing in a separate thread
    threading.Thread(target=process_images_for_preview, daemon=True).start()

def apply_compression(images_to_compress):
    """Apply compression to the selected images."""
    global managed_images, preview_window, preview_images

    if not preview_images:
        messagebox.showinfo("لا توجد معاينة", "لم يتم معالجة أي صور للمعاينة.")
        return

    # Ask for confirmation
    if not messagebox.askyesno("تأكيد", f"هل أنت متأكد من تطبيق الضغط على {len(preview_images)} صورة؟"):
        return

    # Apply compression to each image
    for preview_data in preview_images:
        index = preview_data['index']
        item = preview_data['item']
        compressed_bytes = preview_data['compressed_bytes']
        img_format = preview_data['img_format']

        # Update the managed_images list with compression information
        for i, managed_item in enumerate(managed_images):
            if managed_item['path_or_url'] == item['path_or_url']:
                managed_images[i]['original_size'] = preview_data['original_size']
                managed_images[i]['compressed_size'] = preview_data['compressed_size']
                managed_images[i]['compressed_bytes'] = compressed_bytes
                managed_images[i]['compressed_format'] = img_format
                break

    # Close preview window
    if preview_window and preview_window.winfo_exists():
        preview_window.destroy()

    # Update the display
    display_managed_images()

    # Show success message
    update_status(f"تم تطبيق الضغط على {len(preview_images)} صورة بنجاح.")
    messagebox.showinfo("تم", f"تم تطبيق الضغط على {len(preview_images)} صورة بنجاح.")

def enhance_images_task(all_images=True):
    """Enhance image quality.

    Args:
        all_images: If True, enhance all images. If False, enhance only selected images.
    """
    global managed_images

    # Check if there are any images to enhance
    if not managed_images:
        messagebox.showinfo("لا صور", "لا توجد صور لتحسين جودتها.")
        return

    # Get images to enhance
    images_to_enhance = []
    if all_images:
        images_to_enhance = managed_images
    else:
        # Get only selected images
        images_to_enhance = [item for item in managed_images if item.get('selected_var', tk.BooleanVar()).get()]

        if not images_to_enhance:
            messagebox.showinfo("لا يوجد تحديد", "الرجاء تحديد الصور التي تريد تحسين جودتها أولاً.")
            return

    # Check if Phosus API is available
    if 'phosus_enhancement_var' not in globals():
        messagebox.showinfo("غير متاح", "ميزة تحسين الجودة غير متاحة حاليًا.")
        return

    # Create dialog to select enhancement type
    enhancement_dialog = tk.Toplevel()
    enhancement_dialog.title("اختر نوع التحسين")
    enhancement_dialog.geometry("400x300")
    enhancement_dialog.transient(window)
    enhancement_dialog.grab_set()

    # Add enhancement options
    ttk.Label(enhancement_dialog, text="اختر نوع التحسين:").pack(padx=10, pady=10)

    enhancement_var = tk.StringVar(value="super-resolution")

    ttk.Radiobutton(
        enhancement_dialog,
        text="تحسين الدقة (Super Resolution)",
        variable=enhancement_var,
        value="super-resolution"
    ).pack(anchor="w", padx=20, pady=5)

    ttk.Radiobutton(
        enhancement_dialog,
        text="إزالة الضوضاء (Denoise)",
        variable=enhancement_var,
        value="denoise"
    ).pack(anchor="w", padx=20, pady=5)

    ttk.Radiobutton(
        enhancement_dialog,
        text="تحسين الألوان (Color Enhancement)",
        variable=enhancement_var,
        value="color-enhancement"
    ).pack(anchor="w", padx=20, pady=5)

    # Add buttons
    buttons_frame = ttk.Frame(enhancement_dialog)
    buttons_frame.pack(pady=20)

    def start_enhancement():
        enhancement_type = enhancement_var.get()
        enhancement_dialog.destroy()

        # Set the enhancement type
        phosus_enhancement_var.set(enhancement_type)

        # Show preview window
        show_enhancement_preview(images_to_enhance, enhancement_type)

    ttk.Button(
        buttons_frame,
        text="تحسين",
        command=start_enhancement
    ).pack(side="left", padx=10)

    ttk.Button(
        buttons_frame,
        text="إلغاء",
        command=enhancement_dialog.destroy
    ).pack(side="left", padx=10)

def show_enhancement_preview(images_to_enhance, enhancement_type):
    """Show preview of enhanced images."""
    global preview_window, preview_images

    # Create preview window
    if preview_window and preview_window.winfo_exists():
        preview_window.destroy()  # Close existing preview window

    preview_window = tk.Toplevel()
    preview_window.title(f"معاينة الصور بعد تحسين الجودة ({enhancement_type})")
    preview_window.geometry("800x600")

    # Create scrollable frame for preview images
    preview_canvas = tk.Canvas(preview_window)
    preview_scrollbar = ttk.Scrollbar(preview_window, orient="vertical", command=preview_canvas.yview)
    preview_scrollable_frame = ttk.Frame(preview_canvas)

    preview_scrollable_frame.bind(
        "<Configure>",
        lambda e: preview_canvas.configure(scrollregion=preview_canvas.bbox("all"))
    )

    preview_canvas.create_window((0, 0), window=preview_scrollable_frame, anchor="nw")
    preview_canvas.configure(yscrollcommand=preview_scrollbar.set)

    preview_canvas.pack(side="left", fill="both", expand=True)
    preview_scrollbar.pack(side="right", fill="y")

    # Add control buttons at the top
    control_frame = ttk.Frame(preview_scrollable_frame)
    control_frame.pack(fill="x", padx=10, pady=10)

    ttk.Label(control_frame, text=f"معاينة الصور بعد تحسين الجودة ({enhancement_type})").pack(side="left", padx=5)

    # Add button to apply enhancement
    apply_button = ttk.Button(
        control_frame,
        text="تطبيق التحسين",
        command=lambda: apply_enhancement(images_to_enhance, enhancement_type)
    )
    apply_button.pack(side="right", padx=5)

    # Add button to cancel
    cancel_button = ttk.Button(
        control_frame,
        text="إلغاء",
        command=preview_window.destroy
    )
    cancel_button.pack(side="right", padx=5)

    # Process each image and show preview
    preview_images = []  # Clear previous preview images

    # Create a progress bar
    progress_frame = ttk.Frame(preview_scrollable_frame)
    progress_frame.pack(fill="x", padx=10, pady=5)

    progress_label = ttk.Label(progress_frame, text="جاري معالجة الصور...")
    progress_label.pack(side="left", padx=5)

    progress_bar = ttk.Progressbar(progress_frame, orient="horizontal", length=300, mode="determinate")
    progress_bar.pack(side="left", padx=5, fill="x", expand=True)

    # Update the progress bar
    total_images = len(images_to_enhance)
    progress_bar["maximum"] = total_images

    # Process images in a separate thread to avoid freezing the UI
    def process_images_for_enhancement():
        for i, image_item in enumerate(images_to_enhance):
            try:
                # Update progress
                progress_bar["value"] = i + 1
                progress_label.config(text=f"معالجة الصورة {i+1}/{total_images}...")
                preview_window.update_idletasks()

                # Get image data
                image_data = None
                content_type = None
                if image_item['type'] == 'url':
                    # Download image from URL
                    headers = {'User-Agent': 'Mozilla/5.0'}
                    response = requests.get(image_item['path_or_url'], headers=headers, timeout=30)
                    response.raise_for_status()
                    image_data = response.content
                    content_type = response.headers.get('content-type', 'image/jpeg')
                elif image_item['type'] == 'local':
                    # Read image from local path
                    with open(image_item['path_or_url'], 'rb') as f:
                        image_data = f.read()
                    content_type = mimetypes.guess_type(image_item['path_or_url'])[0] or 'image/jpeg'

                if not image_data:
                    continue

                # Open image with PIL for display
                img = Image.open(BytesIO(image_data))

                # Call Phosus API for enhancement
                enhanced_bytes = call_phosus_api(enhancement_type, image_data, content_type)

                if not enhanced_bytes:
                    # If enhancement failed, show error
                    error_frame = ttk.Frame(preview_scrollable_frame, borderwidth=1, relief="solid")
                    error_frame.pack(padx=10, pady=10, fill="x")

                    ttk.Label(error_frame, text=f"فشل تحسين الصورة: {image_item['path_or_url']}").pack(pady=2)
                    ttk.Label(error_frame, text="لم يتم استلام بيانات من API.").pack(pady=2)

                    update_status(f"!!! فشل تحسين الصورة {image_item['path_or_url']}: لم يتم استلام بيانات من API.")
                    continue

                # Create preview frame
                preview_frame = ttk.Frame(preview_scrollable_frame, borderwidth=1, relief="solid")
                preview_frame.pack(padx=10, pady=10, fill="x")

                # Show original and enhanced images side by side
                images_frame = ttk.Frame(preview_frame)
                images_frame.pack(padx=5, pady=5)

                # Original image
                original_frame = ttk.LabelFrame(images_frame, text="الصورة الأصلية")
                original_frame.pack(side="left", padx=10, pady=5)

                # Resize image for display
                img.thumbnail((200, 200))
                tk_img_original = ImageTk.PhotoImage(img)

                original_label = ttk.Label(original_frame, image=tk_img_original)
                original_label.image = tk_img_original  # Keep a reference
                original_label.pack(padx=5, pady=5)

                # Original size
                original_size = len(image_data)
                ttk.Label(original_frame, text=f"الحجم: {format_size(original_size)}").pack(pady=2)

                # Enhanced image
                enhanced_frame = ttk.LabelFrame(images_frame, text="الصورة بعد التحسين")
                enhanced_frame.pack(side="left", padx=10, pady=5)

                # Open enhanced image
                enhanced_img = Image.open(BytesIO(enhanced_bytes))
                enhanced_img.thumbnail((200, 200))
                tk_img_enhanced = ImageTk.PhotoImage(enhanced_img)

                enhanced_label = ttk.Label(enhanced_frame, image=tk_img_enhanced)
                enhanced_label.image = tk_img_enhanced  # Keep a reference
                enhanced_label.pack(padx=5, pady=5)

                # Enhanced size
                enhanced_size = len(enhanced_bytes)
                ttk.Label(enhanced_frame, text=f"الحجم: {format_size(enhanced_size)}").pack(pady=2)

                # Store preview data
                preview_images.append({
                    'index': i,
                    'item': image_item,
                    'original_size': original_size,
                    'enhanced_size': enhanced_size,
                    'enhanced_bytes': enhanced_bytes,
                    'tk_img_original': tk_img_original,
                    'tk_img_enhanced': tk_img_enhanced
                })

            except Exception as e:
                # Show error message
                error_frame = ttk.Frame(preview_scrollable_frame, borderwidth=1, relief="solid")
                error_frame.pack(padx=10, pady=10, fill="x")

                ttk.Label(error_frame, text=f"خطأ في معالجة الصورة: {image_item['path_or_url']}").pack(pady=2)
                ttk.Label(error_frame, text=f"الخطأ: {e}").pack(pady=2)

                update_status(f"!!! خطأ في معالجة الصورة {image_item['path_or_url']}: {e}")

        # Hide progress bar when done
        progress_frame.pack_forget()

    # Start processing in a separate thread
    threading.Thread(target=process_images_for_enhancement, daemon=True).start()

def apply_enhancement(images_to_enhance, enhancement_type):
    """Apply enhancement to the selected images."""
    global managed_images, preview_window, preview_images

    if not preview_images:
        messagebox.showinfo("لا توجد معاينة", "لم يتم معالجة أي صور للمعاينة.")
        return

    # Ask for confirmation
    if not messagebox.askyesno("تأكيد", f"هل أنت متأكد من تطبيق التحسين على {len(preview_images)} صورة؟"):
        return

    # Apply enhancement to each image
    for preview_data in preview_images:
        index = preview_data['index']
        item = preview_data['item']
        enhanced_bytes = preview_data['enhanced_bytes']

        # Update the managed_images list with enhancement information
        for i, managed_item in enumerate(managed_images):
            if managed_item['path_or_url'] == item['path_or_url']:
                managed_images[i]['original_size'] = preview_data['original_size']
                managed_images[i]['enhanced_size'] = preview_data['enhanced_size']
                managed_images[i]['enhanced_bytes'] = enhanced_bytes
                break

    # Close preview window
    if preview_window and preview_window.winfo_exists():
        preview_window.destroy()

    # Update the display
    display_managed_images()

    # Show success message
    update_status(f"تم تطبيق التحسين على {len(preview_images)} صورة بنجاح.")
    messagebox.showinfo("تم", f"تم تطبيق التحسين على {len(preview_images)} صورة بنجاح.")

def copy_all_image_urls():
    """Copy all image URLs to clipboard."""
    if not managed_images:
        messagebox.showinfo("لا صور", "لا توجد صور لنسخ روابطها.")
        return

    # Collect all URLs
    urls = []
    for item in managed_images:
        if item['type'] == 'url':
            # If it's already a URL, add it
            urls.append(item['path_or_url'])
        elif item['type'] == 'local' and 'uploaded_url' in item:
            # If it's a local file that has been uploaded, add the uploaded URL
            urls.append(item['uploaded_url'])

    if not urls:
        messagebox.showinfo("لا روابط", "لا توجد روابط صور لنسخها.")
        return

    # Join URLs with newlines and copy to clipboard
    urls_text = "\n".join(urls)
    try:
        pyperclip.copy(urls_text)
        update_status(f"تم نسخ {len(urls)} رابط صورة إلى الحافظة.")
        messagebox.showinfo("تم النسخ", f"تم نسخ {len(urls)} رابط صورة إلى الحافظة.")
    except Exception as e:
        update_status(f"!!! خطأ أثناء نسخ الروابط: {e}")
        messagebox.showerror("خطأ", f"فشل نسخ الروابط: {e}")

def display_managed_images():
    # مسح العناصر القديمة
    for widget in image_preview_inner_frame.winfo_children():
        widget.destroy()

    if not managed_images:
        # عرض رسالة لا توجد صور
        ttk.Label(image_preview_inner_frame, text="لا توجد صور معروضة حاليًا.").pack(padx=10, pady=10)

        # إضافة زر لحذف جميع الصور (معطل عندما لا توجد صور)
        # Moved to control frame below
        # clear_all_images_button = ttk.Button(image_preview_inner_frame, text="حذف جميع الصور", state=tk.DISABLED)
        # clear_all_images_button.pack(pady=5)
        return

    # Control frame for buttons above images
    control_frame = ttk.Frame(image_preview_inner_frame)
    control_frame.pack(fill="x", padx=5, pady=5)

    # إضافة زر لحذف جميع الصور
    clear_all_images_button = ttk.Button(
        control_frame,
        text="حذف جميع الصور",
        command=lambda: clear_all_managed_images()
    )
    clear_all_images_button.pack(side=tk.LEFT, padx=5)

    # NEW: Button to delete selected images
    delete_selected_images_button = ttk.Button(
        control_frame,
        text="حذف الصور المحددة",
        command=lambda: delete_selected_managed_images() # Call the new delete function
    )
    delete_selected_images_button.pack(side=tk.LEFT, padx=5)

    # NEW: Button to copy all image URLs
    copy_all_urls_button = ttk.Button(
        control_frame,
        text="نسخ جميع روابط الصور",
        command=lambda: copy_all_image_urls()
    )
    copy_all_urls_button.pack(side=tk.LEFT, padx=5)

    # NEW: Compression level selection
    compression_frame = ttk.Frame(control_frame)
    compression_frame.pack(side=tk.LEFT, padx=10)

    ttk.Label(compression_frame, text="مستوى الضغط:").pack(side=tk.LEFT)

    global compression_level
    compression_var = tk.StringVar(value=compression_level)

    compression_combo = ttk.Combobox(
        compression_frame,
        textvariable=compression_var,
        values=["normal", "medium", "high"],
        width=8,
        state="readonly"
    )
    compression_combo.pack(side=tk.LEFT, padx=5)

    # Update the global compression level when changed
    def update_compression_level(*args):
        global compression_level
        compression_level = compression_var.get()
        update_status(f"تم تغيير مستوى الضغط إلى: {compression_level}")

    compression_var.trace_add("write", update_compression_level)

    # NEW: Create a second row for additional buttons
    second_control_frame = ttk.Frame(image_preview_inner_frame)
    second_control_frame.pack(fill="x", padx=5, pady=5)

    # NEW: Button to compress all images
    compress_all_images_button = ttk.Button(
        second_control_frame,
        text="ضغط جميع الصور",
        command=lambda: compress_images_task(all_images=True)
    )
    compress_all_images_button.pack(side=tk.LEFT, padx=5)

    # NEW: Button to compress selected images
    compress_selected_images_button = ttk.Button(
        second_control_frame,
        text="ضغط الصور المحددة",
        command=lambda: compress_images_task(all_images=False)
    )
    compress_selected_images_button.pack(side=tk.LEFT, padx=5)

    # NEW: Button to enhance all images
    enhance_all_images_button = ttk.Button(
        second_control_frame,
        text="تحسين جودة جميع الصور",
        command=lambda: enhance_images_task(all_images=True)
    )
    enhance_all_images_button.pack(side=tk.LEFT, padx=5)

    # NEW: Button to enhance selected images
    enhance_selected_images_button = ttk.Button(
        second_control_frame,
        text="تحسين جودة الصور المحددة",
        command=lambda: enhance_images_task(all_images=False)
    )
    enhance_selected_images_button.pack(side=tk.LEFT, padx=5)

    # إضافة تعليمات للمستخدم
    ttk.Label(control_frame, text="استخدم أزرار ↑ و ↓ لترتيب الصور").pack(side=tk.RIGHT, padx=5)

    for i, image_item in enumerate(managed_images):
        img_data = None # Will hold the actual image bytes for thumbnail
        original_size_str = "N/A"
        item_frame = None # Initialize item_frame here to ensure it's always defined for error handling

        try:
            if image_item['type'] == 'url':
                img_data_for_thumb_and_size = None # بيانات الصورة الفعلية
                try:
                    # محاولة HEAD أولاً للحجم
                    response_head = requests.head(image_item['path_or_url'], timeout=5, allow_redirects=True, headers={'User-Agent': 'Mozilla/5.0'})
                    response_head.raise_for_status()
                    content_length = response_head.headers.get('Content-Length')
                    if content_length:
                        original_size_str = format_size(int(content_length))
                        # سنحتاج لتحميل البيانات للعرض المصغر على أي حال
                        # Ensure User-Agent is included in all requests
                        response_img_thumb = requests.get(image_item['path_or_url'], stream=True, timeout=10, headers={'User-Agent': 'Mozilla/5.0'})
                        response_img_thumb.raise_for_status()
                        img_data_for_thumb_and_size = response_img_thumb.content
                    else: # إذا لم يكن Content-Length متاحًا، قم بالتحميل الكامل مرة واحدة
                        # Ensure User-Agent is included in all requests
                        response_full = requests.get(image_item['path_or_url'], stream=True, timeout=10, headers={'User-Agent': 'Mozilla/5.0'})
                        response_full.raise_for_status()
                        img_data_for_thumb_and_size = response_full.content
                        original_size_str = format_size(len(img_data_for_thumb_and_size))

                except requests.exceptions.Timeout:
                    original_size_str = "N/A (Timeout)"
                    update_status(f"Timeout getting size/data for URL: {image_item['path_or_url']}")
                except requests.exceptions.RequestException as e_size:
                    original_size_str = f"N/A (Err)"
                    update_status(f"Error getting size/data for URL {image_item['path_or_url']}: {e_size}")

                img_data = img_data_for_thumb_and_size # استخدم البيانات المحملة

            elif image_item['type'] == 'local':
                if os.path.exists(image_item['path_or_url']):
                    with open(image_item['path_or_url'], 'rb') as f:
                        img_data = f.read()
                    if img_data:
                        original_size_str = format_size(len(img_data))
                else:
                    original_size_str = "N/A (Not Found)"
                    update_status(f"Local image not found for size: {image_item['path_or_url']}")

            item_frame = ttk.Frame(image_preview_inner_frame, borderwidth=1, relief="solid")
            item_frame.pack(side=tk.LEFT, padx=5, pady=5, anchor="n")
            image_item['frame'] = item_frame

            # NEW: Checkbox for selection
            image_item['selected_var'] = tk.BooleanVar(value=False) # Add a BooleanVar to the item
            select_checkbox = ttk.Checkbutton(item_frame, variable=image_item['selected_var'])
            select_checkbox.pack(pady=(2,0)) # Pack at the top of the item frame

            if img_data:
                img = Image.open(BytesIO(img_data))

                # Check if it's an animated GIF
                is_animated_gif = False
                if img.format == 'GIF' and getattr(img, 'is_animated', False):
                    is_animated_gif = True
                    update_status(f"تم اكتشاف GIF متحرك بـ {img.n_frames} إطار")

                if is_animated_gif:
                    # For animated GIFs, we'll create a special animated preview
                    # First, create a thumbnail of each frame
                    frames = []
                    durations = []

                    try:
                        for frame_idx in range(img.n_frames):
                            img.seek(frame_idx)
                            frame_copy = img.copy()
                            frame_copy.thumbnail((100, 100))
                            frames.append(ImageTk.PhotoImage(frame_copy))
                            durations.append(img.info.get('duration', 100))  # Default to 100ms
                    except EOFError:
                        pass  # End of frames

                    # Store the frames in the item
                    image_item['tk_frames'] = frames
                    image_item['frame_durations'] = durations
                    image_item['current_frame'] = 0
                    image_item['is_animated'] = True

                    # Create image label
                    img_label = ttk.Label(item_frame)
                    img_label.pack(pady=(0,2))
                    image_item['label'] = img_label

                    # Show the first frame
                    if frames:
                        img_label.configure(image=frames[0])

                    # Add animation indicator
                    ttk.Label(item_frame, text="GIF متحرك", foreground="blue").pack(pady=(0, 2))

                    # Start animation
                    def animate_gif(item=image_item):
                        if item.get('label') and item['label'].winfo_exists():
                            frames = item.get('tk_frames', [])
                            durations = item.get('frame_durations', [])
                            if frames and 'current_frame' in item:
                                current = item['current_frame']
                                item['current_frame'] = (current + 1) % len(frames)
                                item['label'].configure(image=frames[item['current_frame']])
                                # Schedule next frame
                                delay = durations[current] if current < len(durations) else 100
                                if delay < 10:  # Ensure minimum delay
                                    delay = 100
                                if 'window' in globals() and window.winfo_exists():
                                    window.after(delay, lambda: animate_gif(item))

                    # Start animation after a short delay
                    if 'window' in globals() and window.winfo_exists():
                        window.after(100, lambda: animate_gif(image_item))
                else:
                    # Regular image handling
                    img.thumbnail((100, 100))
                    image_item['tk_image'] = ImageTk.PhotoImage(img)
                    image_item['is_animated'] = False

                    # Create image label
                    img_label = ttk.Label(item_frame, image=image_item['tk_image'])
                    img_label.pack(pady=(0,2))
                    image_item['label'] = img_label
            else:
                 ttk.Label(item_frame, text="فشل عرض الصورة").pack(pady=(0,2))

            name_label_text = os.path.basename(image_item['path_or_url']) if image_item['type'] == 'local' else image_item['path_or_url'].split('/')[-1]
            name_label = ttk.Label(item_frame, text=name_label_text[:20] + ('...' if len(name_label_text) > 20 else ''), wraplength=90)
            name_label.pack(pady=(0,2))

            # Show original size
            size_label_text = f"الحجم الأصلي: {original_size_str}"
            size_info_label = ttk.Label(item_frame, text=size_label_text, wraplength=90, font=("Tahoma", 8)) # خط أصغر للحجم
            size_info_label.pack(pady=(0,2))

            # Show compressed size if available
            if 'compressed_size' in image_item and image_item['compressed_size']:
                compressed_size_str = format_size(image_item['compressed_size'])
                original_size_bytes = 0

                # Calculate compression ratio if original size is available
                if 'original_size' in image_item and image_item['original_size']:
                    original_size_bytes = image_item['original_size']
                elif original_size_str != "N/A" and not "Err" in original_size_str:
                    try:
                        # Try to parse the original size string back to bytes
                        if "KB" in original_size_str:
                            original_size_bytes = float(original_size_str.replace("KB", "").strip()) * 1024
                        elif "MB" in original_size_str:
                            original_size_bytes = float(original_size_str.replace("MB", "").strip()) * 1024 * 1024
                        elif "B" in original_size_str:
                            original_size_bytes = float(original_size_str.replace("B", "").strip())
                    except:
                        pass

                # Calculate and show compression ratio
                if original_size_bytes > 0:
                    compression_ratio = (1 - (image_item['compressed_size'] / original_size_bytes)) * 100
                    compressed_size_text = f"بعد الضغط: {compressed_size_str} (توفير: {compression_ratio:.1f}%)"
                else:
                    compressed_size_text = f"بعد الضغط: {compressed_size_str}"

                compressed_size_label = ttk.Label(item_frame, text=compressed_size_text, wraplength=90, font=("Tahoma", 8))
                compressed_size_label.pack(pady=(0,2))

            # إضافة إطار للأزرار
            buttons_frame = ttk.Frame(item_frame)
            buttons_frame.pack(pady=(0,2))

            # زر الإزالة (Keep individual remove for convenience)
            remove_button = ttk.Button(buttons_frame, text="إزالة", width=5, command=lambda item=image_item: remove_managed_image(item))
            remove_button.pack(side=tk.LEFT, padx=2)
            image_item['button'] = remove_button

            # أزرار ترتيب الصور (للأعلى وللأسفل)
            up_button = ttk.Button(
                buttons_frame,
                text="↑",
                width=2,
                command=lambda idx=i: move_image_up(idx)
            )
            up_button.pack(side=tk.LEFT, padx=1)

            down_button = ttk.Button(
                buttons_frame,
                text="↓",
                width=2,
                command=lambda idx=i: move_image_down(idx)
            )
            down_button.pack(side=tk.LEFT, padx=1)

            # زر تغيير الرابط
            change_url_button = ttk.Button(
                buttons_frame,
                text="تغيير الرابط",
                command=lambda idx=i: change_image_url(idx)
            )
            change_url_button.pack(side=tk.LEFT, padx=5)

        except Exception as e:
            update_status(f"!!! خطأ أثناء عرض الصورة المصغرة {image_item['path_or_url']}: {e}")
            # ... (منطق معالجة الخطأ كما كان) ...
            if not image_item.get('frame'): # Ensure frame exists for error display
                item_frame_err = ttk.Frame(image_preview_inner_frame, borderwidth=1, relief="solid")
                item_frame_err.pack(side=tk.LEFT, padx=5, pady=5, anchor="n")
                image_item['frame'] = item_frame_err # Store it back
            else: # Use existing frame if already created
                item_frame_err = image_item['frame']

            # Clear any previous content in this error frame
            for child_widget in item_frame_err.winfo_children():
                child_widget.destroy()

            # NEW: Add checkbox to error frame too
            image_item['selected_var'] = tk.BooleanVar(value=False) # Add a BooleanVar to the item
            select_checkbox_err = ttk.Checkbutton(item_frame_err, variable=image_item['selected_var'])
            select_checkbox_err.pack(pady=(2,0))

            ttk.Label(item_frame_err, text="فشل التحميل").pack()
            name_label_text_err = os.path.basename(image_item['path_or_url']) if image_item['type'] == 'local' else image_item['path_or_url'].split('/')[-1]
            ttk.Label(item_frame_err, text=name_label_text_err[:20] + ('...' if len(name_label_text_err) > 20 else ''), wraplength=90).pack(pady=(0,2))

            # إضافة إطار للأزرار
            buttons_frame_err = ttk.Frame(item_frame_err)
            buttons_frame_err.pack(pady=(0,2))

            # زر الإزالة
            remove_button_err = ttk.Button(buttons_frame_err, text="إزالة", width=5, command=lambda item=image_item: remove_managed_image(item))
            remove_button_err.pack(side=tk.LEFT, padx=2)

            # أزرار ترتيب الصور (للأعلى وللأسفل)
            up_button_err = ttk.Button(
                buttons_frame_err,
                text="↑",
                width=2,
                command=lambda idx=i: move_image_up(idx)
            )
            up_button_err.pack(side=tk.LEFT, padx=1)

            down_button_err = ttk.Button(
                buttons_frame_err,
                text="↓",
                width=2,
                command=lambda idx=i: move_image_down(idx)
            )
            down_button_err.pack(side=tk.LEFT, padx=1)

            # زر تغيير الرابط
            change_url_button_err = ttk.Button(
                buttons_frame_err,
                text="تغيير الرابط",
                command=lambda idx=i: change_image_url(idx)
            )
            change_url_button_err.pack(side=tk.LEFT, padx=5)


def handle_process_upload_selected():
    if not STORAGE_CLIENT_OK:
        messagebox.showerror("خطأ", "فشل الاتصال بـ Supabase Storage.")
        return
    if not managed_images:
        messagebox.showinfo("لا صور", "الرجاء إضافة صور أولاً.")
        return

    update_status(f"بدء معالجة ورفع {len(managed_images)} صورة معروضة...")
    process_upload_images_button.config(state=tk.DISABLED)
    # Pass a copy of the list to the thread
    run_in_thread(process_and_upload_images_task, list(managed_images))


def process_and_upload_images_task(images_to_process): # Takes list of image_item dicts
    processed_urls = []
    success_count = 0
    total_to_process = len(images_to_process)

    for i, image_item in enumerate(images_to_process):
        update_status(f"\n--- معالجة الصورة {i+1}/{total_to_process}: {image_item['path_or_url']} ---")
        try:
            # Check if the image has already been compressed or enhanced
            if 'compressed_bytes' in image_item:
                update_status(f"استخدام الصورة المضغوطة مسبقًا للصورة {i+1}")
                # Use the pre-compressed image data
                compressed_bytes = image_item['compressed_bytes']

                # Check if this is a GIF image that should maintain animation
                is_animated_gif = image_item.get('is_animated', False)
                path_is_gif = image_item['path_or_url'].lower().endswith('.gif')

                if is_animated_gif or path_is_gif:
                    update_status(f"الصورة هي GIF متحركة. سيتم الحفاظ على التنسيق الأصلي.")
                    img_format = 'gif'
                else:
                    # For non-GIF images, use the compressed format or default to jpeg
                    img_format = image_item.get('compressed_format', 'jpeg')

                # Generate a unique filename
                original_filename = os.path.basename(image_item['path_or_url'])
                base, _ = os.path.splitext(original_filename)
                if not base: base = "image"
                final_filename = f"{base}.{img_format}"
                unique_filename = generate_unique_filename(final_filename, prefix="img_")

                # Upload to Supabase
                upload_content_type = f"image/{img_format}"
                final_url = upload_to_supabase(IMAGE_BUCKET, compressed_bytes, unique_filename, upload_content_type)
            elif 'enhanced_bytes' in image_item:
                update_status(f"استخدام الصورة المحسنة مسبقًا للصورة {i+1}")
                # Use the pre-enhanced image data
                enhanced_bytes = image_item['enhanced_bytes']

                # Check if this is a GIF image that should maintain animation
                is_animated_gif = image_item.get('is_animated', False)
                path_is_gif = image_item['path_or_url'].lower().endswith('.gif')

                if is_animated_gif or path_is_gif:
                    update_status(f"الصورة هي GIF متحركة. سيتم الحفاظ على التنسيق الأصلي.")
                    img_format = 'gif'
                else:
                    # Determine format from the enhanced image
                    try:
                        img = Image.open(BytesIO(enhanced_bytes))
                        img_format = img.format.lower() if img.format else 'jpeg'
                    except:
                        img_format = 'jpeg'  # Default if can't determine

                # Generate a unique filename
                original_filename = os.path.basename(image_item['path_or_url'])
                base, _ = os.path.splitext(original_filename)
                if not base: base = "image"
                final_filename = f"{base}.{img_format}"
                unique_filename = generate_unique_filename(final_filename, prefix="img_")

                # Upload to Supabase
                upload_content_type = f"image/{img_format}"
                final_url = upload_to_supabase(IMAGE_BUCKET, enhanced_bytes, unique_filename, upload_content_type)
            else:
                # Process the image normally
                final_url = process_image(image_item['path_or_url'], image_item['type'])

            if final_url:
                status_msg = f"--> رابط الصورة النهائي: {final_url}"
                update_status(status_msg)
                processed_urls.append(final_url)
                success_count += 1

                # Store the uploaded URL in the image item for future reference
                for j, managed_item in enumerate(managed_images):
                    if managed_item['path_or_url'] == image_item['path_or_url']:
                        managed_images[j]['uploaded_url'] = final_url
                        break
            else:
                update_status(f"!!! فشلت معالجة الصورة {i+1} ({image_item['path_or_url']}) - لم يتم إرجاع رابط.")
        except Exception as e:
            update_status(f"!!! فشل معالجة الصورة {i+1} ({image_item['path_or_url']}): {e}")
            update_status(f"Traceback: {traceback.format_exc()}")


    if processed_urls:
        if 'publish_primary_image_entry' in globals():
            # استخدام الصورة الأولى في القائمة المرتبة كصورة رئيسية
            auto_populate_field(publish_primary_image_entry, processed_urls[0])
        if 'publish_other_images_text' in globals() and len(processed_urls) > 1:
            # استخدام باقي الصور في القائمة المرتبة كصور إضافية
            other_urls_to_add = processed_urls[1:7] # Max 6 other images
            auto_populate_text_widget(publish_other_images_text, "\n".join(other_urls_to_add))

    if 'process_upload_images_button' in globals() and process_upload_images_button.winfo_exists():
        process_upload_images_button.config(state=tk.NORMAL)
    update_status(f"\n--- === اكتملت معالجة الصور ({success_count}/{total_to_process} ناجحة) === ---")


def compress_gif(gif_data, compression_level='normal'):
    """Compress a GIF image while preserving animation.

    Args:
        gif_data: GIF image data as bytes
        compression_level: 'normal', 'medium', or 'high'

    Returns:
        Tuple of (compressed_bytes, format, content_type)
    """
    try:
        # Open the GIF
        gif = Image.open(BytesIO(gif_data))

        # Check if it's actually a GIF
        if gif.format != 'GIF':
            update_status("Not a GIF image. Using regular image compression.")
            return None

        # Check if it has multiple frames (is animated)
        is_animated = getattr(gif, 'is_animated', False)
        if not is_animated:
            # Try to manually check if it's animated by seeking to the second frame
            try:
                gif.seek(1)
                is_animated = True
                gif.seek(0)  # Reset to first frame
            except EOFError:
                # Only one frame, not animated
                is_animated = False

        if not is_animated:
            # Not an animated GIF, use regular compression
            update_status("GIF is not animated. Using regular image compression.")
            return None

        # Get original info
        original_size = len(gif_data)
        original_width, original_height = gif.size
        n_frames = getattr(gif, 'n_frames', 0)
        update_status(f"Processing animated GIF: {original_width}x{original_height}, {n_frames} frames, {format_size(original_size)}")

        # Define compression parameters based on level
        if compression_level == 'high':
            resize_factor = 0.7  # Reduce dimensions to 70%
            colors = 128  # Reduce color palette
            optimize_frames = True  # Remove redundant pixels between frames
        elif compression_level == 'medium':
            resize_factor = 0.8  # Reduce dimensions to 80%
            colors = 192  # Reduce color palette
            optimize_frames = True  # Remove redundant pixels between frames
        else:  # 'normal' or any other value
            resize_factor = 0.9  # Reduce dimensions to 90%
            colors = 256  # Keep full color palette
            optimize_frames = False  # Keep all pixels for better quality

        update_status(f"GIF compression parameters: resize={resize_factor}, colors={colors}, optimize_frames={optimize_frames}")

        # Calculate new dimensions
        new_width = int(original_width * resize_factor)
        new_height = int(original_height * resize_factor)

        # Create a new GIF with the same parameters
        frames = []
        durations = []
        disposals = []  # Store disposal method for each frame
        transparency_indices = []  # Store transparency index for each frame

        # Process each frame
        try:
            i = 0
            while True:
                gif.seek(i)

                # Get frame info
                duration = gif.info.get('duration', 100)  # Default to 100ms if not specified
                durations.append(duration)

                # Get disposal method (how to handle the previous frame)
                # 0: No disposal specified
                # 1: Do not dispose (leave as is)
                # 2: Restore to background color
                # 3: Restore to previous content
                disposal = gif.disposal_method if hasattr(gif, 'disposal_method') else 2
                disposals.append(disposal)

                # Get transparency index if available
                transparency = gif.info.get('transparency', None)
                transparency_indices.append(transparency)

                # Copy and process the frame
                frame = gif.copy()

                # Resize frame if needed
                if resize_factor < 1.0:
                    frame = frame.resize((new_width, new_height), Image.LANCZOS)

                # Handle transparency and color palette
                if frame.mode == 'P':
                    # Already in palette mode, optimize palette if needed
                    if colors < 256:
                        # Create a new palette with fewer colors while preserving transparency
                        if transparency is not None:
                            # Convert to RGBA, then back to P with fewer colors
                            frame_rgba = frame.convert('RGBA')
                            frame = frame_rgba.quantize(colors=colors, method=Image.MEDIANCUT)
                            # Ensure transparency is preserved
                            alpha = frame_rgba.split()[3]
                            mask = Image.eval(alpha, lambda a: 255 if a <= 128 else 0)
                            frame.paste(0, mask)
                        else:
                            # No transparency, just reduce colors
                            frame = frame.quantize(colors=colors, method=Image.MEDIANCUT)
                else:
                    # Convert to P mode with optimized palette
                    if transparency is not None:
                        # Handle transparency for non-P mode
                        frame = frame.convert('RGBA')
                        frame = frame.quantize(colors=colors, method=Image.MEDIANCUT)
                    else:
                        frame = frame.convert('P', palette=Image.ADAPTIVE, colors=colors)

                frames.append(frame)
                i += 1

                # Progress update for large GIFs
                if i % 10 == 0:
                    update_status(f"Processed {i} frames...")

        except EOFError:
            # End of frames
            pass

        update_status(f"Processed all {len(frames)} frames. Saving compressed GIF...")

        # Save the compressed GIF
        output_buffer = BytesIO()

        # Prepare save parameters
        save_kwargs = {
            'format': 'GIF',
            'save_all': True,
            'append_images': frames[1:],
            'optimize': optimize_frames,
            'duration': durations,
            'loop': 0,  # Loop forever
            'disposal': disposals,
        }

        # Add transparency if any frame has it
        if any(t is not None for t in transparency_indices):
            # Use the first valid transparency index
            for t in transparency_indices:
                if t is not None:
                    save_kwargs['transparency'] = t
                    break

        # Save the first frame with all parameters
        frames[0].save(output_buffer, **save_kwargs)
        compressed_bytes = output_buffer.getvalue()
        compressed_size = len(compressed_bytes)

        # Calculate compression ratio
        compression_ratio = (1 - (compressed_size / original_size)) * 100 if original_size > 0 else 0

        # Return compressed GIF data with detailed status
        update_status(f"Compressed animated GIF: Original: {format_size(original_size)}, Compressed: {format_size(compressed_size)}, Saved: {compression_ratio:.1f}%")

        # If compression actually made it larger, return the original
        if compressed_size > original_size:
            update_status("Compression increased file size. Using original GIF data.")
            return gif_data, 'gif', 'image/gif'

        return compressed_bytes, 'gif', 'image/gif'

    except Exception as e:
        update_status(f"Error compressing GIF: {e}")
        update_status(f"Traceback: {traceback.format_exc()}")
        return None

def compress_image(img, compression_level='normal', original_data=None):
    """Compress an image based on the specified compression level.

    Args:
        img: PIL Image object
        compression_level: 'normal', 'medium', or 'high'
        original_data: Original image data as bytes (for GIF handling)

    Returns:
        Tuple of (compressed_bytes, format, content_type)
    """
    # Check if this is a GIF and we have the original data
    if original_data and (img.format == 'GIF' or (hasattr(img, 'filename') and str(img.filename).lower().endswith('.gif'))):
        # Check if it's an animated GIF
        is_animated = False
        try:
            # Try to detect animation
            if img.format == 'GIF' and getattr(img, 'is_animated', False):
                is_animated = True
                update_status(f"Detected animated GIF with {getattr(img, 'n_frames', 0)} frames")

            # Even if not detected as animated by PIL, try to compress as GIF anyway
            # as some GIFs might not be properly detected
            gif_result = compress_gif(original_data, compression_level)
            if gif_result:
                update_status("Successfully compressed GIF image")
                return gif_result
            else:
                update_status("GIF compression returned no result, falling back to regular compression")
        except Exception as e:
            update_status(f"Error during GIF detection/compression: {e}")
            update_status(f"Traceback: {traceback.format_exc()}")

        # If GIF compression failed, continue with regular compression

    # Define compression parameters based on level
    if compression_level == 'high':
        # High compression (smallest file size, lowest quality)
        jpeg_quality = 60
        png_compression = 9
        webp_quality = 60
        resize_factor = 0.8  # Reduce dimensions to 80%
    elif compression_level == 'medium':
        # Medium compression
        jpeg_quality = 75
        png_compression = 7
        webp_quality = 75
        resize_factor = 0.9  # Reduce dimensions to 90%
    else:  # 'normal' or any other value
        # Normal compression (better quality, larger file size)
        jpeg_quality = 85
        png_compression = 6
        webp_quality = 85
        resize_factor = 1.0  # No resize

    # Resize image if needed
    if resize_factor < 1.0:
        new_width = int(img.width * resize_factor)
        new_height = int(img.height * resize_factor)
        img = img.resize((new_width, new_height), Image.LANCZOS)
        update_status(f"Resized image to {new_width}x{new_height} for {compression_level} compression")

    # Determine best format based on image content
    img_format = img.format if img.format else 'JPEG'
    if img_format.upper() in ['PNG', 'WEBP', 'JPEG', 'JPG']:
        img_format_save = img_format.upper()
    else:
        # Default to JPEG for most images
        img_format_save = 'JPEG'

    # Prepare compression parameters
    output_buffer = BytesIO()
    save_kwargs = {'format': img_format_save}

    if img_format_save == 'JPEG':
        save_kwargs['quality'] = jpeg_quality
        save_kwargs['optimize'] = True
    elif img_format_save == 'PNG':
        save_kwargs['optimize'] = True
        save_kwargs['compress_level'] = png_compression
    elif img_format_save == 'WEBP':
        save_kwargs['quality'] = webp_quality

    # Save compressed image
    img.save(output_buffer, **save_kwargs)
    compressed_bytes = output_buffer.getvalue()

    # Return compressed image data and metadata
    content_type = f'image/{img_format_save.lower()}'
    return compressed_bytes, img_format_save.lower(), content_type

def process_image(path_or_url: str, image_type: str): # Added image_type
    update_status(f"Processing image ({image_type}): {path_or_url}")
    image_bytes = None
    original_filename_for_upload = "image_from_tool" # Default

    if image_type == 'local':
        if not os.path.exists(path_or_url):
            raise FileNotFoundError(f"Local image file not found: {path_or_url}")
        with open(path_or_url, 'rb') as f:
            image_bytes = f.read()
        original_filename_for_upload = os.path.basename(path_or_url)
        content_type = mimetypes.guess_type(path_or_url)[0] or 'application/octet-stream'
        if not content_type.startswith('image/'):
            raise ValueError(f"Local file is not a valid image type: {content_type} for {path_or_url}")
    elif image_type == 'url':
        if STORAGE_URL and IMAGE_BUCKET and path_or_url.startswith(f"{STORAGE_URL}/storage/v1/object/public/{IMAGE_BUCKET}/"):
            update_status(f"Image URL is already a Supabase link. Using directly: {path_or_url}")
            return path_or_url
        try:
            headers = {'User-Agent': 'Mozilla/5.0'}
            # Handle media.forgecdn.net links directly
            if "media.forgecdn.net" in path_or_url:
                update_status(f"Directly processing media.forgecdn.net image URL: {path_or_url}")

            response = requests.get(path_or_url, stream=True, timeout=30, headers=headers)
            response.raise_for_status()
            content_type = response.headers.get('content-type', 'application/octet-stream').split(';')[0]
            if not content_type.startswith('image/'):
                raise ValueError(f"URL not a valid image (Type: {content_type}) for {path_or_url}")
            image_bytes = response.content
            original_filename_for_upload = sanitize_filename(path_or_url.split('/')[-1].split('?')[0])
        except requests.exceptions.RequestException as e:
            raise ValueError(f"Download error for {path_or_url}: {e}")
    else:
        raise ValueError(f"Unknown image type: {image_type}")

    if not image_bytes: raise ValueError("Image data is empty.")
    update_status(f"Image data loaded ({len(image_bytes)} bytes).")

    # Store original size for reporting
    original_size = len(image_bytes)

    # --- Phosus API Enhancement ---
    selected_enhancement = phosus_enhancement_var.get() if 'phosus_enhancement_var' in globals() else "none"
    if selected_enhancement != "none":
        update_status(f"Attempting Phosus enhancement: {selected_enhancement}")
        try:
            enhanced_bytes = call_phosus_api(selected_enhancement, image_bytes, content_type)
            if enhanced_bytes:
                update_status(f"Phosus enhancement successful. New size: {len(enhanced_bytes)} bytes.")
                image_bytes = enhanced_bytes
                # Update content_type if super-resolution might change it (e.g. to PNG if it outputs PNG)
                # For now, assume it keeps original type or we handle it in call_phosus_api if needed
            else:
                update_status("Phosus enhancement returned no data, using original image.")
        except Exception as phosus_err:
            update_status(f"!!! Phosus API error: {phosus_err}. Using original image.")
    # --- End Phosus API Enhancement ---

    # --- GIF Handling ---
    is_gif = False
    if content_type and 'image/gif' in content_type.lower():
        is_gif = True
    elif original_filename_for_upload.lower().endswith('.gif'):
        is_gif = True

    # We'll still detect GIFs, but now we'll try to compress them instead of uploading directly
    if is_gif:
        update_status("Detected GIF. Attempting to compress while preserving animation.")

        # Try to compress the GIF using our new function
        try:
            # Open the GIF to check if it's animated
            gif_img = Image.open(BytesIO(image_bytes))
            is_animated = getattr(gif_img, 'is_animated', False)

            # Try to manually check if it's animated by seeking to the second frame
            if not is_animated:
                try:
                    gif_img.seek(1)
                    is_animated = True
                    gif_img.seek(0)  # Reset to first frame
                except EOFError:
                    # Only one frame, not animated
                    is_animated = False

            if is_animated:
                update_status(f"GIF is animated with {getattr(gif_img, 'n_frames', 0)} frames.")

                # Make sure the filename ends with .gif
                base, ext = os.path.splitext(original_filename_for_upload)
                if not ext.lower() == '.gif':
                    original_filename_for_upload = f"{base}.gif"

                # Try to compress the animated GIF
                gif_result = compress_gif(image_bytes, compression_level)

                if gif_result:
                    compressed_bytes, _, _ = gif_result
                    update_status(f"Successfully compressed animated GIF. Original: {format_size(len(image_bytes))}, Compressed: {format_size(len(compressed_bytes))}")

                    # Generate a unique filename with .gif extension
                    unique_gif_filename = generate_unique_filename(original_filename_for_upload, prefix="img_")

                    # Make sure the filename ends with .gif
                    if not unique_gif_filename.lower().endswith('.gif'):
                        unique_gif_filename = f"{os.path.splitext(unique_gif_filename)[0]}.gif"

                    # Upload the compressed GIF
                    return upload_to_supabase(IMAGE_BUCKET, compressed_bytes, unique_gif_filename, 'image/gif')
                else:
                    update_status("Could not compress animated GIF. Uploading original.")
                    # Upload the original animated GIF
                    unique_gif_filename = generate_unique_filename(original_filename_for_upload, prefix="img_")

                    # Make sure the filename ends with .gif
                    if not unique_gif_filename.lower().endswith('.gif'):
                        unique_gif_filename = f"{os.path.splitext(unique_gif_filename)[0]}.gif"

                    return upload_to_supabase(IMAGE_BUCKET, image_bytes, unique_gif_filename, 'image/gif')
            else:
                update_status("GIF is not animated. Will process as normal image.")
                # Continue with normal processing for non-animated GIFs
        except Exception as gif_err:
            update_status(f"Error processing GIF: {gif_err}. Uploading original.")
            # Ensure content_type is correctly set for GIF upload

            # Make sure the filename ends with .gif
            base, ext = os.path.splitext(original_filename_for_upload)
            if not ext.lower() == '.gif':
                original_filename_for_upload = f"{base}.gif"

            unique_gif_filename = generate_unique_filename(original_filename_for_upload, prefix="img_")

            # Make sure the filename ends with .gif
            if not unique_gif_filename.lower().endswith('.gif'):
                unique_gif_filename = f"{os.path.splitext(unique_gif_filename)[0]}.gif"

            return upload_to_supabase(IMAGE_BUCKET, image_bytes, unique_gif_filename, 'image/gif')
    # --- End GIF Handling ---

    try:
        img = Image.open(BytesIO(image_bytes))
    except Exception as img_err:
        raise ValueError(f"Pillow error opening image: {img_err}")

    if img.mode in ("RGBA", "P", "LA"):
        update_status(f"Converting image from {img.mode} to RGB...")
        bg = Image.new("RGB", img.size, (255, 255, 255))
        try:
            if 'A' in img.mode: bg.paste(img, mask=img.split()[-1])
            else: bg.paste(img)
            img = bg
        except Exception as convert_err:
            img = img.convert("RGB") # Fallback conversion
        update_status("Converted to RGB.")

    # Use the compression level
    current_compression_level = compression_level
    update_status(f"Using compression level: {current_compression_level}")

    # Compress the image, passing the original image data for GIF handling
    compressed_bytes, img_format_save, upload_content_type = compress_image(img, current_compression_level, image_bytes)

    # Report compression results
    compressed_size = len(compressed_bytes)
    compression_ratio = (1 - (compressed_size / original_size)) * 100 if original_size > 0 else 0
    update_status(f"Compression results: Original: {format_size(original_size)}, Compressed: {format_size(compressed_size)}, Saved: {compression_ratio:.1f}%")

    # Store the original and compressed sizes for display in the UI
    global managed_images
    for i, item in enumerate(managed_images):
        if item['path_or_url'] == path_or_url:
            managed_images[i]['original_size'] = original_size
            managed_images[i]['compressed_size'] = compressed_size
            break

    # Final check on size before upload
    if compressed_size > MAX_IMAGE_SIZE_BYTES:
        update_status(f"Image is still too large ({format_size(compressed_size)}). Applying more aggressive compression.")

        # Try more aggressive compression if still too large
        more_aggressive_level = 'high'
        if compression_level == 'high':
            # If already at high compression, try even more aggressive approach
            # Resize to 50% and use lowest quality
            new_width = int(img.width * 0.5)
            new_height = int(img.height * 0.5)
            img = img.resize((new_width, new_height), Image.LANCZOS)

            output_buffer = BytesIO()
            img.save(output_buffer, format='JPEG', quality=50, optimize=True)
            compressed_bytes = output_buffer.getvalue()
            img_format_save = 'jpeg'
            upload_content_type = 'image/jpeg'

            update_status(f"Applied extreme compression: resized to 50% and quality 50. New size: {format_size(len(compressed_bytes))}")
        else:
            # Try high compression if not already using it
            compressed_bytes, img_format_save, upload_content_type = compress_image(img, 'high')
            update_status(f"Applied high compression. New size: {format_size(len(compressed_bytes))}")

    # Final final check
    if len(compressed_bytes) > MAX_IMAGE_SIZE_BYTES:
        update_status(f"!!! Final image size ({format_size(len(compressed_bytes))}) still exceeds limit of {format_size(MAX_IMAGE_SIZE_BYTES)} after all compressions. Cannot upload.")
        return None

    base, _ = os.path.splitext(original_filename_for_upload)
    if not base: base = "image" # Ensure base name if original was just an extension or empty
    final_original_filename = f"{base}.{img_format_save}"

    unique_filename = generate_unique_filename(final_original_filename, prefix="img_")

    return upload_to_supabase(IMAGE_BUCKET, compressed_bytes, unique_filename, upload_content_type)


def process_mod(initial_page_url: str):
    update_status(f"Processing mod from: {initial_page_url}")

    if STORAGE_URL and MOD_BUCKET and initial_page_url.startswith(f"{STORAGE_URL}/storage/v1/object/public/{MOD_BUCKET}/"):
        update_status(f"Mod URL is already a Supabase link. Using directly: {initial_page_url}")
        try:
            response = requests.get(initial_page_url, stream=True, timeout=180, headers={'User-Agent': 'Mozilla/5.0'})
            response.raise_for_status()
            mod_bytes = response.content
            return initial_page_url, mod_bytes if mod_bytes else None
        except Exception as e:
            update_status(f"!!! Error fetching content from existing Supabase mod link: {e}")
            return initial_page_url, None

    download_url = ""
    mod_bytes = None
    original_filename = "modfile" # Default
    content_type_final = 'application/octet-stream' # Default
    download_url = initial_page_url # Assume direct link initially

    try:
        if "edge.forgecdn.net" in initial_page_url or initial_page_url.lower().endswith(('.mcaddon', '.mcpack')):
            update_status(f"Treating as direct download link: {initial_page_url}")
            # download_url is already initial_page_url
        elif "9minecraft.net" in initial_page_url: # Only try to extract if it's a 9minecraft page
            update_status(f"Detected 9minecraft.net URL. Attempting to extract download link from page: {initial_page_url}")
            download_url = extract_mod_download_link(initial_page_url)
        else:
            # For other URLs, still treat as direct for now, but log a warning.
            update_status(f"Warning: URL '{initial_page_url}' is not a known 9minecraft or direct forgecdn link. Attempting as direct download.")
            # download_url is already initial_page_url

        update_status(f"Attempting to download mod from: {download_url}")
        headers = {'User-Agent': 'Mozilla/5.0'}
        session = requests.Session()
        session.headers.update(headers)
        with session.get(download_url, stream=True, timeout=180) as response:
            response.raise_for_status()
            content_length = response.headers.get('content-length')
            max_size_bytes = MAX_MOD_SIZE_MB * 1024 * 1024

            if content_length and int(content_length) > max_size_bytes:
                raise ValueError(f"Mod size ({int(content_length)/(1024*1024):.2f}MB) exceeds limit.")

            mod_content_stream = BytesIO()
            total_downloaded = 0
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    total_downloaded += len(chunk)
                    if total_downloaded > max_size_bytes:
                        raise ValueError(f"Mod size during download ({total_downloaded/(1024*1024):.2f}MB) exceeds limit.")
                    mod_content_stream.write(chunk)
            mod_bytes = mod_content_stream.getvalue()
            update_status(f"Download OK. Size: {format_size(len(mod_bytes))}")

            content_type_final = response.headers.get('content-type', 'application/octet-stream').split(';')[0]
            cd = response.headers.get('content-disposition')
            if cd:
                fn_match = re.search(r'filename\*=UTF-8\'\'([\S]+)', cd, flags=re.IGNORECASE)
                if fn_match: original_filename = requests.utils.unquote(fn_match.group(1))
                elif 'filename=' in cd:
                    fn_part = cd.split('filename=')[1]
                    original_filename = fn_part.split(';')[0].strip('"\' ')
            elif "edge.forgecdn.net" in download_url: # Guess from URL for forgecdn if no CD
                 parsed_url_path = urlparse(download_url).path
                 if parsed_url_path: original_filename = os.path.basename(parsed_url_path)

        sanitized_original_filename = sanitize_filename(original_filename)
        base_name, original_ext = os.path.splitext(sanitized_original_filename)
        original_ext_lower = original_ext.lower()

        final_upload_filename = sanitized_original_filename
        target_upload_content_type = content_type_final

        if original_ext_lower not in [".mcpack", ".mcaddon"]:
            if not base_name: base_name = "modfile"
            final_upload_filename = base_name + ".mcaddon" # Force to .mcaddon if not recognized
            target_upload_content_type = 'application/octet-stream'
            update_status(f"Original extension '{original_ext_lower}' not .mcpack/.mcaddon. Forcing to .mcaddon: {final_upload_filename}")
        else:
            update_status(f"Using original extension: {final_upload_filename}")
            if content_type_final == 'application/zip' or not content_type_final.startswith('application/'): # Be more specific for mcpack/mcaddon
                 target_upload_content_type = 'application/octet-stream'


        # محاولة استخراج الإصدار من ملفات المود
        try:
            # فحص ما إذا كان الملف هو ملف zip (mcpack/mcaddon)
            if original_ext_lower in [".mcpack", ".mcaddon"] or content_type_final in ['application/zip', 'application/octet-stream']:
                with zipfile.ZipFile(BytesIO(mod_bytes), 'r') as zipf:
                    manifest_files = [f for f in zipf.namelist() if f.endswith('manifest.json')]
                    if manifest_files:
                        # استخراج أول ملف manifest.json
                        manifest_content = zipf.read(manifest_files[0]).decode('utf-8', errors='ignore')
                        manifest_data = json.loads(manifest_content)

                        # البحث عن الإصدار في ملف manifest.json
                        if 'header' in manifest_data and 'version' in manifest_data['header']:
                            version_array = manifest_data['header']['version']
                            if isinstance(version_array, list) and len(version_array) >= 2:
                                extracted_version_str = '.'.join(str(v) for v in version_array)
                                update_status(f"تم استخراج الإصدار من ملف manifest.json: {extracted_version_str}")
        except Exception as e:
            update_status(f"خطأ أثناء محاولة استخراج الإصدار من محتوى المود: {e}")

        public_url = upload_to_supabase(MOD_BUCKET, mod_bytes, final_upload_filename, target_upload_content_type)
        return public_url, mod_bytes, extracted_version_str

    except requests.exceptions.RequestException as e:
        update_status(f"Download error for {download_url or initial_page_url}: {e}")
    except ValueError as e:
        update_status(f"Validation Error: {e}")
    except Exception as e:
        update_status(f"Unexpected error in process_mod: {e}")
        update_status(f"Traceback: {traceback.format_exc()}")
    return None, None


def generate_description_task(mod_name, mod_category, scraped_text, manual_features):
    global gemini_model, current_gemini_key_index, GEMINI_CLIENT_OK
    if not GEMINI_CLIENT_OK:
        update_status("!!! Gemini client not configured. Cannot generate description.")
        if 'generate_desc_button' in globals() and generate_desc_button.winfo_exists():
            generate_desc_button.config(state=tk.NORMAL)
        return

    # Determine primary context: manual_features or scraped_text (full description)
    primary_context_source = ""
    if manual_features:
        primary_context_source = "The user has provided the following key features. Base the description primarily on these:\n\"\"\"\n" + manual_features + "\n\"\"\"\n"
        if scraped_text: # Offer scraped_text (full description) as supplementary
            primary_context_source += "\nAdditionally, the following full description was extracted and can be used for supplementary details if relevant:\n\"\"\"\n" + scraped_text[:2000] + "\n\"\"\"\n"
    elif scraped_text: # No manual features, scraped_text (full description) is primary
        primary_context_source = "Based on the following full mod description and features extracted from the source:\n\"\"\"\n" + scraped_text[:2500] + "\n\"\"\"\n" # Increased limit for full desc
    else:
        update_status("!!! No features or scraped text provided for description generation.")
        if 'generate_desc_button' in globals() and generate_desc_button.winfo_exists():
            generate_desc_button.config(state=tk.NORMAL)
        messagebox.showwarning("Input Required", "Please provide mod features or scrape an article to generate a description.")
        return

    prompt = f"""
    You are an expert Minecraft content writer. Your task is to write an engaging, unique, and detailed description (around 100-200 words) for a Minecraft content piece in English, maintaining a human-like and natural tone.

    **Content Name:** {mod_name if mod_name else "Not specified"}
    **Content Type:** {mod_category}

    **Source Information (Full Mod Description and/or Key Features):**
    {primary_context_source}
    **Instructions:**
    - **Use the provided 'Source Information' (which contains the full mod description and/or user-provided features) as the PRIMARY basis for your writing.**
    - **Create a NEW, UNIQUE, and engaging description.** Do NOT simply copy or slightly rephrase the original description.
    - **You MUST explicitly mention the mod's key features** within your new description. Refer to the 'Source Information' to identify these features.
    - Highlight the content's value to players and how it enhances gameplay.
    - Use natural and fluent English. Avoid repetition.
    - The output must be in English only.
    - Do not include the content name or type at the beginning unless it fits naturally.
    - Do not add any headings like "Description:" or similar; provide only the descriptive text.
    - If the 'Source Information' is minimal, focus on what is provided to create the best possible description, ensuring features are mentioned if available.
    """
    # ... (rest of generate_description_task remains largely the same for retries and GUI update) ...
    retries = 0
    success = False
    generated_desc = ""
    while retries < MAX_GEMINI_RETRIES and not success:
        if not GEMINI_CLIENT_OK:
            update_status("!!! Gemini client not configured. Cannot generate description.")
            break
        try:
            update_status(f"إرسال الطلب إلى Gemini (باستخدام مفتاح {current_gemini_key_index})...")
            response = gemini_model.generate_content(prompt)
            generated_desc = response.text.strip()
            update_status("تم استلام الرد من Gemini.")
            success = True
        except Exception as e:
            error_message = f"!!! خطأ أثناء إنشاء الوصف بواسطة Gemini (المفتاح {current_gemini_key_index}): {e}"
            update_status(error_message)
            if hasattr(e, 'response') and hasattr(e.response, 'text'):
                 update_status(f"Gemini API Response Text: {e.response.text}")
            update_status(f"Traceback: {traceback.format_exc()}")
            is_rate_limit = "rate limit" in str(e).lower() or "quota" in str(e).lower() or "resource has been exhausted" in str(e).lower()
            if is_rate_limit:
                update_status(f"!!! تم الوصول إلى حد المعدل للمفتاح {current_gemini_key_index}. محاولة استخدام المفتاح التالي...")
                retries += 1
                if not configure_gemini_client(current_gemini_key_index + 1):
                    messagebox.showerror("خطأ Gemini", "فشل إنشاء الوصف: تم استنفاد جميع مفاتيح API أو فشل التكوين.")
                    break
            else:
                messagebox.showerror("خطأ Gemini", f"فشل إنشاء الوصف: {e}")
                break
    if success:
        if 'window' in globals() and window.winfo_exists():
            window.after(0, auto_populate_text_widget, publish_desc_text, generated_desc)
            update_status("--> تم تحديث حقل الوصف بالوصف المُنشأ بواسطة AI.")
        else:
            update_status(f"الوصف المُنشأ (للسجل): {generated_desc}")
    elif not GEMINI_CLIENT_OK:
         update_status("!!! فشل إنشاء الوصف لأن عميل Gemini غير متاح.")
    if 'window' in globals() and window.winfo_exists():
        window.after(0, lambda: generate_desc_button.config(state=tk.NORMAL) if 'generate_desc_button' in globals() and generate_desc_button.winfo_exists() else None)

# --- (The rest of the file: upload_to_supabase, extract_mod_download_link, scrape_9minecraft_article, extract_data_with_gemini_task, GUI setup, etc. remains largely the same, but ensure all function calls are correct after these changes)

# Make sure handle_add_image_url, handle_add_local_image, and handle_process_upload_selected are correctly defined and called in the GUI setup.
# The GUI setup for image_manage_frame should now call handle_add_image_url and handle_add_local_image without lambda if they don't need arguments from the lambda scope.
# And handle_process_upload_selected for the process button.

# --- GUI Setup (Ensure button commands are updated) ---
# ... (previous GUI setup code) ...

# In __main__ where image_manage_frame buttons are created:
# add_image_url_button = ttk.Button(image_input_frame, text="إضافة URL", command=handle_add_image_url)
# add_local_image_button = ttk.Button(image_input_frame, text="إضافة من القرص", command=handle_add_local_image)
# process_upload_images_button = ttk.Button(image_manage_frame, text="معالجة ورفع الصور المعروضة", command=handle_process_upload_selected)

# ... (rest of the GUI setup and __main__)
# Ensure all other parts of the script are included below.
# The following is a placeholder for the rest of your script.
# You would merge the above changes into your existing full script.

# --- Placeholder for the rest of the script ---

def call_phosus_api(enhancement_type: str, image_bytes: bytes, original_content_type: str):
    """Calls the Phosus API for image enhancement."""
    api_key = None
    endpoint = None
    files = {'image': image_bytes}
    headers = {}

    if enhancement_type == "auto_enhance":
        api_key = PHOSUS_AUTO_ENHANCE_API_KEY
        endpoint = PHOSUS_AUTO_ENHANCE_ENDPOINT
        headers['X-Phosus-Key'] = api_key
    elif enhancement_type == "super_resolution":
        api_key = PHOSUS_SUPER_RES_API_KEY
        endpoint = PHOSUS_SUPER_RES_ENDPOINT
        headers['X-Phosus-Key'] = api_key
        # Super resolution might have specific parameters, e.g., scale factor
        # For now, we assume the API handles it or uses defaults.
    else:
        raise ValueError(f"Unknown Phosus enhancement type: {enhancement_type}")

    if not api_key or not endpoint:
        raise ValueError(f"API key or endpoint not configured for {enhancement_type}")

    update_status(f"Calling Phosus API: {endpoint} for {enhancement_type}")

    # Phosus API might require Content-Type in the multipart form-data,
    # or as a header for the request itself. Assuming 'files' dict handles it.
    # If not, might need to pass headers={'Content-Type': original_content_type} to requests.post

    try:
        response = requests.post(endpoint, files=files, headers=headers, timeout=60) # 60s timeout for enhancement
        response.raise_for_status() # Raises HTTPError for bad responses (4XX or 5XX)

        # Check response content type
        response_content_type = response.headers.get('Content-Type', '').lower()
        if not response_content_type.startswith('image/'):
            error_detail = response.text[:500] if response.text else "No error detail."
            raise ValueError(f"Phosus API did not return an image. Status: {response.status_code}. Content-Type: {response_content_type}. Detail: {error_detail}")

        update_status(f"Phosus API call successful (Status: {response.status_code}).")
        return response.content
    except requests.exceptions.HTTPError as http_err:
        error_body = http_err.response.text[:500] if http_err.response and http_err.response.text else "No response body."
        update_status(f"!!! Phosus HTTP Error: {http_err}. Response: {error_body}")
        raise Exception(f"Phosus API HTTP Error: {http_err.response.status_code} - {error_body}") from http_err
    except requests.exceptions.RequestException as req_err:
        update_status(f"!!! Phosus Request Error: {req_err}")
        raise Exception(f"Phosus API Request Error: {req_err}") from req_err
    except Exception as e:
        update_status(f"!!! Unexpected error in call_phosus_api: {e}")
        raise


# All other functions (clear_all_app_fields, paste_into_widget, copy_from_widget,
# handle_process_mod, process_mod_task, handle_process_bp_rp, download_file_content,
# process_bp_rp_task, process_and_get_supabase_urls, handle_publish_mod, publish_mod_task,
# handle_generate_description, scrape_9minecraft_article, extract_data_with_gemini_task,
# handle_scrape_article, scrape_article_task_gemini, update_gui_with_gemini_data,
# incomplete mod handling, batch processing, and the full __main__ GUI layout)
# should follow here, ensuring that the modifications made above are correctly integrated
# and that any function calls to the modified functions are appropriate.

# For brevity, I'm not repeating the entire 2000+ lines of the original script here.
# The key is to integrate the new/modified functions for image handling,

        # Get Content-Type and Filename from headers
        content_type = response.headers.get('content-type', 'application/octet-stream').split(';')[0]
        content_disposition = response.headers.get('content-disposition')
        if content_disposition:
            update_status(f"Parsing CD for {file_type_for_log}: {content_disposition}")
            filename_match = re.search(r'filename\*=UTF-8\'\'([\S]+)', content_disposition, flags=re.IGNORECASE)
            if filename_match:
                original_filename = requests.utils.unquote(filename_match.group(1)) # Decode URL encoding
                update_status(f"Filename (UTF-8) for {file_type_for_log}: {original_filename}")
            elif 'filename=' in content_disposition:
                fn_part = content_disposition.split('filename=')[1]
                original_filename = fn_part.split(';')[0].strip('"\' ')
                update_status(f"Filename (basic) for {file_type_for_log}: {original_filename}")
        else:
             # Try to guess from URL if no CD header
             parsed_path = urlparse(direct_download_url).path
             if parsed_path and parsed_path != '/':
                 original_filename = os.path.basename(parsed_path)
                 update_status(f"Guessed filename for {file_type_for_log} from URL: {original_filename}")


        # Basic sanitization and ensure .mcpack extension if possible
        sanitized_base, _ = os.path.splitext(sanitize_filename(original_filename))
        if not sanitized_base: sanitized_base = f"{file_type_for_log.lower()}_pack" # Fallback base name
        original_filename = f"{sanitized_base}.mcpack" # Force .mcpack extension

    return content_bytes, original_filename, content_type


def process_bp_rp_task(bp_page_url, rp_page_url, suggested_mcaddon_name):
    """Downloads BP and RP from URLs, zips them into an mcaddon, and uploads."""
    try:
        # 1. Extract direct download links
        update_status("Extracting BP download link...")
        bp_direct_url = extract_mod_download_link(bp_page_url)
        update_status("Extracting RP download link...")
        rp_direct_url = extract_mod_download_link(rp_page_url)

        # 2. Download content
        update_status(f"Downloading BP content from: {bp_direct_url}")
        bp_bytes, bp_filename, _ = download_file_content(bp_direct_url, "BP")
        update_status(f"Downloading RP content from: {rp_direct_url}")
        rp_bytes, rp_filename, _ = download_file_content(rp_direct_url, "RP")

        if not bp_bytes or not rp_bytes:
            raise ValueError("Failed to download BP or RP content.")

        # Ensure filenames are different if they somehow ended up the same
        if bp_filename == rp_filename:
            rp_filename = f"resource_{rp_filename}"
            update_status(f"Warning: BP and RP filenames were identical. Renamed RP to: {rp_filename}")

        # 3. Create .mcaddon (zip) in memory
        mcaddon_filename_base = sanitize_filename(suggested_mcaddon_name)
        mcaddon_filename = f"{mcaddon_filename_base}.mcaddon"
        update_status(f"Creating in-memory zip file: {mcaddon_filename}")
        zip_buffer = BytesIO()
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zipf:
            update_status(f"Adding '{bp_filename}' to zip...")
            zipf.writestr(bp_filename, bp_bytes)
            update_status(f"Adding '{rp_filename}' to zip...")
            zipf.writestr(rp_filename, rp_bytes)
        mcaddon_bytes = zip_buffer.getvalue()
        update_status(f"Created '{mcaddon_filename}' in memory ({len(mcaddon_bytes) / (1024*1024):.2f} MB).")

        # Check combined size
        max_size_bytes = MAX_MOD_SIZE_MB * 1024 * 1024
        if len(mcaddon_bytes) > max_size_bytes:
             raise ValueError(f"Combined mcaddon size ({len(mcaddon_bytes) / (1024*1024):.2f} MB) exceeds limit.")

        # محاولة استخراج الإصدار من ملفات المود
        mod_version_from_content = None
        try:
            # فحص ملفات المود للبحث عن ملف manifest.json
            with zipfile.ZipFile(BytesIO(mcaddon_bytes), 'r') as zipf:
                manifest_files = [f for f in zipf.namelist() if f.endswith('manifest.json')]
                if manifest_files:
                    # استخراج أول ملف manifest.json
                    manifest_content = zipf.read(manifest_files[0]).decode('utf-8', errors='ignore')
                    manifest_data = json.loads(manifest_content)

                    # البحث عن الإصدار في ملف manifest.json
                    if 'header' in manifest_data and 'version' in manifest_data['header']:
                        version_array = manifest_data['header']['version']
                        if isinstance(version_array, list) and len(version_array) >= 2:
                            mod_version_from_content = '.'.join(str(v) for v in version_array)
                            update_status(f"تم استخراج الإصدار من ملف manifest.json: {mod_version_from_content}")
        except Exception as e:
            update_status(f"خطأ أثناء محاولة استخراج الإصدار من محتوى المود: {e}")

        # 4. Upload the .mcaddon file
        update_status(f"Uploading '{mcaddon_filename}' to Supabase...")
        # Use 'application/octet-stream' for mcaddon to treat it as a specific file type
        final_url = upload_to_supabase(MOD_BUCKET, mcaddon_bytes, mcaddon_filename, 'application/octet-stream')

        # إذا تم استخراج الإصدار، قم بتحديث حقل الإصدار
        if mod_version_from_content and 'publish_version_entry' in globals() and publish_version_entry.winfo_exists():
            auto_populate_field(publish_version_entry, mod_version_from_content)
            update_status(f"تم تحديث حقل الإصدار تلقائيًا: {mod_version_from_content}")

        # 5. Update GUI
        status_msg = f"--> رابط المود المدمج النهائي (Supabase): {final_url}"
        update_status(status_msg)
        update_mod_result(final_url)
        # Auto-populate URL
        if 'publish_mod_url_entry' in globals():
            auto_populate_field(publish_mod_url_entry, final_url)
            update_status("تم ملء حقل رابط تحميل المود تلقائيًا بالملف المدمج.")
        # Auto-populate Size
        if 'publish_size_entry' in globals():
            formatted_size = format_size(len(mcaddon_bytes))
            # Enable, update, disable again
            publish_size_entry.config(state=tk.NORMAL)
            auto_populate_field(publish_size_entry, formatted_size)
            publish_size_entry.config(state=tk.DISABLED)
            update_status(f"تم ملء حقل الحجم تلقائيًا للملف المدمج: {formatted_size}")

    except Exception as e:
        error_msg = f"!!! فشل دمج ومعالجة BP/RP: {e}"
        update_status(error_msg)
        update_status(f"Traceback: {traceback.format_exc()}") # Log full traceback for debugging
    finally:
        # Re-enable button safely
        if 'process_bp_rp_button' in globals() and process_bp_rp_button.winfo_exists():
            process_bp_rp_button.config(state=tk.NORMAL)

def process_bp_rp_local_task(bp_file_path, rp_file_path, suggested_mcpack_name):
    """Processes local BP and RP files, merges them into a single mcpack file, and uploads."""
    try:
        update_status("\n--- بدء معالجة ودمج ملفات BP و RP المحلية ---")

        # 1. Read the BP and RP files
        update_status("قراءة ملف BP...")
        with open(bp_file_path, 'rb') as f:
            bp_bytes = f.read()

        update_status("قراءة ملف RP...")
        with open(rp_file_path, 'rb') as f:
            rp_bytes = f.read()

        if not bp_bytes or not rp_bytes:
            raise ValueError("أحد الملفات أو كلاهما فارغ.")

        update_status(f"تم قراءة الملفات بنجاح. حجم BP: {format_size(len(bp_bytes))}, حجم RP: {format_size(len(rp_bytes))}")

        # 2. Extract content if the files are .mcaddon or .mcpack (which are zip files)
        bp_content = None
        rp_content = None
        bp_filename = os.path.basename(bp_file_path)
        rp_filename = os.path.basename(rp_file_path)

        # Process BP file
        bp_ext = os.path.splitext(bp_file_path)[1].lower()
        if bp_ext in ['.mcaddon', '.mcpack']:
            update_status(f"استخراج محتوى ملف BP ({bp_ext})...")
            try:
                with zipfile.ZipFile(BytesIO(bp_bytes), 'r') as zipf:
                    # Check if this is a direct BP pack or contains a BP pack inside
                    manifest_files = [f for f in zipf.namelist() if f.endswith('manifest.json')]
                    if manifest_files:
                        # This is likely a direct BP pack
                        update_status("تم العثور على ملف manifest.json في ملف BP. استخدام الملف كما هو.")
                        bp_content = bp_bytes
                    else:
                        # This might be an mcaddon containing both BP and RP
                        # Look for .mcpack files inside
                        mcpack_files = [f for f in zipf.namelist() if f.lower().endswith('.mcpack')]
                        if mcpack_files:
                            # Find the BP pack
                            bp_pack = None
                            for pack_file in mcpack_files:
                                # Extract and check if it's a BP
                                pack_data = zipf.read(pack_file)
                                try:
                                    with zipfile.ZipFile(BytesIO(pack_data), 'r') as pack_zipf:
                                        pack_manifest_files = [f for f in pack_zipf.namelist() if f.endswith('manifest.json')]
                                        if pack_manifest_files:
                                            manifest_content = pack_zipf.read(pack_manifest_files[0]).decode('utf-8', errors='ignore')
                                            manifest_data = json.loads(manifest_content)
                                            # Check if it's a BP by looking at the modules
                                            if 'modules' in manifest_data:
                                                for module in manifest_data['modules']:
                                                    if module.get('type') == 'data':
                                                        bp_pack = pack_data
                                                        bp_filename = os.path.basename(pack_file)
                                                        update_status(f"تم العثور على حزمة BP داخل ملف mcaddon: {pack_file}")
                                                        break
                                except Exception as e:
                                    update_status(f"خطأ أثناء فحص ملف {pack_file}: {e}")

                            if bp_pack:
                                bp_content = bp_pack
                            else:
                                update_status("لم يتم العثور على حزمة BP داخل ملف mcaddon. استخدام الملف الأصلي.")
                                bp_content = bp_bytes
                        else:
                            update_status("لم يتم العثور على ملفات mcpack داخل ملف mcaddon. استخدام الملف الأصلي.")
                            bp_content = bp_bytes
            except zipfile.BadZipFile:
                update_status(f"!!! خطأ: ملف BP ليس ملف ZIP صالح. استخدام الملف الأصلي.")
                bp_content = bp_bytes
        else:
            # Not a zip file, use as is
            update_status(f"ملف BP ليس بتنسيق mcaddon أو mcpack. استخدام الملف الأصلي.")
            bp_content = bp_bytes

        # Process RP file
        rp_ext = os.path.splitext(rp_file_path)[1].lower()
        if rp_ext in ['.mcaddon', '.mcpack']:
            update_status(f"استخراج محتوى ملف RP ({rp_ext})...")
            try:
                with zipfile.ZipFile(BytesIO(rp_bytes), 'r') as zipf:
                    # Check if this is a direct RP pack or contains an RP pack inside
                    manifest_files = [f for f in zipf.namelist() if f.endswith('manifest.json')]
                    if manifest_files:
                        # This is likely a direct RP pack
                        update_status("تم العثور على ملف manifest.json في ملف RP. استخدام الملف كما هو.")
                        rp_content = rp_bytes
                    else:
                        # This might be an mcaddon containing both BP and RP
                        # Look for .mcpack files inside
                        mcpack_files = [f for f in zipf.namelist() if f.lower().endswith('.mcpack')]
                        if mcpack_files:
                            # Find the RP pack
                            rp_pack = None
                            for pack_file in mcpack_files:
                                # Extract and check if it's an RP
                                pack_data = zipf.read(pack_file)
                                try:
                                    with zipfile.ZipFile(BytesIO(pack_data), 'r') as pack_zipf:
                                        pack_manifest_files = [f for f in pack_zipf.namelist() if f.endswith('manifest.json')]
                                        if pack_manifest_files:
                                            manifest_content = pack_zipf.read(pack_manifest_files[0]).decode('utf-8', errors='ignore')
                                            manifest_data = json.loads(manifest_content)
                                            # Check if it's an RP by looking at the modules
                                            if 'modules' in manifest_data:
                                                for module in manifest_data['modules']:
                                                    if module.get('type') == 'resources':
                                                        rp_pack = pack_data
                                                        rp_filename = os.path.basename(pack_file)
                                                        update_status(f"تم العثور على حزمة RP داخل ملف mcaddon: {pack_file}")
                                                        break
                                except Exception as e:
                                    update_status(f"خطأ أثناء فحص ملف {pack_file}: {e}")

                            if rp_pack:
                                rp_content = rp_pack
                            else:
                                update_status("لم يتم العثور على حزمة RP داخل ملف mcaddon. استخدام الملف الأصلي.")
                                rp_content = rp_bytes
                        else:
                            update_status("لم يتم العثور على ملفات mcpack داخل ملف mcaddon. استخدام الملف الأصلي.")
                            rp_content = rp_bytes
            except zipfile.BadZipFile:
                update_status(f"!!! خطأ: ملف RP ليس ملف ZIP صالح. استخدام الملف الأصلي.")
                rp_content = rp_bytes
        else:
            # Not a zip file, use as is
            update_status(f"ملف RP ليس بتنسيق mcaddon أو mcpack. استخدام الملف الأصلي.")
            rp_content = rp_bytes

        # Ensure we have content for both BP and RP
        if not bp_content or not rp_content:
            raise ValueError("فشل استخراج محتوى أحد الملفات أو كلاهما.")

        # Ensure filenames are different
        if bp_filename == rp_filename:
            rp_filename = f"resource_{rp_filename}"
            update_status(f"تحذير: أسماء ملفات BP و RP متطابقة. تمت إعادة تسمية RP إلى: {rp_filename}")

        # 3. Create a single .mcpack file containing both BP and RP
        mcpack_filename_base = sanitize_filename(suggested_mcpack_name)
        mcpack_filename = f"{mcpack_filename_base}.mcpack"
        update_status(f"إنشاء ملف mcpack في الذاكرة: {mcpack_filename}")

        zip_buffer = BytesIO()
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zipf:
            update_status(f"إضافة '{bp_filename}' إلى الملف المضغوط...")
            zipf.writestr(bp_filename, bp_content)
            update_status(f"إضافة '{rp_filename}' إلى الملف المضغوط...")
            zipf.writestr(rp_filename, rp_content)

        mcpack_bytes = zip_buffer.getvalue()
        update_status(f"تم إنشاء '{mcpack_filename}' في الذاكرة ({len(mcpack_bytes) / (1024*1024):.2f} MB).")

        # Check combined size
        max_size_bytes = MAX_MOD_SIZE_MB * 1024 * 1024
        if len(mcpack_bytes) > max_size_bytes:
            raise ValueError(f"حجم الملف المدمج ({len(mcpack_bytes) / (1024*1024):.2f} MB) يتجاوز الحد المسموح به.")

        # محاولة استخراج الإصدار من ملفات المود
        mod_version_from_content = None
        try:
            # فحص ملفات المود للبحث عن ملف manifest.json
            with zipfile.ZipFile(BytesIO(mcpack_bytes), 'r') as zipf:
                # First try to find manifest in the BP file
                with zipfile.ZipFile(BytesIO(bp_content), 'r') as bp_zipf:
                    manifest_files = [f for f in bp_zipf.namelist() if f.endswith('manifest.json')]
                    if manifest_files:
                        # استخراج أول ملف manifest.json
                        manifest_content = bp_zipf.read(manifest_files[0]).decode('utf-8', errors='ignore')
                        manifest_data = json.loads(manifest_content)

                        # البحث عن الإصدار في ملف manifest.json
                        if 'header' in manifest_data and 'version' in manifest_data['header']:
                            version_array = manifest_data['header']['version']
                            if isinstance(version_array, list) and len(version_array) >= 2:
                                mod_version_from_content = '.'.join(str(v) for v in version_array)
                                update_status(f"تم استخراج الإصدار من ملف manifest.json في BP: {mod_version_from_content}")
        except Exception as e:
            update_status(f"خطأ أثناء محاولة استخراج الإصدار من محتوى المود: {e}")

        # 4. Upload the .mcpack file
        update_status(f"رفع '{mcpack_filename}' إلى Supabase...")
        final_url = upload_to_supabase(MOD_BUCKET, mcpack_bytes, mcpack_filename, 'application/octet-stream')

        # إذا تم استخراج الإصدار، قم بتحديث حقل الإصدار
        if mod_version_from_content and 'publish_version_entry' in globals() and publish_version_entry.winfo_exists():
            auto_populate_field(publish_version_entry, mod_version_from_content)
            update_status(f"تم تحديث حقل الإصدار تلقائيًا: {mod_version_from_content}")

        # 5. Update GUI
        status_msg = f"--> رابط المود المدمج النهائي (Supabase): {final_url}"
        update_status(status_msg)
        update_mod_result(final_url)

        # Auto-populate URL
        if 'publish_mod_url_entry' in globals():
            auto_populate_field(publish_mod_url_entry, final_url)
            update_status("تم ملء حقل رابط تحميل المود تلقائيًا بالملف المدمج.")

        # Auto-populate Size
        if 'publish_size_entry' in globals():
            formatted_size = format_size(len(mcpack_bytes))
            # Enable, update, disable again
            publish_size_entry.config(state=tk.NORMAL)
            auto_populate_field(publish_size_entry, formatted_size)
            publish_size_entry.config(state=tk.DISABLED)
            update_status(f"تم ملء حقل الحجم تلقائيًا للملف المدمج: {formatted_size}")

        update_status("--- تمت معالجة ودمج ملفات BP و RP المحلية بنجاح ---")

    except Exception as e:
        error_msg = f"!!! فشل معالجة ودمج ملفات BP و RP المحلية: {e}"
        update_status(error_msg)
        update_status(f"Traceback: {traceback.format_exc()}")
    finally:
        # Re-enable button safely
        if 'process_bp_rp_local_button' in globals() and process_bp_rp_local_button.winfo_exists():
            process_bp_rp_local_button.config(state=tk.NORMAL)



# --- Global variables for BP/RP local file paths ---
bp_file_path = None
rp_file_path = None

# --- Handler for BP/RP Combining from URLs ---
def handle_process_bp_rp():
    """Handles the 'Combine BP/RP' button click for URL-based files."""
    bp_url = bp_url_entry.get().strip()
    rp_url = rp_url_entry.get().strip()
    # Use the mod name from the publish section as the suggested filename base
    suggested_name = publish_name_entry.get().strip()

    if not bp_url or not rp_url:
        messagebox.showerror("خطأ", "الرجاء إدخال رابط صفحة BP ورابط صفحة RP.")
        return
    if not suggested_name:
        # Fallback if publish_name_entry is empty
        suggested_name = "combined_mod"
        update_status("تحذير: اسم المود في قسم النشر فارغ. سيتم استخدام 'combined_mod' كاسم افتراضي للملف.")

    # Basic URL validation
    parsed_bp_url = urlparse(bp_url)
    parsed_rp_url = urlparse(rp_url)
    if not all([parsed_bp_url.scheme, parsed_bp_url.netloc, parsed_rp_url.scheme, parsed_rp_url.netloc]):
        messagebox.showerror("خطأ", "أحد روابط BP/RP المدخلة أو كلاهما لا يبدو كرابط URL صالح.")
        return

    update_status(f"بدء دمج BP من: {bp_url} و RP من: {rp_url} بالاسم المقترح: {suggested_name}")
    process_bp_rp_button.config(state=tk.DISABLED)
    # The process_bp_rp_task function already exists and handles threading.
    run_in_thread(process_bp_rp_task, bp_url, rp_url, suggested_name)

# --- Handlers for BP/RP Local File Selection ---
def handle_select_bp_file():
    """Handles the selection of a local BP file."""
    global bp_file_path
    filepath = filedialog.askopenfilename(
        title="اختر ملف BP (mcpack/mcaddon)",
        filetypes=[("Minecraft Mod Files", "*.mcpack *.mcaddon"), ("All files", "*.*")]
    )
    if not filepath:
        update_status("تم إلغاء اختيار ملف BP.")
        return

    bp_file_path = filepath
    update_status(f"تم اختيار ملف BP: {filepath}")

    # Update the entry field
    if 'bp_file_path_entry' in globals() and bp_file_path_entry.winfo_exists():
        bp_file_path_entry.config(state=tk.NORMAL)
        bp_file_path_entry.delete(0, tk.END)
        bp_file_path_entry.insert(0, filepath)
        bp_file_path_entry.config(state=tk.DISABLED)

def handle_select_rp_file():
    """Handles the selection of a local RP file."""
    global rp_file_path
    filepath = filedialog.askopenfilename(
        title="اختر ملف RP (mcpack/mcaddon)",
        filetypes=[("Minecraft Mod Files", "*.mcpack *.mcaddon"), ("All files", "*.*")]
    )
    if not filepath:
        update_status("تم إلغاء اختيار ملف RP.")
        return

    rp_file_path = filepath
    update_status(f"تم اختيار ملف RP: {filepath}")

    # Update the entry field
    if 'rp_file_path_entry' in globals() and rp_file_path_entry.winfo_exists():
        rp_file_path_entry.config(state=tk.NORMAL)
        rp_file_path_entry.delete(0, tk.END)
        rp_file_path_entry.insert(0, filepath)
        rp_file_path_entry.config(state=tk.DISABLED)

def clear_bp_file_path():
    """Clears the BP file path."""
    global bp_file_path
    bp_file_path = None
    if 'bp_file_path_entry' in globals() and bp_file_path_entry.winfo_exists():
        bp_file_path_entry.config(state=tk.NORMAL)
        bp_file_path_entry.delete(0, tk.END)
        bp_file_path_entry.config(state=tk.DISABLED)
    update_status("تم مسح مسار ملف BP.")

def clear_rp_file_path():
    """Clears the RP file path."""
    global rp_file_path
    rp_file_path = None
    if 'rp_file_path_entry' in globals() and rp_file_path_entry.winfo_exists():
        rp_file_path_entry.config(state=tk.NORMAL)
        rp_file_path_entry.delete(0, tk.END)
        rp_file_path_entry.config(state=tk.DISABLED)
    update_status("تم مسح مسار ملف RP.")

def handle_process_bp_rp_local():
    """Handles the 'Process Local BP/RP Files' button click."""
    global bp_file_path, rp_file_path

    # Check if files are selected
    if not bp_file_path or not rp_file_path:
        messagebox.showerror("خطأ", "الرجاء اختيار ملفي BP و RP أولاً.")
        return

    # Check if files exist
    if not os.path.exists(bp_file_path) or not os.path.exists(rp_file_path):
        messagebox.showerror("خطأ", "أحد الملفات المختارة أو كلاهما غير موجود.")
        return

    # Use the mod name from the publish section as the suggested filename base
    suggested_name = publish_name_entry.get().strip()
    if not suggested_name:
        # Fallback if publish_name_entry is empty
        suggested_name = "combined_mod"
        update_status("تحذير: اسم المود في قسم النشر فارغ. سيتم استخدام 'combined_mod' كاسم افتراضي للملف.")

    update_status(f"بدء معالجة ودمج الملفات المحلية: BP: {bp_file_path}, RP: {rp_file_path}")
    process_bp_rp_local_button.config(state=tk.DISABLED)

    # Run the processing task in a thread
    run_in_thread(process_bp_rp_local_task, bp_file_path, rp_file_path, suggested_name)


def extract_mcpeland_data(page_url: str):
    """
    Extracts download link and version from an mcpeland.io page,
    handling cases where download links are direct on the article page
    or require resolving an intermediary 'do=download' link.
    """
    update_status(f"Extracting mod data from MCPELAND: {page_url}")
    final_direct_download_url = None
    version_str = None
    headers = {'User-Agent': 'Mozilla/5.0'}

    try:
        # --- 1. Fetch the initial page (article or do=download) ---
        response_initial = requests.get(page_url, headers=headers, timeout=30, allow_redirects=True)
        response_initial.raise_for_status()
        current_processing_url = response_initial.url # URL after initial redirects
        soup_current = BeautifulSoup(response_initial.text, 'html.parser')
        update_status(f"Initial page fetched: {current_processing_url}")

        # --- 2. Extract Version (if current page is likely an article page) ---
        # Attempt to extract version regardless, but it's more likely on an article page.
        version_section_title = soup_current.find(lambda tag: tag.name in ['div', 'span', 'h2', 'h3'] and "Supported Minecraft Versions" in tag.get_text(strip=True))
        if version_section_title:
            next_element = version_section_title.find_next_sibling()
            if next_element:
                version_text_candidates = re.findall(r'\d+\.\d+(?:\.\d+)?', next_element.get_text())
                if version_text_candidates:
                    version_str = version_text_candidates[0]
                    update_status(f"Found MCPELAND version: {version_str}")
        if not version_str:
            # Fallback: try to get version from title if still not found
            title_tag = soup_current.find('title')
            if title_tag and title_tag.string:
                version_match_title = re.search(r'(\d+\.\d+(?:\.\d+)?(?:[\s-]\d+\.\d+(?:\.\d+)?)?)', title_tag.string)
                if version_match_title:
                    version_str = version_match_title.group(1).split('-')[0].split(' ')[0].strip() # Take the first part
                    update_status(f"Found MCPELAND version from title tag: {version_str}")
            if not version_str:
                 update_status("Could not extract version from MCPELAND page.")


        # --- 3. Find the Download Link ---
        # Scenario A: The initial page_url (or resolved current_processing_url) is a 'do=download' link
        # that *should* directly serve the file or redirect to it via meta-refresh.
        is_direct_download_trigger = "index.php?do=download&id=" in current_processing_url.lower()

        if is_direct_download_trigger:
            update_status(f"Processing as MCPELAND 'do=download' trigger: {current_processing_url}")
            # Check for meta-refresh first
            meta_refresh = soup_current.find("meta", attrs={"http-equiv": re.compile(r"refresh", re.I)})
            if meta_refresh and meta_refresh.get("content"):
                content_attr = meta_refresh["content"]
                match_meta_url = re.search(r"(?:url|WWaitURL)\s*=\s*([^;'\"]+)", content_attr, re.I)
                if match_meta_url:
                    url_from_meta = match_meta_url.group(1).strip("'\" ")
                    # This URL from meta should be the direct file link
                    final_direct_download_url = urljoin(current_processing_url, url_from_meta)
                    update_status(f"Found direct file URL via meta refresh: {final_direct_download_url}")
                    # Perform a HEAD request to confirm it's not HTML
                    try:
                        head_response = requests.head(final_direct_download_url, headers=headers, timeout=10, allow_redirects=True)
                        head_content_type = head_response.headers.get('content-type', '').split(';')[0].strip().lower()
                        if 'text/html' in head_content_type:
                            update_status(f"!!! Warning: Meta refresh URL {final_direct_download_url} is still HTML. Discarding.")
                            final_direct_download_url = None # Discard if it's HTML
                        else:
                            update_status(f"Meta refresh URL {final_direct_download_url} confirmed as non-HTML (Type: {head_content_type}).")
                    except requests.exceptions.RequestException as e_head:
                        update_status(f"!!! Warning: Could not HEAD check meta refresh URL {final_direct_download_url}: {e_head}. Assuming it's direct for now.")
                else:
                    update_status(f"Meta refresh tag found but no URL pattern matched: '{content_attr}'")

            # If no valid meta-refresh, the 'do=download' link itself might be the direct file.
            # This happens if mcpeland.io serves the file directly from such a URL.
            if not final_direct_download_url:
                content_type_initial = response_initial.headers.get('content-type', '').split(';')[0].strip().lower()
                if 'text/html' not in content_type_initial:
                    final_direct_download_url = current_processing_url # The URL itself is the file
                    update_status(f"Initial 'do=download' URL ({current_processing_url}) is non-HTML (Type: {content_type_initial}). Assuming direct file link.")
                else:
                    # It was a 'do=download' link, but it served HTML and had no valid meta-refresh.
                    # This means it might have redirected to an article page, which is handled next.
                    update_status(f"'do=download' link {current_processing_url} served HTML without valid meta-refresh. Will now parse this HTML as an article page.")
                    # soup_current is already the soup of this HTML page.

        # Scenario B: The current page is an article page (either initially, or after a 'do=download' resolved to HTML)
        # We need to find the actual download link *on this article page*.
        if not final_direct_download_url: # If we haven't found a direct link yet
            update_status(f"Parsing as MCPELAND article page for download links: {current_processing_url}")

            # Keywords to identify download links (add more if needed, including Russian)
            download_keywords = ["скачать", "download", ".mcaddon", ".mcpack"]

            candidate_links = []
            for a_tag in soup_current.find_all('a', href=True):
                href = a_tag['href']
                link_text = a_tag.get_text(strip=True).lower()

                # Check 1: Does the href contain the 'do=download&id=' pattern?
                if "index.php?do=download&id=" in href.lower():
                    # Check 2: Does the link text suggest it's a download?
                    if any(keyword in link_text for keyword in download_keywords):
                        full_url = urljoin(current_processing_url, href)
                        # Ensure it's on the same domain
                        if urlparse(full_url).netloc == urlparse(current_processing_url).netloc:
                            candidate_links.append(full_url)
                            update_status(f"Found potential direct download link on article page: {full_url} (Text: '{a_tag.get_text(strip=True)}')")

            if candidate_links:
                # Prioritize links that are different from the page_url we started with, if page_url was a do=download
                # This avoids picking the same intermediary link if it was passed in.
                if is_direct_download_trigger: # if initial page_url was a do=download type
                    preferred_links = [l for l in candidate_links if l != page_url]
                    if preferred_links:
                        final_direct_download_url = preferred_links[0]
                        update_status(f"Selected download link (different from initial 'do=download' trigger): {final_direct_download_url}")
                    elif candidate_links: # All candidates are same as initial trigger, pick first
                        final_direct_download_url = candidate_links[0]
                        update_status(f"Selected download link (same as initial 'do=download' trigger, might be the direct file): {final_direct_download_url}")
                else: # Initial page_url was an article page
                    final_direct_download_url = candidate_links[0] # Take the first one found
                    update_status(f"Selected download link from article page: {final_direct_download_url}")

                # Perform a HEAD request to confirm it's not HTML
                if final_direct_download_url:
                    try:
                        head_response = requests.head(final_direct_download_url, headers=headers, timeout=10, allow_redirects=True) # allow_redirects=True for safety
                        head_content_type = head_response.headers.get('content-type', '').split(';')[0].strip().lower()
                        if 'text/html' in head_content_type:
                            update_status(f"!!! Warning: Selected download link {final_direct_download_url} is HTML. Discarding.")
                            final_direct_download_url = None # Discard if it's HTML
                        else:
                            update_status(f"Selected download link {final_direct_download_url} confirmed as non-HTML (Type: {head_content_type}).")
                            # If redirects occurred during HEAD, use the final URL
                            if head_response.url != final_direct_download_url:
                                update_status(f"Download link {final_direct_download_url} redirected to {head_response.url} during HEAD check. Using final URL.")
                                final_direct_download_url = head_response.url
                    except requests.exceptions.RequestException as e_head_final:
                        update_status(f"!!! Warning: Could not HEAD check selected download link {final_direct_download_url}: {e_head_final}. Assuming it's direct for now.")
            else:
                update_status(f"No 'index.php?do=download&id=' links with download keywords found on article page: {current_processing_url}")


        if not final_direct_download_url:
             update_status(f"!!! FINAL: Could not extract a clear final direct download link from MCPELAND process starting with: {page_url}")

        return final_direct_download_url, version_str

    except requests.exceptions.RequestException as e:
        update_status(f"!!! MCPELAND page fetch/processing error for {page_url}: {e}")
    except Exception as e:
        update_status(f"!!! General error in extract_mcpeland_data for {page_url}: {e}")
        update_status(f"Traceback: {traceback.format_exc()}")
    return None, version_str


# --- NEW Synchronous Helper Function for Image Processing during Publish ---
def process_and_get_supabase_urls(urls_to_process: list[str], base_article_url: str = None) -> list[str]:
    """
    Synchronously processes a list of image URLs and returns their Supabase URLs.
    If processing fails for an image, its original URL might be returned or None.
    This runs on the main thread during publish, so keep it efficient.
    """
    if not STORAGE_CLIENT_OK:
        update_status("!!! Cannot process images: Supabase storage client not OK.")
        messagebox.showerror("خطأ في التخزين", "فشل الاتصال بـ Supabase Storage. لا يمكن معالجة الصور.")
        return urls_to_process # Return original URLs on critical error

    processed_urls = []
    total_images = len(urls_to_process)
    update_status(f"\n--- بدء معالجة {total_images} صورة للنشر ---")

    for i, image_url in enumerate(urls_to_process):
        if not image_url: # Check if the URL itself is empty/None from Gemini
            update_status(f"!!! تخطي رابط الصورة فارغ أو غير صالح (من Gemini) للصورة {i+1}: {image_url}")
            processed_urls.append(None)
            continue

        # The base_article_url is passed to process_image_url_only, which handles relative paths.
        update_status(f"--- معالجة الصورة {i+1}/{total_images} للنشر (الأصلي: {image_url[:60]}... ) ---")
        try:
            # process_image_url_only will attempt to make the URL absolute if needed,
            # then download, process, and upload it. It returns a Supabase URL or None.
            final_supabase_url = process_image_url_only(image_url, base_article_url)

            if final_supabase_url:
                status_msg = f"--> رابط الصورة المعالج: {final_supabase_url}"
                update_status(status_msg)
                processed_urls.append(final_supabase_url)
            else:
                # process_image_url_only returned None, indicating failure to process this image.
                update_status(f"!!! فشلت معالجة الصورة {i+1} ({image_url[:60]}...). لم يتم إرجاع رابط صالح من process_image_url_only.")
                processed_urls.append(None) # Append None to signify this image failed
        except Exception as e:
            update_status(f"!!! حدث خطأ استثنائي أثناء معالجة الصورة {i+1} ({image_url[:60]}...): {e}")
            # It's good practice to log the full traceback for unexpected errors during development/debugging
            # update_status(f"Traceback: {traceback.format_exc()}")
            processed_urls.append(None) # Append None on exception as well

    update_status(f"--- === اكتملت معالجة الصور للنشر === ---")
    # Filter out None values before returning. This list is used for DB insertion.
    final_urls = [url for url in processed_urls if url]
    return final_urls


# --- Publish Handler ---
def handle_publish_mod():
    if not APP_DB_CLIENT_OK: messagebox.showerror("خطأ", "فشل الاتصال بقاعدة بيانات التطبيق. لا يمكن النشر."); return

    # إضافة خيار لتفريغ الحقول بعد النشر
    clear_after_publish = messagebox.askyesno("تفريغ الحقول", "هل ترغب في تفريغ جميع الحقول بعد النشر؟")

    mod_name = publish_name_entry.get().strip()
    mod_desc = publish_desc_text.get("1.0", tk.END).strip()
    mod_desc_ar = publish_arabic_desc_text.get("1.0", tk.END).strip()  # إضافة الوصف العربي
    mod_category = category_combobox.get()
    primary_image_url = publish_primary_image_entry.get().strip()
    other_images_raw = publish_other_images_text.get("1.0", tk.END).strip()
    mod_download_url = publish_mod_url_entry.get().strip()
    mod_version = publish_version_entry.get().strip()
    # Read size from the disabled field by temporarily enabling it
    publish_size_entry.config(state=tk.NORMAL)
    mod_size = publish_size_entry.get().strip()
    publish_size_entry.config(state=tk.DISABLED)

    # Get creator information
    creator_name = publish_creator_name_entry.get().strip() if 'publish_creator_name_entry' in globals() and publish_creator_name_entry.winfo_exists() else ""
    creator_contact = publish_creator_contact_entry.get().strip() if 'publish_creator_contact_entry' in globals() and publish_creator_contact_entry.winfo_exists() else ""

    # Get custom social sites information from multiple entries
    creator_social_channels = []
    if 'custom_site_entries' in globals():
        for entry_data in custom_site_entries:
            site_name = entry_data['name_entry'].get().strip()
            site_url = entry_data['url_entry'].get().strip()

            # Skip placeholder text and empty entries
            if (site_name and site_name != "اسم الموقع" and
                site_url and site_url != "رابط الموقع"):
                creator_social_channels.append(f"{site_name}: {site_url}")

    creator_social_json = creator_social_channels if creator_social_channels else None


    if not mod_name: messagebox.showerror("خطأ", "اسم المود مطلوب."); return
    if not mod_download_url: messagebox.showerror("خطأ", "رابط تحميل المود مطلوب."); return
    if not mod_category: messagebox.showerror("خطأ", "الفئة مطلوبة."); return
    # --- NEW: Process images before preparing publish_data ---
    update_status("جمع روابط الصور من الحقول...")
    image_urls_from_gui = []
    if primary_image_url:
        image_urls_from_gui.append(primary_image_url)
    other_images = [url.strip() for url in other_images_raw.splitlines() if url.strip()]
    image_urls_from_gui.extend(other_images)

    if not image_urls_from_gui:
        if not messagebox.askyesno("تأكيد", "لا توجد روابط صور في الحقول. هل تريد المتابعة بالنشر بدون صور؟"):
            return
        processed_image_urls = [] # Empty list if no images provided and user confirms
    else:
        # Call the synchronous helper function to process images and get Supabase URLs
        processed_image_urls = process_and_get_supabase_urls(image_urls_from_gui)
        if not processed_image_urls:
             update_status("!!! تحذير: فشلت معالجة جميع الصور. سيتم النشر بدون روابط صور.")
             # Optionally ask user again if they want to proceed without images
             if not messagebox.askyesno("تأكيد", "فشلت معالجة جميع الصور. هل تريد المتابعة بالنشر بدون صور؟"):
                 publish_mod_button.config(state=tk.NORMAL) # Re-enable button if user cancels
                 return

    # Limit to 7 images total (primary + 6 others) using the processed URLs
    final_image_urls_for_db = processed_image_urls[:7]
    if len(processed_image_urls) > 7:
        update_status(f"تحذير: تم تجاهل {len(processed_image_urls) - 7} من روابط الصور المعالجة (الحد الأقصى 7).")

    publish_data = {
        "name": mod_name,
        "description": mod_desc if mod_desc else None,
        "description_ar": mod_desc_ar if mod_desc_ar else None,  # إضافة الوصف العربي
        "category": mod_category,
        "image_urls": final_image_urls_for_db if final_image_urls_for_db else None, # Use processed URLs
        "version": mod_version if mod_version else None,
        "size": mod_size if mod_size else None, # Add size to publish data
        "download_url": mod_download_url, # Mod URL is processed separately
        "creator_name": creator_name if creator_name else None,
        "creator_contact_info": creator_contact if creator_contact else None,
        "creator_social_channels": creator_social_json,
    }

    update_status("\n--- بدء نشر المود في التطبيق ---")
    update_status(f"البيانات: {json.dumps(publish_data, indent=2, ensure_ascii=False)}")
    publish_mod_button.config(state=tk.DISABLED)
    run_in_thread(publish_mod_task, publish_data, clear_after_publish)

def publish_mod_task(data_to_insert, clear_fields_after=False):
    try:
        update_status(f"DEBUG: البيانات المرسلة للنشر: {json.dumps(data_to_insert, indent=2, ensure_ascii=False)}") # لطباعة البيانات المرسلة
        insert_response = app_db_client.table(MODS_TABLE_NAME).insert(data_to_insert).execute()

        # --- DEBUGGING: Log a lot more about the response ---
        response_vars = "N/A"
        try:
            response_vars = vars(insert_response)
        except TypeError: # If vars() fails (e.g. not a standard object with __dict__)
            response_vars = str(insert_response)
        update_status(f"DEBUG: Supabase insert response object (raw): {response_vars}")

        response_data_content = getattr(insert_response, 'data', 'ATTRIBUTE_NOT_FOUND')
        response_error_content = getattr(insert_response, 'error', 'ATTRIBUTE_NOT_FOUND')
        response_status_code = getattr(insert_response, 'status_code', 'ATTRIBUTE_NOT_FOUND')

        update_status(f"DEBUG: Response Data: {response_data_content}")
        update_status(f"DEBUG: Response Error: {response_error_content}")
        update_status(f"DEBUG: Response Status Code: {response_status_code}")
        # --- END DEBUGGING ---

        # Original check: if hasattr(insert_response, 'data') and insert_response.data:
        # Let's make the check more robust: success is no error and a 2xx status code.
        # The 'data' attribute might be empty even on success for inserts depending on 'Prefer' headers or RLS.

        if response_error_content == 'ATTRIBUTE_NOT_FOUND' or response_error_content is None:
            # No error object, this is usually good. Check status code.
            if isinstance(response_status_code, int) and 200 <= response_status_code < 300:
                update_status("--> تم نشر المود بنجاح في قاعدة بيانات التطبيق!")
                update_status(f"البيانات المُرجعة (إذا وجدت): {response_data_content}")
                if clear_fields_after and 'window' in globals() and window.winfo_exists():
                    update_status("تفريغ جميع الحقول بعد النشر الناجح...")
                    window.after(0, clear_all_app_fields) # Use the new comprehensive clear function
            else:
                # No error object, but status code indicates failure.
                error_msg = f"فشل نشر المود. Status Code: {response_status_code}. Response Data: {response_data_content}"
                update_status(error_msg)
                raise Exception(error_msg)
        else:
            # An error object exists.
            error_details = "Unknown error details"
            if response_error_content != 'ATTRIBUTE_NOT_FOUND':
                # Try to get more specific error info
                msg = getattr(response_error_content, 'message', str(response_error_content))
                code = getattr(response_error_content, 'code', 'N/A')
                details = getattr(response_error_content, 'details', 'N/A')
                hint = getattr(response_error_content, 'hint', 'N/A')
                error_details = f"Message: {msg}, Code: {code}, Details: {details}, Hint: {hint}"

            full_error_log = f"فشل نشر المود. الخطأ من Supabase (status {response_status_code}): {error_details}. Response data was: {response_data_content}"
            update_status(full_error_log)
            raise Exception(full_error_log)

    except Exception as e:
        error_message = f"!!! خطأ فادح أثناء النشر: {e}"
        update_status(error_message)
        update_status(f"Traceback: {traceback.format_exc()}") # This is very important for debugging
    finally:
        # This block always runs, ensuring the button is re-enabled.
        if 'publish_mod_button' in globals() and publish_mod_button.winfo_exists():
            publish_mod_button.config(state=tk.NORMAL)

def handle_generate_description():
    """Handles the button click for generating AI description."""
    # Define last_scraped_data as global variable
    global last_scraped_data, gemini_extraction_cache

    # Initialize last_scraped_data if it doesn't exist
    if 'last_scraped_data' not in globals():
        last_scraped_data = {}

    if not GEMINI_CLIENT_OK:
        messagebox.showerror("Gemini Error", "Gemini is not configured correctly or the library is missing.")
        return

    manual_features_text = mod_features_text.get("1.0", tk.END).strip()
    mod_name = publish_name_entry.get().strip()
    mod_category = category_combobox.get()

    # Use scraped text as primary context if available
    scraped_context = None
    if last_scraped_data and last_scraped_data.get("full_scraped_text"):
        scraped_context = last_scraped_data["full_scraped_text"]
        update_status("Using scraped article text as context for AI description.")
    elif not manual_features_text:
         messagebox.showwarning("Input Required", "Please enter some mod features or scrape an article first.")
         return
    else:
         update_status("No scraped text found, using only manually entered features for AI description.")


    if not manual_features_text and not scraped_context:
         messagebox.showwarning("Input Required", "Please enter some mod features or scrape an article first.")
         return

    update_status("\n--- Starting description generation using Gemini AI ---")
    generate_desc_button.config(state=tk.DISABLED)
    # Pass both scraped context and manual features
    run_in_thread(generate_description_task, mod_name, mod_category, scraped_context, manual_features_text)

def handle_generate_arabic_description():
    """Handles the button click for generating Arabic AI description."""
    # Define last_scraped_data as global variable
    global last_scraped_data, gemini_extraction_cache

    # Initialize last_scraped_data if it doesn't exist
    if 'last_scraped_data' not in globals():
        last_scraped_data = {}

    if not GEMINI_CLIENT_OK:
        messagebox.showerror("Gemini Error", "Gemini is not configured correctly or the library is missing.")
        return

    manual_features_text = mod_features_text.get("1.0", tk.END).strip()
    mod_name = publish_name_entry.get().strip()
    mod_category = category_combobox.get()

    # Use scraped text as primary context if available
    scraped_context = None
    if last_scraped_data and last_scraped_data.get("full_scraped_text"):
        scraped_context = last_scraped_data["full_scraped_text"]
        update_status("Using scraped article text as context for Arabic AI description.")
    elif not manual_features_text:
         messagebox.showwarning("Input Required", "Please enter some mod features or scrape an article first.")
         return
    else:
         update_status("No scraped text found, using only manually entered features for Arabic AI description.")

    if not manual_features_text and not scraped_context:
         messagebox.showwarning("Input Required", "Please enter some mod features or scrape an article first.")
         return

    update_status("\n--- Starting Arabic description generation using Gemini AI ---")
    generate_arabic_desc_button.config(state=tk.DISABLED)
    # Pass both scraped context and manual features
    run_in_thread(generate_arabic_description_task, mod_name, mod_category, scraped_context, manual_features_text)

def generate_arabic_description_task(mod_name, mod_category, scraped_text, manual_features):
    """Calls Gemini API to generate Arabic description using available context and updates the GUI."""
    global gemini_model, current_gemini_key_index, GEMINI_CLIENT_OK

    # Determine primary context: manual_features or scraped_text (full description)
    primary_context_source = ""
    if manual_features:
        primary_context_source = "The user has provided the following key features. Base the description primarily on these:\n\"\"\"\n" + manual_features + "\n\"\"\"\n"
        if scraped_text: # Offer scraped_text (full description) as supplementary
            primary_context_source += "\nAdditionally, the following full description was extracted and can be used for supplementary details if relevant:\n\"\"\"\n" + scraped_text[:2000] + "\n\"\"\"\n"
    elif scraped_text: # No manual features, scraped_text (full description) is primary
        primary_context_source = "Based on the following full mod description and features extracted from the source:\n\"\"\"\n" + scraped_text[:2500] + "\n\"\"\"\n" # Increased limit for full desc
    else:
        update_status("!!! No features or scraped text provided for Arabic description generation.")
        if 'generate_arabic_desc_button' in globals() and generate_arabic_desc_button.winfo_exists():
            generate_arabic_desc_button.config(state=tk.NORMAL)
        messagebox.showwarning("Input Required", "Please provide mod features or scrape an article to generate an Arabic description.")
        return

    prompt = f"""
    أنت كاتب محتوى ماين كرافت خبير باللغة العربية. مهمتك هي كتابة وصف جذاب وفريد ومفصل (حوالي 100-200 كلمة) لمحتوى ماين كرافت باللغة العربية، مع الحفاظ على نبرة طبيعية وبشرية.

    **اسم المحتوى:** {mod_name if mod_name else "غير محدد"}
    **نوع المحتوى:** {mod_category}

    **معلومات المصدر (الوصف الكامل للمود و/أو الميزات الرئيسية):**
    {primary_context_source}

    **التعليمات:**
    - **استخدم 'معلومات المصدر' المقدمة (التي تحتوي على الوصف الكامل للمود و/أو الميزات المقدمة من المستخدم) كأساس رئيسي لكتابتك.**
    - **أنشئ وصفاً جديداً وفريداً وجذاباً.** لا تقم بنسخ أو إعادة صياغة الوصف الأصلي ببساطة.
    - **يجب أن تذكر صراحة الميزات الرئيسية للمود** ضمن وصفك الجديد. ارجع إلى 'معلومات المصدر' لتحديد هذه الميزات.
    - سلط الضوء على قيمة المحتوى للاعبين وكيف يحسن تجربة اللعب.
    - استخدم اللغة العربية الطبيعية والسلسة. تجنب التكرار.
    - يجب أن يكون الناتج باللغة العربية فقط.
    - لا تضع اسم المحتوى أو النوع في البداية إلا إذا كان ذلك مناسباً طبيعياً.
    - لا تضف أي عناوين مثل "الوصف:" أو ما شابه؛ قدم النص الوصفي فقط.
    - إذا كانت 'معلومات المصدر' قليلة، ركز على ما هو متوفر لإنشاء أفضل وصف ممكن، مع التأكد من ذكر الميزات إذا كانت متوفرة.

    قدم إجابتك بهذا التنسيق بالضبط:
    [ARABIC_DESCRIPTION]
    وصفك الجذاب باللغة العربية هنا...
    [/ARABIC_DESCRIPTION]
    """

    retries = 0
    success = False
    generated_arabic_desc = ""
    while retries < MAX_GEMINI_RETRIES and not success:
        if not GEMINI_CLIENT_OK:
            update_status("!!! Gemini client not configured. Cannot generate Arabic description.")
            break # Exit loop if client isn't OK

        try:
            update_status(f"📤 إرسال الطلب إلى Gemini لإنشاء الوصف العربي (باستخدام مفتاح {current_gemini_key_index})...")
            response = gemini_model.generate_content(prompt)
            full_response = response.text.strip()
            update_status("📥 تم استلام الرد من Gemini للوصف العربي.")

            # Parse the response to extract Arabic description
            generated_arabic_desc = ""

            # Extract Arabic description
            desc_match = re.search(r'\[ARABIC_DESCRIPTION\](.*?)\[/ARABIC_DESCRIPTION\]', full_response, re.DOTALL)
            if desc_match:
                generated_arabic_desc = desc_match.group(1).strip()
                update_status(f"✅ تم استخراج الوصف العربي بنجاح ({len(generated_arabic_desc)} حرف)")
            else:
                # Fallback: use the entire response as description if format is not followed
                generated_arabic_desc = full_response
                update_status("⚠️ تحذير: لم يتبع Gemini التنسيق المتوقع. استخدام الرد كاملاً كوصف عربي.")

            success = True # Mark as success if no exception

        except Exception as e:
            error_message = f"!!! خطأ أثناء إنشاء الوصف العربي بواسطة Gemini (المفتاح {current_gemini_key_index}): {e}"
            update_status(error_message)
            # Try to get more details from the error if possible
            if hasattr(e, 'response') and hasattr(e.response, 'text'):
                 update_status(f"Gemini API Response Text: {e.response.text}")
            update_status(f"Traceback: {traceback.format_exc()}")

            # Check for rate limit error (specific error type might vary, adjust if needed)
            # Example: Check if the error message contains typical rate limit phrases
            is_rate_limit = "rate limit" in str(e).lower() or "quota" in str(e).lower() or "resource has been exhausted" in str(e).lower()

            if is_rate_limit:
                update_status(f"!!! تم الوصول إلى حد المعدل للمفتاح {current_gemini_key_index}. محاولة استخدام المفتاح التالي...")
                retries += 1
                if not configure_gemini_client(current_gemini_key_index + 1):
                    # If configuring the next client fails (e.g., no more keys), break the loop
                    messagebox.showerror("خطأ Gemini", "فشل إنشاء الوصف العربي: تم استنفاد جميع مفاتيح API أو فشل التكوين.")
                    break
                # If configuration succeeded, the loop will continue with the new key
            else:
                # For other errors, show message and break
                messagebox.showerror("خطأ Gemini", f"فشل إنشاء الوصف العربي: {e}")
                break # Stop retrying for non-rate-limit errors

    # --- Process result after loop ---
    if success:
        # Schedule GUI update on the main thread
        if 'window' in globals() and window.winfo_exists():
            # Pass the widget directly to the update function
            window.after(0, auto_populate_text_widget, publish_arabic_desc_text, generated_arabic_desc)
            update_status("--> ✅ تم تحديث حقل الوصف العربي بالوصف المُنشأ بواسطة AI.")
            update_status(f"📝 الوصف العربي المُنشأ ({len(generated_arabic_desc)} حرف): {generated_arabic_desc[:100]}...")
        else:
            update_status("خطأ: لا يمكن تحديث حقل الوصف العربي (النافذة غير موجودة).")
            update_status(f"الوصف العربي المُنشأ (للسجل): {generated_arabic_desc}")
    elif not GEMINI_CLIENT_OK:
         update_status("!!! فشل إنشاء الوصف العربي لأن عميل Gemini غير متاح.")
    # else: (Error message already shown in the loop for non-rate-limit errors or after exhausting keys)

    # --- Re-enable button safely on the main thread ---
    # Moved finally block outside the while loop
    if 'window' in globals() and window.winfo_exists():
        window.after(0, lambda: generate_arabic_desc_button.config(state=tk.NORMAL) if 'generate_arabic_desc_button' in globals() and generate_arabic_desc_button.winfo_exists() else None)

def generate_description_task(mod_name, mod_category, scraped_text, manual_features): # Note: scraped_text here is the 'full_mod_description' from Gemini
    """Calls Gemini API to generate description using available context and updates the GUI."""
    global gemini_model, current_gemini_key_index, GEMINI_CLIENT_OK

    # Determine primary context: manual_features or scraped_text (full description)
    primary_context_source = ""
    if manual_features:
        primary_context_source = "The user has provided the following key features. Base the description primarily on these:\n\"\"\"\n" + manual_features + "\n\"\"\"\n"
        if scraped_text: # Offer scraped_text (full description) as supplementary
            primary_context_source += "\nAdditionally, the following full description was extracted and can be used for supplementary details if relevant:\n\"\"\"\n" + scraped_text[:2000] + "\n\"\"\"\n"
    elif scraped_text: # No manual features, scraped_text (full description) is primary
        primary_context_source = "Based on the following full mod description and features extracted from the source:\n\"\"\"\n" + scraped_text[:2500] + "\n\"\"\"\n" # Increased limit for full desc
    else:
        update_status("!!! No features or scraped text provided for description generation.")
        if 'generate_desc_button' in globals() and generate_desc_button.winfo_exists():
            generate_desc_button.config(state=tk.NORMAL)
        messagebox.showwarning("Input Required", "Please provide mod features or scrape an article to generate a description.")
        return

    prompt = f"""
    You are an expert Minecraft content writer. Your task is to:
    1. Write an engaging, unique, and detailed description (around 100-200 words) for a Minecraft content piece in English, maintaining a human-like and natural tone.
    2. Extract creator information from the source content.

    **Content Name:** {mod_name if mod_name else "Not specified"}
    **Content Type:** {mod_category}

    **Source Information (Full Mod Description and/or Key Features):**
    {primary_context_source}
    **Instructions:**
    - **Use the provided 'Source Information' (which contains the full mod description and/or user-provided features) as the PRIMARY basis for your writing.**
    - **Create a NEW, UNIQUE, and engaging description.** Do NOT simply copy or slightly rephrase the original description.
    - **You MUST explicitly mention the mod's key features** within your new description. Refer to the 'Source Information' to identify these features.
    - Highlight the content's value to players and how it enhances gameplay.
    - Use natural and fluent English. Avoid repetition.
    - The output must be in English only.
    - Do not include the content name or type at the beginning unless it fits naturally.
    - Do not add any headings like "Description:" or similar; provide only the descriptive text.
    - If the 'Source Information' is minimal, focus on what is provided to create the best possible description, ensuring features are mentioned if available.

    **IMPORTANT:** After the description, also extract creator information and provide it in JSON format:

    Provide your response in this exact format:
    [DESCRIPTION]
    Your engaging description here...
    [/DESCRIPTION]

    [CREATOR_INFO]
    {{
        "creator_name": "Creator name if found, otherwise 'admin'",
        "creator_contact_info": "Contact information if found, otherwise null",
        "creator_social_channels": ["List of social media links/channels if found, otherwise empty array"]
    }}
    [/CREATOR_INFO]
    """
    retries = 0
    success = False
    generated_desc = ""
    while retries < MAX_GEMINI_RETRIES and not success:
        if not GEMINI_CLIENT_OK:
            update_status("!!! Gemini client not configured. Cannot generate description.")
            break # Exit loop if client isn't OK

        try:
            update_status(f"إرسال الطلب إلى Gemini (باستخدام مفتاح {current_gemini_key_index})...")
            response = gemini_model.generate_content(prompt)
            full_response = response.text.strip()
            update_status("تم استلام الرد من Gemini.")

            # Parse the response to extract description and creator info
            generated_desc = ""
            creator_info = {}

            # Extract description
            desc_match = re.search(r'\[DESCRIPTION\](.*?)\[/DESCRIPTION\]', full_response, re.DOTALL)
            if desc_match:
                generated_desc = desc_match.group(1).strip()
            else:
                # Fallback: use the entire response as description if format is not followed
                generated_desc = full_response
                update_status("Warning: Gemini response did not follow expected format. Using entire response as description.")

            # Extract creator info
            creator_match = re.search(r'\[CREATOR_INFO\](.*?)\[/CREATOR_INFO\]', full_response, re.DOTALL)
            if creator_match:
                try:
                    creator_json_str = creator_match.group(1).strip()
                    creator_info = json.loads(creator_json_str)
                    update_status("Successfully extracted creator information from Gemini response.")
                except json.JSONDecodeError as je:
                    update_status(f"Warning: Could not parse creator info JSON: {je}")
                    creator_info = {"creator_name": "admin", "creator_contact_info": None, "creator_social_channels": []}
            else:
                update_status("Warning: No creator info found in Gemini response. Using default values.")
                creator_info = {"creator_name": "admin", "creator_contact_info": None, "creator_social_channels": []}

            success = True # Mark as success if no exception

        except Exception as e:
            error_message = f"!!! خطأ أثناء إنشاء الوصف بواسطة Gemini (المفتاح {current_gemini_key_index}): {e}"
            update_status(error_message)
            # Try to get more details from the error if possible
            if hasattr(e, 'response') and hasattr(e.response, 'text'):
                 update_status(f"Gemini API Response Text: {e.response.text}")
            update_status(f"Traceback: {traceback.format_exc()}")

            # Check for rate limit error (specific error type might vary, adjust if needed)
            # Example: Check if the error message contains typical rate limit phrases
            is_rate_limit = "rate limit" in str(e).lower() or "quota" in str(e).lower() or "resource has been exhausted" in str(e).lower()

            if is_rate_limit:
                update_status(f"!!! تم الوصول إلى حد المعدل للمفتاح {current_gemini_key_index}. محاولة استخدام المفتاح التالي...")
                retries += 1
                if not configure_gemini_client(current_gemini_key_index + 1):
                    # If configuring the next client fails (e.g., no more keys), break the loop
                    messagebox.showerror("خطأ Gemini", "فشل إنشاء الوصف: تم استنفاد جميع مفاتيح API أو فشل التكوين.")
                    break
                # If configuration succeeded, the loop will continue with the new key
            else:
                # For other errors, show message and break
                messagebox.showerror("خطأ Gemini", f"فشل إنشاء الوصف: {e}")
                break # Stop retrying for non-rate-limit errors

    # --- Process result after loop ---
    if success:
        # Schedule GUI update on the main thread
        if 'window' in globals() and window.winfo_exists():
            # Pass the widget directly to the update function
            window.after(0, auto_populate_text_widget, publish_desc_text, generated_desc)
            update_status("--> تم تحديث حقل الوصف بالوصف المُنشأ بواسطة AI.")

            # Update creator information fields if they exist and creator_info was extracted
            if 'creator_info' in locals() and creator_info:
                creator_name = creator_info.get("creator_name", "admin")
                creator_contact = creator_info.get("creator_contact_info")
                creator_social_channels = creator_info.get("creator_social_channels", [])

                # Update creator name field
                if creator_name and 'publish_creator_name_entry' in globals() and publish_creator_name_entry.winfo_exists():
                    window.after(0, auto_populate_field, publish_creator_name_entry, creator_name)
                    update_status(f"--> تم تحديث حقل اسم صانع المود: {creator_name}")

                # Update creator contact field
                if creator_contact and 'publish_creator_contact_entry' in globals() and publish_creator_contact_entry.winfo_exists():
                    window.after(0, auto_populate_field, publish_creator_contact_entry, creator_contact)
                    update_status(f"--> تم تحديث حقل معلومات التواصل: {creator_contact}")

                # Update custom social sites if available
                if 'custom_site_entries' in globals() and creator_social_channels:
                    # Clear existing entries first (except the first one)
                    while len(custom_site_entries) > 1:
                        remove_custom_site_row(len(custom_site_entries) - 1)

                    # Parse and populate custom sites from creator_social_channels
                    for i, channel in enumerate(creator_social_channels):
                        if isinstance(channel, str) and ':' in channel:
                            parts = channel.split(':', 1)
                            if len(parts) == 2:
                                site_name = parts[0].strip()
                                site_url = parts[1].strip()

                                # Add new row if needed
                                if i >= len(custom_site_entries):
                                    add_custom_site_row()

                                # Populate the entry
                                if i < len(custom_site_entries):
                                    entry_data = custom_site_entries[i]
                                    entry_data['name_entry'].delete(0, tk.END)
                                    entry_data['name_entry'].insert(0, site_name)
                                    entry_data['url_entry'].delete(0, tk.END)
                                    entry_data['url_entry'].insert(0, site_url)

                    update_status(f"--> تم تحديث {len(creator_social_channels)} موقع تواصل مخصص")
        else:
            update_status("خطأ: لا يمكن تحديث حقل الوصف (النافذة غير موجودة).")
            update_status(f"الوصف المُنشأ (للسجل): {generated_desc}")
    elif not GEMINI_CLIENT_OK:
         update_status("!!! فشل إنشاء الوصف لأن عميل Gemini غير متاح.")
    # else: (Error message already shown in the loop for non-rate-limit errors or after exhausting keys)

    # --- Re-enable button safely on the main thread ---
    # Moved finally block outside the while loop
    if 'window' in globals() and window.winfo_exists():
        window.after(0, lambda: generate_desc_button.config(state=tk.NORMAL) if 'generate_desc_button' in globals() and generate_desc_button.winfo_exists() else None)


# --- Core Functions (File Processing) ---
def upload_to_supabase(bucket_name: str, file_content: bytes, file_name: str, content_type: str):
    if not storage_client: raise ConnectionError("Supabase storage client not initialized.")
    temp_file_path = None
    try:
        safe_base, ext = os.path.splitext(file_name)
        if not ext: ext = mimetypes.guess_extension(content_type) or '.bin'

        # Check if this is a GIF file and ensure the extension is .gif
        is_gif = False
        if content_type and 'image/gif' in content_type.lower():
            is_gif = True
            if not file_name.lower().endswith('.gif'):
                file_name = f"{safe_base}.gif"
                update_status(f"Changed filename to {file_name} to match GIF content type")

        # Use the filename passed into the function directly as the upload path.
        # Ensure it's sanitized just in case, although it should be already.
        safe_filename = sanitize_filename(file_name)
        update_status(f"Uploading '{safe_filename}' to bucket '{bucket_name}' with content type '{content_type}'...")
        temp_dir = os.path.dirname(__file__) or '.'
        temp_file_path = os.path.join(temp_dir, f"temp_{random.randint(1000,9999)}_{safe_filename}")
        update_status(f"Using temporary file: {temp_file_path}")
        with open(temp_file_path, "wb") as f: f.write(file_content)
        upload_start_time = time.time()

        # Include content-type in file_options to ensure proper MIME type
        file_options = {
            "cache-control": "3600",
            "upsert": "true",
            "contentType": content_type  # Add content type to ensure proper MIME type
        }

        with open(temp_file_path, "rb") as f:
             response = storage_client.storage.from_(bucket_name).upload(
                 file=f, path=safe_filename,
                 file_options=file_options
             )
        upload_duration = time.time() - upload_start_time
        update_status(f"Upload API call finished in {upload_duration:.2f} seconds.")
        if os.path.exists(temp_file_path):
             try: os.remove(temp_file_path); update_status(f"Removed temporary file.")
             except OSError as rm_err: update_status(f"Warning: Could not remove temp file: {rm_err}")
             temp_file_path = None
        update_status(f"Upload response status: {response.status_code if hasattr(response, 'status_code') else 'N/A'}")
        update_status(f"Retrieving public URL for '{safe_filename}'...")
        public_url_data = storage_client.storage.from_(bucket_name).get_public_url(safe_filename)
        update_status(f"Public URL data received.")
        public_url = public_url_data if isinstance(public_url_data, str) else None
        if not public_url:
             if isinstance(public_url_data, dict): public_url = public_url_data.get('publicURL') or public_url_data.get('publicUrl')
        if not public_url or not public_url.startswith('http'):
             constructed_url = f"{STORAGE_URL}/storage/v1/object/public/{bucket_name}/{safe_filename.replace(' ', '%20')}"
             update_status(f"Warning: Could not get valid public URL via API. Trying constructed URL.")
             if bucket_name in constructed_url and safe_filename in constructed_url: public_url = constructed_url
             else: raise ValueError(f"Could not get or construct valid public URL for {safe_filename}.")
        update_status(f"Successfully uploaded '{safe_filename}'.")
        return public_url
    except Exception as e:
        error_message = f"Error uploading {file_name}: {e}"
        # Check for potential RLS issue
        if isinstance(e, Exception) and hasattr(e, 'args') and e.args:
             try:
                 error_dict = e.args[0]
                 if isinstance(error_dict, dict) and error_dict.get('statusCode') == 403:
                      error_message += "\n   >>> Hint: A 403 Unauthorized error often indicates a missing Supabase Storage RLS policy. Ensure the 'anon' role has INSERT permission on the bucket."
             except Exception: pass # Ignore errors parsing the exception itself
        update_status(f"!!! {error_message}")

        if temp_file_path and os.path.exists(temp_file_path):
            try: os.remove(temp_file_path); update_status(f"Cleaned up temp file after error.")
            except OSError as rm_err: update_status(f"Error removing temp file: {rm_err}")
        raise

# Renamed function to avoid conflict with the 2-argument version used earlier
def process_image_url_only(image_url: str, base_article_url: str = None): # Added base_article_url
    update_status(f"Processing image from URL: {image_url}")

    if image_url and image_url.startswith('/'):
        if base_article_url:
            try:
                parsed_base = urlparse(base_article_url)
                if parsed_base.scheme and parsed_base.netloc:
                    image_url = urljoin(f"{parsed_base.scheme}://{parsed_base.netloc}", image_url)
                    update_status(f"Constructed absolute image URL: {image_url}")
                else:
                    update_status(f"!!! Cannot construct absolute URL for relative image path {image_url} - base_article_url is invalid: {base_article_url}")
                    return None
            except Exception as e_parse:
                update_status(f"!!! Error parsing base_article_url '{base_article_url}' for relative image path {image_url}: {e_parse}")
                return None
        else:
            update_status(f"!!! Cannot construct absolute URL for relative image path {image_url} - no base_article_url provided.")
            return None

    # Check if image_url is valid after potential modification or if it was initially invalid
    if not image_url or not image_url.startswith('http'):
        update_status(f"!!! Skipping image URL (not http/https or became invalid/None): {image_url}")
        return None

    if STORAGE_URL and IMAGE_BUCKET and image_url.startswith(f"{STORAGE_URL}/storage/v1/object/public/{IMAGE_BUCKET}/"):
        update_status(f"Image URL is already a Supabase link from bucket '{IMAGE_BUCKET}'. Using directly: {image_url}")
        return image_url

    try:
        headers = {'User-Agent': 'Mozilla/5.0'}
        is_mcpeland_image = "mcpeland.io" in urlparse(image_url).netloc

        if is_mcpeland_image:
            update_status(f"Processing mcpeland.io image URL: {image_url}. Applying specific headers if needed.")
            # Add any specific headers for mcpeland.io if discovered they are necessary.
            # For now, using the standard User-Agent.
            # headers['Referer'] = base_article_url if base_article_url else image_url # Example if referer is needed

        update_status(f"Requesting image from: {image_url} with headers: {headers}")
        response = requests.get(image_url, stream=True, timeout=30, headers=headers)

        update_status(f"Response received for {image_url}. Status Code: {response.status_code}")
        update_status(f"Response Headers for {image_url}: {json.dumps(dict(response.headers), indent=2)}")

        response.raise_for_status() # Check for HTTP errors after logging

        content_type = response.headers.get('content-type', 'application/octet-stream').split(';')[0].strip()
        update_status(f"Parsed Content-Type from headers for {image_url}: '{content_type}'")

        # Detailed check before content_type.startswith('image/')
        if not content_type.startswith('image/'):
            update_status(f"!!! Warning: Content-Type '{content_type}' for {image_url} does not start with 'image/'.")
            # For mcpeland.io, if content type is not image, log more details or attempt fallback
            if is_mcpeland_image:
                update_status(f"!!! MCPELAND.IO image URL {image_url} returned non-image Content-Type: '{content_type}'. This might indicate an issue with the URL or server response.")
                # Consider if a different approach is needed for mcpeland.io if this happens often.
                # For now, it will proceed and likely fail the 'image/' check, which is desired for diagnosis.
            # Raise ValueError here to stop processing if not an image, as per original logic
            raise ValueError(f"URL not a valid image (Content-Type: {content_type}) for {image_url}")

        image_bytes = response.content
        if not image_bytes: raise ValueError("Downloaded image is empty.")
        update_status(f"Downloaded {len(image_bytes)} bytes from {image_url}.")

        original_filename_from_url = sanitize_filename(image_url.split('/')[-1].split('?')[0])

        # --- Phosus API Enhancement (similar to process_image) ---
        selected_enhancement = phosus_enhancement_var.get() if 'phosus_enhancement_var' in globals() else "none"
        if selected_enhancement != "none":
            update_status(f"Attempting Phosus enhancement: {selected_enhancement}")
            try:
                enhanced_bytes = call_phosus_api(selected_enhancement, image_bytes, content_type)
                if enhanced_bytes:
                    update_status(f"Phosus enhancement successful. New size: {len(enhanced_bytes)} bytes.")
                    image_bytes = enhanced_bytes
                    # Potentially update content_type if Phosus changes it (e.g., super-res to PNG)
                    # For now, assume it's handled or content_type remains valid for Pillow.
                else:
                    update_status("Phosus enhancement returned no data, using original image.")
            except Exception as phosus_err:
                update_status(f"!!! Phosus API error: {phosus_err}. Using original image.")
        # --- End Phosus API Enhancement ---

        # --- GIF Handling ---
        is_gif = False
        is_animated_gif = False
        if 'image/gif' in content_type.lower():
            is_gif = True
        elif original_filename_from_url.lower().endswith('.gif'):
            is_gif = True
            if not 'image/gif' in content_type.lower():
                content_type = 'image/gif'
                update_status(f"Corrected content type to 'image/gif' based on extension for: {original_filename_from_url}")

        if is_gif:
            update_status(f"Detected GIF ({original_filename_from_url}). Checking if animated...")

            # Check if it's an animated GIF
            try:
                gif_img = Image.open(BytesIO(image_bytes))
                is_animated = getattr(gif_img, 'is_animated', False)

                # Try to manually check if it's animated by seeking to the second frame
                if not is_animated:
                    try:
                        gif_img.seek(1)
                        is_animated = True
                        gif_img.seek(0)  # Reset to first frame
                    except EOFError:
                        # Only one frame, not animated
                        is_animated = False

                if is_animated:
                    update_status(f"GIF is animated with {getattr(gif_img, 'n_frames', 0)} frames. Preserving animation.")
                    is_animated_gif = True
                else:
                    update_status("GIF is not animated.")
            except Exception as e:
                update_status(f"Error checking if GIF is animated: {e}. Assuming it might be animated.")
                is_animated_gif = True  # Assume it might be animated to be safe

            # Make sure the filename ends with .gif
            if not original_filename_from_url.lower().endswith('.gif'):
                base, _ = os.path.splitext(original_filename_from_url)
                original_filename_from_url = f"{base}.gif"

            # Check size for GIF before upload
            if len(image_bytes) > MAX_IMAGE_SIZE_BYTES and is_animated_gif:
                update_status(f"Animated GIF size ({format_size(len(image_bytes))}) exceeds limit. Attempting to compress while preserving animation.")
                try:
                    # Try to compress the GIF
                    compression_level = 'normal'  # Default compression level
                    gif_result = compress_gif(image_bytes, compression_level)

                    if gif_result:
                        compressed_bytes, _, _ = gif_result
                        update_status(f"Successfully compressed animated GIF. Original: {format_size(len(image_bytes))}, Compressed: {format_size(len(compressed_bytes))}")

                        # Use the compressed GIF if it's smaller
                        if len(compressed_bytes) < len(image_bytes):
                            image_bytes = compressed_bytes
                            update_status("Using compressed GIF for upload.")
                        else:
                            update_status("Compressed GIF is not smaller than original. Using original.")
                except Exception as e:
                    update_status(f"Error compressing animated GIF: {e}. Will use original.")

            # Final size check
            if len(image_bytes) > MAX_IMAGE_SIZE_BYTES:
                update_status(f"!!! GIF size ({format_size(len(image_bytes))}) exceeds limit of {format_size(MAX_IMAGE_SIZE_BYTES)}. Skipping upload.")
                return None

            unique_gif_filename = generate_unique_filename(original_filename_from_url, prefix="img_")
            return upload_to_supabase(IMAGE_BUCKET, image_bytes, unique_gif_filename, 'image/gif')
        # --- End GIF Handling ---

        try:
            img = Image.open(BytesIO(image_bytes))
        except Exception as img_err:
            raise ValueError(f"Pillow error opening image: {img_err}")

        if img.mode in ("RGBA", "P", "LA"):
             update_status(f"Converting image from {img.mode} to RGB...")
             bg = Image.new("RGB", img.size, (255, 255, 255))
             try:
                 if 'A' in img.mode: bg.paste(img, mask=img.split()[-1])
                 else: bg.paste(img)
                 img = bg; update_status("Converted to RGB.")
             except Exception as convert_err:
                 update_status(f"Warn: Conversion with alpha paste failed: {convert_err}. Using simple convert...");
                 img = img.convert("RGB")

        img_format_detected = img.format if img.format else None
        img_format_save = 'JPEG' # Default

        if img_format_detected:
             if img_format_detected.upper() in ['JPEG', 'JPG', 'PNG', 'WEBP']:
                 img_format_save = img_format_detected.upper()
             else:
                 update_status(f"Warn: Detected format {img_format_detected} not ideal. Saving as JPEG.")
        elif content_type:
             if 'png' in content_type.lower(): img_format_save = 'PNG'
             elif 'webp' in content_type.lower(): img_format_save = 'WEBP'

        output_buffer = BytesIO()
        save_kwargs = {'format': img_format_save}
        if img_format_save == 'JPEG':
            save_kwargs['quality'] = 85
            save_kwargs['optimize'] = True
        elif img_format_save == 'PNG':
            save_kwargs['optimize'] = True
        elif img_format_save == 'WEBP':
            save_kwargs['quality'] = 85

        update_status(f"Compressing to {img_format_save}...")
        try:
            img.save(output_buffer, **save_kwargs)
        except Exception as save_err:
            raise ValueError(f"Pillow save error: {save_err}")

        compressed_bytes = output_buffer.getvalue()
        update_status(f"Initial compression. Original size: {len(image_bytes)}, Compressed size: {len(compressed_bytes)}")

        # --- Aggressive compression if still over 1MB (similar to process_image) ---
        if len(compressed_bytes) > MAX_IMAGE_SIZE_BYTES:
            update_status(f"Image is still too large ({len(compressed_bytes)} bytes). Applying aggressive JPEG compression.")
            # Ensure img is the Pillow Image object (it should be from 'img = Image.open(BytesIO(image_bytes))' or after conversion)

            output_buffer_aggressive = BytesIO() # New buffer for aggressive compression
            aggressive_save_kwargs = {'format': 'JPEG', 'quality': 75, 'optimize': True} # Force JPEG

            try:
                img.save(output_buffer_aggressive, **aggressive_save_kwargs)
                new_compressed_bytes = output_buffer_aggressive.getvalue()
                update_status(f"Aggressive JPEG compression size: {len(new_compressed_bytes)} bytes.")
                if len(new_compressed_bytes) < len(compressed_bytes):
                     compressed_bytes = new_compressed_bytes
                     img_format_save = 'JPEG' # Update save format if changed
                else:
                     update_status("Aggressive compression did not reduce size further or failed, using previous compression.")
            except Exception as agg_save_err:
                update_status(f"!!! Error during aggressive JPEG compression: {agg_save_err}. Using previously compressed image.")
        # --- End Aggressive Compression ---

        # Final check on size before upload
        if len(compressed_bytes) > MAX_IMAGE_SIZE_BYTES:
            update_status(f"!!! Final image size ({len(compressed_bytes)} bytes) still exceeds limit of {MAX_IMAGE_SIZE_BYTES} bytes after all compressions. Skipping upload.")
            # raise ValueError(f"Image size exceeds limit after all compressions. Cannot process.") # Or return None
            return None # Or handle as error appropriately for batch mode

        base, _ = os.path.splitext(original_filename_from_url)
        final_filename_for_upload = f"{base if base else 'image'}.{img_format_save.lower()}"

        unique_filename = generate_unique_filename(final_filename_for_upload, prefix="img_")
        upload_content_type = f"image/{img_format_save.lower()}"

        public_url = upload_to_supabase(IMAGE_BUCKET, compressed_bytes, unique_filename, upload_content_type)
        return public_url

    except requests.exceptions.RequestException as e:
        update_status(f"Download error for {image_url}: {e}"); raise
    except Image.UnidentifiedImageError:
        update_status(f"Error: Cannot identify image file from {image_url}."); raise
    except ValueError as e:
        update_status(f"Processing error for {image_url}: {e}"); raise
    except Exception as e:
        update_status(f"Unexpected error processing {image_url}: {e}"); raise


# --- NEW: Handler and Task for Single Mod Processing ---
def handle_process_mod():
    """Handles the 'Process Mod' button click."""
    mod_url = mod_page_url_entry.get().strip()
    if not mod_url:
        messagebox.showerror("خطأ", "الرجاء إدخال رابط صفحة المود.")
        return
    # Basic URL validation
    parsed_url = urlparse(mod_url)
    if not all([parsed_url.scheme, parsed_url.netloc]):
        messagebox.showerror("خطأ", "الرابط المدخل لا يبدو كرابط URL صالح.")
        return

    is_direct = direct_download_var.get() # Get state of the new checkbox
    update_status(f"بدء معالجة المود من: {mod_url} {'(رابط مباشر)' if is_direct else ''}")
    process_mod_button.config(state=tk.DISABLED)
    run_in_thread(process_mod_task, mod_url, is_direct) # Pass is_direct

def process_mod_task(mod_page_url, is_direct_download: bool): # Added is_direct_download
    """Task to process a single mod URL in a thread."""
    final_url = None
    mod_bytes = None
    mod_version_from_source = None # New variable to hold version
    try:
        # process_mod now returns three values and accepts is_direct_download
        final_url, mod_bytes, mod_version_from_source = process_mod(mod_page_url, is_direct_download)

        if final_url:
            status_msg = f"--> رابط المود النهائي (Supabase): {final_url}"
            update_status(status_msg)
            update_mod_result(final_url) # Update the result box
            # Auto-populate the publish URL field
            if 'publish_mod_url_entry' in globals() and publish_mod_url_entry.winfo_exists():
                auto_populate_field(publish_mod_url_entry, final_url)
                update_status("تم ملء حقل رابط تحميل المود تلقائيًا.")
            # Auto-populate the size field
            if mod_bytes and 'publish_size_entry' in globals() and publish_size_entry.winfo_exists():
                formatted_size = format_size(len(mod_bytes))
                publish_size_entry.config(state=tk.NORMAL)
                auto_populate_field(publish_size_entry, formatted_size)
                publish_size_entry.config(state=tk.DISABLED)
                update_status(f"تم ملء حقل الحجم تلقائيًا: {formatted_size}")
            elif not mod_bytes and final_url: # If URL exists but no bytes (e.g. existing Supabase link)
                 update_status("!!! تحذير: لم يتم الحصول على حجم المود للمعالجة التلقائية (قد يكون رابطًا موجودًا).")


            # NEW: Populate version field if extracted
            if mod_version_from_source and 'publish_version_entry' in globals() and publish_version_entry.winfo_exists():
                auto_populate_field(publish_version_entry, mod_version_from_source)
                update_status(f"Auto-populated Version field from source: {mod_version_from_source}")
            elif mod_version_from_source:
                update_status(f"Extracted version '{mod_version_from_source}' but version field not found in GUI.")

            # Add CurseForge images to managed images if available
            global curseforge_image_urls
            if curseforge_image_urls and "curseforge.com" in mod_page_url.lower():
                update_status(f"Found {len(curseforge_image_urls)} images from CurseForge. Adding to managed images...")

                # Clear existing managed images
                global managed_images
                managed_images = []

                # Add each image to managed images
                for img_url in curseforge_image_urls:
                    add_image_to_managed_list(img_url, 'url')
                    update_status(f"Added CurseForge image: {img_url}")

                # Update the image display
                if 'image_preview_inner_frame' in globals() and image_preview_inner_frame.winfo_exists():
                    display_managed_images()

                # Reset the global variable
                curseforge_image_urls = []

        else:
            update_status(f"!!! فشلت معالجة المود من {mod_page_url}. لم يتم إرجاع رابط.")
            update_mod_result("فشل معالجة المود.") # Update result box on failure

    except Exception as e:
        error_msg = f"!!! فشل معالجة المود: {e}"
        update_status(error_msg)
        update_status(f"Traceback: {traceback.format_exc()}") # Log full traceback
        update_mod_result(f"فشل: {e}") # Update result box with error
    finally:
        # Re-enable button safely
        if 'process_mod_button' in globals() and process_mod_button.winfo_exists():
            process_mod_button.config(state=tk.NORMAL)

def update_mod_result(message):
    """Updates the mod result text box."""
    if 'mod_result_text' in globals() and mod_result_text.winfo_exists():
        mod_result_text.config(state=tk.NORMAL)
        mod_result_text.delete("1.0", tk.END)
        mod_result_text.insert(tk.END, message)
        mod_result_text.config(state=tk.DISABLED)
    else:
        update_status(f"Mod Result (GUI Error): {message}")


def extract_mod_download_link(page_url: str):
    update_status(f"Extracting mod link from: {page_url}")

    # Special handling for CurseForge URLs
    if "curseforge.com" in page_url.lower():
        update_status(f"Detected CurseForge URL. Extracting download link: {page_url}")
        try:
            # Extract the project slug from the URL
            parsed_url = urlparse(page_url)
            path_parts = parsed_url.path.strip('/').split('/')

            if len(path_parts) >= 3:
                # The last part of the path is usually the project slug
                project_slug = path_parts[-1]
                update_status(f"Extracted project slug: {project_slug}")

                # Try to fetch the gallery images first
                try:
                    # Use a more sophisticated browser-like request
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                        'Accept-Language': 'en-US,en;q=0.9',
                        'Referer': 'https://www.google.com/',
                        'Connection': 'keep-alive',
                        'Upgrade-Insecure-Requests': '1',
                        'Cache-Control': 'max-age=0',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'cross-site',
                        'Sec-Fetch-User': '?1',
                        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120"',
                        'sec-ch-ua-mobile': '?0',
                        'sec-ch-ua-platform': '"Windows"',
                        'DNT': '1',
                        'Pragma': 'no-cache'
                    }

                    session = requests.Session()
                    session.headers.update(headers)

                    # Try to get the gallery page which might be less protected
                    gallery_url = f"{page_url}/screenshots"
                    update_status(f"Trying to fetch gallery page: {gallery_url}")

                    gallery_response = session.get(gallery_url, timeout=60)
                    if gallery_response.status_code == 200:
                        update_status("Successfully fetched gallery page!")

                        # Parse the gallery page to extract images
                        gallery_soup = BeautifulSoup(gallery_response.text, 'html.parser')

                        # Look for gallery images
                        image_urls = []
                        gallery_images = gallery_soup.select('.screenshot img')

                        for img in gallery_images:
                            if img.has_attr('src'):
                                image_urls.append(img['src'])
                            elif img.has_attr('data-src'):
                                image_urls.append(img['data-src'])

                        update_status(f"Found {len(image_urls)} images in the gallery")

                        # Store the image URLs in a global variable for later use
                        global curseforge_image_urls
                        curseforge_image_urls = image_urls
                except Exception as gallery_e:
                    update_status(f"Error fetching gallery page: {gallery_e}")

                # Construct a direct download URL
                direct_download_url = f"{page_url}/download"
                update_status(f"Constructed direct download URL: {direct_download_url}")

                # Set a default version if we can't extract it
                global extracted_version_str
                extracted_version_str = "1.0.0"

                # Try to extract version from the project slug
                version_match = re.search(r'[vV]?(\d+[\.\-]\d+(?:[\.\-]\d+)?)', project_slug)
                if version_match:
                    extracted_version = version_match.group(1).replace('-', '.')
                    update_status(f"Extracted version from project slug: {extracted_version}")
                    extracted_version_str = extracted_version

                # Try to extract version from the page title
                try:
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    }
                    response = requests.get(page_url, headers=headers, timeout=30)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')
                        title = soup.find('title')
                        if title:
                            title_text = title.get_text()
                            version_match = re.search(r'[vV]?(\d+[\.\-]\d+(?:[\.\-]\d+)?)', title_text)
                            if version_match:
                                extracted_version = version_match.group(1).replace('-', '.')
                                update_status(f"Extracted version from page title: {extracted_version}")
                                extracted_version_str = extracted_version
                except Exception as e:
                    update_status(f"Error extracting version from page title: {e}")

                return direct_download_url
            else:
                # If we can't extract the project slug, return the original URL with /download appended
                direct_download_url = f"{page_url}/download"
                update_status(f"Could not extract project slug. Using URL with /download: {direct_download_url}")
                return direct_download_url

        except Exception as e:
            update_status(f"Error handling CurseForge URL: {e}")
            update_status(f"Traceback: {traceback.format_exc()}")
            # Return the original URL with /download appended as fallback
            return f"{page_url}/download"

    # Regular handling for other URLs
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0'
        }
        response = requests.get(page_url, headers=headers, timeout=30)
        response.raise_for_status()
        update_status(f"Page fetched (Status: {response.status_code}).")
        soup = BeautifulSoup(response.text, 'html.parser')
        pattern = re.compile(r'https?://[^/]*cdn9mc\.com/index\.php\?.*act=download.*id=\d+.*hash=[a-f0-9]+', re.IGNORECASE)
        links_found = []
        for link in soup.find_all('a', href=True):
            href = link['href']
            if not href.startswith('http'): href = urljoin(page_url, href)
            if pattern.search(href): links_found.append(href); update_status(f"Found potential link: {href}")
        if not links_found: update_status("Link pattern not found."); raise ValueError("Could not find download link.")
        if len(links_found) > 1: update_status(f"Warn: Multiple links found. Using first.")
        download_link = links_found[0]
        update_status(f"Extracted link: {download_link}")
        return download_link
    except requests.exceptions.RequestException as e: update_status(f"Fetch error: {e}"); raise
    except Exception as e: update_status(f"Parse error: {e}"); raise

def process_mod(initial_page_url: str, is_direct_download_link: bool = False):
    update_status(f"Processing mod from: {initial_page_url} {'(Direct URL from checkbox)' if is_direct_download_link else ''}")
    extracted_version_str = None # سنحاول استخراج الإصدار من الرابط أو المحتوى
    mod_bytes = None
    download_url_to_use = initial_page_url # سنستخدم الرابط الأولي بشكل مباشر في معظم الحالات

    # محاولة استخراج الإصدار من الرابط نفسه
    try:
        # البحث عن نمط الإصدار في الرابط (مثل v1.2.3 أو 1.2.3)
        url_version_match = re.search(r'[vV]?(\d+\.\d+(?:\.\d+)?)', initial_page_url)
        if url_version_match:
            extracted_version_str = url_version_match.group(1)
            update_status(f"تم استخراج الإصدار من الرابط: {extracted_version_str}")
    except Exception as e:
        update_status(f"خطأ أثناء محاولة استخراج الإصدار من الرابط: {e}")

    # 1. التعامل مع روابط Supabase الموجودة مسبقًا
    if STORAGE_URL and MOD_BUCKET and initial_page_url.startswith(f"{STORAGE_URL}/storage/v1/object/public/{MOD_BUCKET}/"):
        update_status(f"Mod URL is already a Supabase link from bucket '{MOD_BUCKET}'. Using directly: {initial_page_url}")
        try:
            headers = {'User-Agent': 'Mozilla/5.0'}
            response = requests.get(initial_page_url, stream=True, timeout=180, headers=headers)
            response.raise_for_status()
            mod_bytes_content = response.content
            if not mod_bytes_content:
                update_status(f"!!! Warning: Supabase mod link content is empty: {initial_page_url}")
                return initial_page_url, None, None
            update_status(f"Successfully fetched content for existing Supabase mod link (Size: {len(mod_bytes_content)} bytes).")
            return initial_page_url, mod_bytes_content, None
        except Exception as e:
            update_status(f"!!! Error fetching content from existing Supabase mod link {initial_page_url}: {e}")
            return initial_page_url, None, None

    try:
        # 2. تحديد رابط التحميل الذي سيتم استخدامه
        # إذا كان is_direct_download_link محددًا (من مربع الاختيار)، أو كان الرابط من mcpeland.io (نفترض أنه مباشر من Gemini)
        # أو كان من forgecdn أو ينتهي بـ .mcaddon/.mcpack
        is_mcpeland_url = "mcpeland.io" in initial_page_url.lower()

        if is_direct_download_link or \
           is_mcpeland_url or \
           "edge.forgecdn.net" in initial_page_url or \
           "curseforge.com" in initial_page_url.lower() or \
           initial_page_url.lower().endswith(('.mcaddon', '.mcpack')):

            if is_mcpeland_url:
                update_status(f"Treating mcpeland.io URL as direct download (from Gemini): {initial_page_url}")
            elif "curseforge.com" in initial_page_url.lower():
                update_status(f"Detected CurseForge URL. Using special handling: {initial_page_url}")
            elif is_direct_download_link:
                update_status(f"Treating as direct download link (checkbox checked): {initial_page_url}")
            else:
                update_status(f"Treating as direct download link (auto-detected type): {initial_page_url}")
            download_url_to_use = initial_page_url
        elif "9minecraft.net" in initial_page_url: # لا يزال التعامل مع 9minecraft كما هو
            update_status(f"Detected 9minecraft.net URL. Attempting to extract download link from page: {initial_page_url}")
            download_url_to_use = extract_mod_download_link(initial_page_url)
            if not download_url_to_use:
                raise ValueError("Failed to extract download link from 9minecraft.")
        else:
            update_status(f"Warning: URL '{initial_page_url}' is not a known page type and not marked as direct. Attempting as direct download.")
            download_url_to_use = initial_page_url

        if not download_url_to_use:
            raise ValueError("Download URL could not be determined.")

        # 3. تحميل محتوى المود من download_url_to_use
        update_status(f"Attempting to download mod from resolved URL: {download_url_to_use}")
        headers = {'User-Agent': 'Mozilla/5.0'}
        session = requests.Session()
        session.headers.update(headers)

        with session.get(download_url_to_use, stream=True, timeout=180) as response:
            response.raise_for_status()
            update_status(f"Download request OK (Status: {response.status_code}).")

            content_length_str = response.headers.get('content-length')
            max_size_bytes = MAX_MOD_SIZE_MB * 1024 * 1024

            mod_content_stream = BytesIO()
            total_downloaded = 0
            update_status("Starting download...")
            last_update_time = time.time()

            for chunk in response.iter_content(chunk_size=8192 * 2): # Increased chunk size
                if chunk:
                    total_downloaded += len(chunk)
                    if total_downloaded > max_size_bytes:
                        raise ValueError(f"Mod size ({total_downloaded/(1024*1024):.2f}MB) exceeds limit during download.")
                    mod_content_stream.write(chunk)
                    current_time = time.time()
                    if current_time - last_update_time > 1: # Update status every second
                        size_mb = total_downloaded / (1024*1024)
                        if content_length_str:
                            try:
                                total_size_header = int(content_length_str)
                                percent = (total_downloaded / total_size_header) * 100 if total_size_header > 0 else 0
                                update_status(f"DL {size_mb:.2f}/{total_size_header / (1024*1024):.2f} MB ({percent:.1f}%)")
                            except ValueError: # If content_length_str is not a valid int
                                update_status(f"DL {size_mb:.2f} MB... (Invalid Content-Length header)")
                                content_length_str = None # Avoid trying to parse again
                        else:
                            update_status(f"DL {size_mb:.2f} MB...")
                        last_update_time = current_time

            mod_bytes = mod_content_stream.getvalue()
            final_size_mb = total_downloaded / (1024*1024)
            update_status(f"Download OK. Final Size: {final_size_mb:.2f} MB")

            # التحقق من نوع المحتوى بعد التحميل
            content_type_final = response.headers.get('content-type', 'application/octet-stream').split(';')[0].strip().lower()
            update_status(f"Response Content-Type from headers: {content_type_final}")

            if 'text/html' in content_type_final:
                # حتى لو افترضنا أنه رابط مباشر، قد يعيد mcpeland صفحة HTML للخطأ أو شيء من هذا القبيل.
                update_status(f"!!! CRITICAL ERROR: Downloaded content from {download_url_to_use} is an HTML page (Content-Type: {content_type_final}), not a mod file. This can happen if the 'direct' link from Gemini was incorrect or led to an error page on mcpeland.io.")
                # يمكنك محاولة تحليل جزء من HTML هنا لتسجيل عنوان الصفحة للخطأ
                try:
                    soup_temp = BeautifulSoup(mod_bytes[:2048], 'html.parser')
                    if soup_temp.title and soup_temp.title.string:
                        update_status(f"HTML Page Title (for error context): {soup_temp.title.string.strip()}")
                except Exception: pass
                raise ValueError(f"Downloaded content is HTML, not a mod file. Source URL: {download_url_to_use}")

            # 4. تحديد اسم الملف ونوع المحتوى للرفع
            original_filename = "modfile" # Default
            content_disposition = response.headers.get('content-disposition')
            if content_disposition:
                update_status(f"Parsing CD: {content_disposition}")
                filename_match_utf8 = re.search(r'filename\*=UTF-8\'\'([\S]+)', content_disposition, flags=re.IGNORECASE)
                filename_match_basic = re.search(r'filename=([^;]+)', content_disposition, flags=re.IGNORECASE)
                if filename_match_utf8:
                    original_filename = requests.utils.unquote(filename_match_utf8.group(1))
                elif filename_match_basic:
                    original_filename = filename_match_basic.group(1).strip('"\' ')
            elif download_url_to_use:
                parsed_url_path = urlparse(download_url_to_use).path
                if parsed_url_path: original_filename = os.path.basename(parsed_url_path)

            sanitized_original_filename = sanitize_filename(original_filename)
            base_name, original_ext = os.path.splitext(sanitized_original_filename)
            original_ext_lower = original_ext.lower()

            final_upload_filename = sanitized_original_filename
            target_upload_content_type = content_type_final

            if original_ext_lower not in [".mcpack", ".mcaddon"]:
                if not base_name: base_name = "modfile"
                final_upload_filename = base_name + ".mcaddon"
                target_upload_content_type = 'application/octet-stream' # More generic for forced mcaddon
                update_status(f"Original extension '{original_ext_lower}' not .mcpack/.mcaddon. Forcing to .mcaddon: {final_upload_filename}")
            else:
                update_status(f"Using original extension: {final_upload_filename}")
                # إذا كان الامتداد صحيحًا ولكن نوع المحتوى عام جدًا، اضبطه
                if content_type_final == 'application/zip' or not content_type_final.startswith('application/'):
                     target_upload_content_type = 'application/octet-stream'


            update_status(f"Final filename for upload: {final_upload_filename}, Target Content-Type for upload: {target_upload_content_type}")

            # 5. رفع الملف إلى Supabase
            public_url = upload_to_supabase(MOD_BUCKET, mod_bytes, final_upload_filename, target_upload_content_type)
            return public_url, mod_bytes, extracted_version_str # extracted_version_str سيكون None هنا

    except requests.exceptions.RequestException as e:
        update_status(f"Download error for {download_url_to_use or initial_page_url}: {e}")
        return None, None, extracted_version_str
    except ValueError as e:
        update_status(f"Validation Error: {e}")
        return None, None, extracted_version_str
    except Exception as e:
        update_status(f"Unexpected error in process_mod: {e}")
        update_status(f"Traceback: {traceback.format_exc()}")
        return None, None, extracted_version_str


    except requests.exceptions.RequestException as e:
        update_status(f"Download error for {initial_page_url}: {e}")
        return None, None, extracted_version_str # Return version even if download fails
    except ValueError as e:
        update_status(f"Validation Error: {e}")
        return None, None, extracted_version_str # Return version even if validation fails
    except Exception as e:
        update_status(f"Unexpected error in process_mod: {e}")
        update_status(f"Traceback: {traceback.format_exc()}")
        return None, None, extracted_version_str # Return version even if other error


# --- NEW: 9minecraft Scraping Function (Improved) ---
def scrape_9minecraft_article(article_url):
    """Scrapes a 9minecraft article URL for mod details (Improved logic)."""
    update_status(f"\n--- بدء استخراج البيانات من: {article_url} ---")
    details = {
        "title": None, "description": None, "features": None,
        "primary_image": None, "other_images": [], "latest_mcaddon_link": None,
        "version": None,
        "full_scraped_text": None # NEW: To store combined text for AI
    }
    try:
        # Check if it's a CurseForge URL and use special handling
        is_curseforge = "curseforge.com" in article_url.lower()

        if is_curseforge:
            update_status("Detected CurseForge URL. Using special headers and handling.")
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'en-US,en;q=0.9',
                'Referer': 'https://www.curseforge.com/',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Cache-Control': 'max-age=0',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1',
                'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"'
            }

            # Create a session to maintain cookies
            session = requests.Session()
            session.headers.update(headers)

            # First, visit the page to get cookies
            update_status("Visiting CurseForge page to get cookies...")
            response = session.get(article_url, timeout=60)
            response.raise_for_status()
        else:
            # Regular handling for non-CurseForge URLs
            headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}
            response = requests.get(article_url, headers=headers, timeout=30)
            response.raise_for_status()

        update_status(f"تم جلب الصفحة (الحالة: {response.status_code}).")
        soup = BeautifulSoup(response.text, 'html.parser')

        # --- Find main content area ---
        content_selectors = ['div.post-content', 'div.entry-content', 'article.post', 'div#content']
        post_content = None
        for selector in content_selectors:
            post_content = soup.select_one(selector)
            if post_content:
                update_status(f"Found main content area using selector: '{selector}'")
                break
        if not post_content:
            update_status("!!! Could not find main content container using known selectors.")
            h1_tag_temp = soup.find('h1')
            if h1_tag_temp:
                post_content = h1_tag_temp.find_parent('div') or h1_tag_temp.find_parent('article')
                if post_content: update_status("Warning: Using fallback content container based on H1.")
                else: update_status("!!! Failed to find content container even with fallback.")
            else:
                 update_status("!!! No H1 found to attempt fallback content container search.")

        # --- 1. Title ---
        title_tag = None
        full_title = None
        if post_content:
            title_tag = post_content.find('h1', class_='post-title') or post_content.find('h1')
        if not title_tag:
            title_tag = soup.find('h1', class_='post-title') or soup.find('h1')

        if title_tag:
            update_status("Found title tag (h1).")
            full_title = title_tag.get_text(strip=True)
            update_status(f"Extracted full title: {full_title}")
            # Shorten title (improved splitting)
            short_title = re.split(r'\s*\(|\s*–|\s+Mod|\s+Addon|\s+Shader|\s+Texture Pack', full_title, maxsplit=1)[0].strip()
            details["title"] = short_title if short_title else full_title
            update_status(f"Shortened title: {details['title']}")
            # Extract version from full title
            version_match = re.search(r'\(?([\d\.]+)\)?$', full_title) # Look for version at the end
            if not version_match: version_match = re.search(r'\((\d+(\.\d+)+)\)', full_title) # Look for version in parentheses
            if version_match:
                details["version"] = version_match.group(1).strip()
                update_status(f"Extracted version from title: {details['version']}")
            else:
                update_status("Could not extract version from title.")
        else:
            update_status("!!! Could not find title tag (h1).")

        # --- 2. Description & Features (Improved Logic) ---
        desc_parts = []
        features_list = []
        all_text_parts_for_ai = [] # Store relevant text for AI prompt
        if post_content:
            update_status("Analyzing content for description and features...")
            in_features_section = False
            start_collecting_desc = False
            # Find the title tag within post_content to start searching after it
            content_title_tag = post_content.find('h1')
            current_element = content_title_tag.find_next_sibling() if content_title_tag else post_content.find(['p', 'div']) # Start after title or first p/div

            while current_element:
                if not hasattr(current_element, 'name'): # Skip NavigableStrings directly
                    current_element = current_element.find_next_sibling()
                    continue

                # Get text once for efficiency
                element_text = ""
                try:
                    # Handle potential errors if element is just a comment or similar
                    element_text = current_element.get_text(" ", strip=True)
                except AttributeError:
                    update_status(f"Skipping non-text element: {type(current_element)}")
                    current_element = current_element.find_next_sibling()
                    continue # Skip to next sibling if we can't get text

                element_text_lower = element_text.lower()

                # --- Stop Conditions ---
                stop_headings = ('screenshots', 'crafting recipes', 'installation note', 'how to install', 'download links', 'for minecraft pe/bedrock', 'for java edition', 'you may also like')
                # Check if the element itself or a child strong/h tag starts with a stop phrase
                is_stop_heading = False
                if current_element.name in ['h2', 'h3', 'h4', 'h5', 'strong', 'p']:
                     heading_text_check = element_text_lower
                     # Sometimes the heading is inside a nested tag like p > strong
                     strong_tag = current_element.find(['strong', 'h2', 'h3', 'h4'], recursive=False)
                     if strong_tag: heading_text_check = strong_tag.get_text(strip=True).lower()

                     if any(heading_text_check.startswith(phrase) for phrase in stop_headings):
                         is_stop_heading = True

                if is_stop_heading:
                    update_status(f"Reached stop section: '{element_text_lower[:30]}...'")
                    break

                # --- Start Collecting Description ---
                # Logic: Only start collecting *after* skipping known non-description elements like breadcrumbs and the meta line.
                if not start_collecting_desc:
                    # Check for breadcrumbs
                    is_breadcrumb = current_element.name == 'div' and ('breadcrumb' in current_element.get('class', []) or current_element.find('a', href=True))
                    # Check for the meta line (views/author/date) - Adjusted Regex slightly for flexibility
                    is_meta_line = current_element.name == 'p' and re.match(r'\s*\d{1,3}(?:,\d{3})*\s+views.*author:', element_text_lower, re.IGNORECASE)

                    if is_breadcrumb:
                        update_status("Skipping potential breadcrumb div.")
                    elif is_meta_line:
                        update_status("Skipping meta info paragraph (views/author).")
                    else:
                        # If it's not breadcrumbs or the meta line, we can start collecting
                        start_collecting_desc = True
                        update_status("Starting description collection from this element.")
                        # --- Process the *current* element now that collection has started ---
                        # (This avoids skipping the very first description paragraph)
                        # --- Detect Features Heading ---
                        is_features_heading = False
                        if not in_features_section and current_element.name in ['strong', 'h3', 'p']:
                            heading_text_check = element_text_lower
                            strong_tag = current_element.find(['strong', 'h3'], recursive=False)
                            if strong_tag: heading_text_check = strong_tag.get_text(strip=True).lower()
                            if re.match(r'features:?', heading_text_check):
                                is_features_heading = True

                        if is_features_heading:
                            in_features_section = True
                            update_status("Found features section.")
                            if element_text: all_text_parts_for_ai.append(element_text)
                        # --- Process based on section ---
                        elif in_features_section:
                            # Extract features (handle paragraphs and lists)
                            if current_element.name == 'ul':
                                for li in current_element.find_all('li', recursive=False):
                                    feature_text = li.get_text(strip=True)
                                    if feature_text:
                                        features_list.append(f"- {feature_text}")
                                        all_text_parts_for_ai.append(feature_text)
                            elif element_text:
                                features_list.append(element_text)
                                all_text_parts_for_ai.append(element_text)
                        else: # Description section
                            if element_text:
                                desc_parts.append(element_text)
                                all_text_parts_for_ai.append(element_text)
                        # --- End processing current element ---
                elif start_collecting_desc: # This 'elif' handles subsequent elements after start
                    # --- Detect Features Heading ---
                    is_features_heading = False
                    if not in_features_section and current_element.name in ['strong', 'h3', 'p']:
                        heading_text_check = element_text_lower
                        strong_tag = current_element.find(['strong', 'h3'], recursive=False)
                        if strong_tag: heading_text_check = strong_tag.get_text(strip=True).lower()
                        if re.match(r'features:?', heading_text_check):
                            is_features_heading = True

                    if is_features_heading:
                        in_features_section = True
                        update_status("Found features section.")
                        # Add the heading itself to the AI text, but not features list
                        if element_text: all_text_parts_for_ai.append(element_text)

                    # --- Process based on section ---
                    elif in_features_section:
                        # Extract features (handle paragraphs and lists)
                        if current_element.name == 'ul':
                            for li in current_element.find_all('li', recursive=False):
                                feature_text = li.get_text(strip=True)
                                if feature_text:
                                    features_list.append(f"- {feature_text}")
                                    all_text_parts_for_ai.append(feature_text) # Add to AI text
                        elif element_text: # Add paragraphs or other text within features
                            features_list.append(element_text)
                            all_text_parts_for_ai.append(element_text) # Add to AI text

                    else: # Description section
                        # Add paragraphs, skip empty ones
                        if element_text:
                            desc_parts.append(element_text)
                            all_text_parts_for_ai.append(element_text) # Add to AI text

                current_element = current_element.find_next_sibling() # Move to next sibling

            details["description"] = "\n".join(desc_parts).strip() if desc_parts else None
            details["features"] = "\n".join(features_list).strip() if features_list else None
            details["full_scraped_text"] = "\n".join(all_text_parts_for_ai).strip() if all_text_parts_for_ai else None # Store combined text

            if details["description"]: update_status("Extracted description.")
            else: update_status("Could not extract description.")
            if details["features"]: update_status("Extracted features.")
            else: update_status("Could not extract features.")
            if details["full_scraped_text"]: update_status("Stored combined text for AI.")

        else:
            update_status("!!! Main content area not found, cannot extract description/features.")

        # --- 3. Images (Keep previous logic, seems okay) ---
        all_images = []
        if post_content:
            update_status("Searching for images within post content...")
            for img in post_content.find_all('img'):
                src = img.get('src') or img.get('data-src')
                if src and src.startswith('http'):
                    src_lower = src.lower()
                    if ('ads' not in src_lower and 'logo' not in src_lower and
                        'feedburner' not in src_lower and 'download' not in src_lower and
                        not src_lower.endswith(('.gif', '.ico'))):
                        parent_a = img.find_parent('a')
                        is_download_link_image = False
                        if parent_a:
                            link_text = parent_a.get_text(strip=True).lower()
                            if 'download from server' in link_text or 'download link' in link_text:
                                is_download_link_image = True
                        if not is_download_link_image:
                            if src not in all_images:
                                all_images.append(src)
                                # update_status(f"Adding image: {src}") # Reduce log spam
                        # else: update_status(f"Skipping image (likely download button): {src}") # Reduce log spam

        if all_images:
            details["primary_image"] = all_images[0]
            details["other_images"] = all_images[1:7]
            update_status(f"Extracted {len(all_images)} candidate images. Primary: {details['primary_image']}")
            update_status(f"Other images extracted: {len(details['other_images'])}")
        else:
            update_status("!!! No suitable images found in the article.")

        # --- 4. Download Link (Improved Logic) ---
        update_status("Searching for download links...")
        target_link = None
        version_pattern_dl = re.compile(r'(?:For|Requires)\s+Minecraft\s+(?:PE/Bedrock|Pocket Edition)\s+([\d\.]+)\+?', re.IGNORECASE)

        # Check if it's a CurseForge URL and use appropriate download link pattern
        if is_curseforge:
            update_status("Using CurseForge download link pattern.")
            download_link_pattern = re.compile(r'https?://(?:[^/]*\.)?(?:edge\.forgecdn\.net|media\.forgecdn\.net|curseforge\.com/download)/.*', re.IGNORECASE)
        else:
            # Regular pattern for 9minecraft
            download_link_pattern = re.compile(r'https?://(?:[^/]*\.)?(?:cdn9mc\.com|9minecraft\.net/linkout)/.*', re.IGNORECASE)

        # Special handling for CurseForge
        if is_curseforge:
            update_status("Using CurseForge-specific download button detection.")
            potential_links = []

            # Look for download buttons with specific classes
            download_buttons = soup.find_all('a', class_=lambda c: c and ('button--download' in c or 'download-button' in c))
            if download_buttons:
                update_status(f"Found {len(download_buttons)} potential CurseForge download buttons.")
                potential_links.extend(download_buttons)

            # If no specific download buttons found, look for any links with 'download' in text or class
            if not potential_links:
                update_status("No specific download buttons found. Looking for any download links.")
                for a_tag in soup.find_all('a'):
                    if a_tag.has_attr('href') and (
                        'download' in a_tag.get_text().lower() or
                        (a_tag.has_attr('class') and any('download' in c.lower() for c in a_tag['class']))
                    ):
                        potential_links.append(a_tag)

            # If still no links found, use the download link pattern as fallback
            if not potential_links:
                update_status("No download links found by text/class. Using URL pattern as fallback.")
                potential_links = soup.find_all('a', href=download_link_pattern)
        else:
            # Regular handling for non-CurseForge URLs
            # Find potential download sections/headers
            download_headers = soup.find_all(['h3', 'strong', 'p'], text=re.compile(r'Download Links|For Minecraft PE/Bedrock', re.IGNORECASE))

            if not download_headers:
                 update_status("Could not find typical download section headers. Searching page widely.")
                 # Fallback: search all links if no headers found
                 potential_links = soup.find_all('a', href=download_link_pattern)
            else:
                 update_status(f"Found {len(download_headers)} potential download section header(s).")
                 # Search within and after these headers
                 potential_links = []
                 for header in download_headers:
                     current = header
                     while current:
                         if hasattr(current, 'find_all'):
                             potential_links.extend(current.find_all('a', href=download_link_pattern))
                         # Stop searching if we hit another major section like 'You may also like'
                         if hasattr(current, 'get_text') and 'you may also like' in current.get_text(strip=True).lower():
                             break
                         current = current.find_next_sibling()


        update_status(f"Found {len(potential_links)} potential download links matching pattern.")
        android_link = None
        windows_link = None
        generic_link = None

        for link in potential_links:
            href = link['href']
            if not href.startswith('http'): href = urljoin(article_url, href) # Ensure absolute URL

            # Check context (parent text) for OS indication
            parent_text = link.parent.get_text(" ", strip=True).lower() if link.parent else ""
            link_text = link.get_text(" ", strip=True).lower()

            is_valid_dl_link = download_link_pattern.match(href) is not None

            if is_valid_dl_link:
                if 'android' in parent_text or 'android' in link_text:
                    if not android_link: # Take the first android link
                        android_link = href
                        update_status(f"Found potential Android link: {href}")
                elif 'windows' in parent_text or 'windows' in link_text:
                     if not windows_link: # Take the first windows link
                         windows_link = href
                         update_status(f"Found potential Windows link: {href}")
                elif not generic_link: # Keep the first generic download link as fallback
                     generic_link = href
                     update_status(f"Found potential generic link: {href}")

        # Prioritize Android > Windows > Generic
        if android_link:
            target_link = android_link
            update_status(f"Selected Android download link: {target_link}")
        elif windows_link:
            target_link = windows_link
            update_status(f"Selected Windows download link: {target_link}")
        elif generic_link:
            target_link = generic_link
            update_status(f"Selected generic download link: {target_link}")
        else:
            # Last fallback: Check the mcaddon: pattern again if nothing else worked
            update_status("No specific OS links found. Checking for 'mcaddon:' pattern as last resort...")
            mcaddon_texts = soup.find_all(text=re.compile(r'mcaddon:', re.IGNORECASE))
            last_mcaddon_link = None
            for text_node in mcaddon_texts:
                 next_a = text_node.find_next('a')
                 if next_a and next_a.has_attr('href'):
                     href = next_a['href']
                     absolute_url = urljoin(article_url, href)
                     if download_link_pattern.match(absolute_url):
                         last_mcaddon_link = absolute_url
            if last_mcaddon_link:
                target_link = last_mcaddon_link
                update_status(f"Selected 'mcaddon:' pattern link as fallback: {target_link}")
            else:
                update_status("!!! Failed to find any suitable download link.")


        details["latest_mcaddon_link"] = target_link

        update_status("--- Data extraction finished ---")
        return details
    except requests.exceptions.RequestException as e:
        update_status(f"!!! Network error fetching page: {e}")
    except Exception as e:
        update_status(f"!!! Unexpected error during scraping: {e}")
        update_status(f"Traceback: {traceback.format_exc()}")
    return None # Return None on failure


# --- NEW: Gemini Data Extraction Function ---
def extract_data_with_gemini_task(article_url):
    """Calls Gemini API to extract structured data from the article URL, with caching and improved JSON cleaning."""
    global gemini_model, current_gemini_key_index, GEMINI_CLIENT_OK, gemini_extraction_cache

    if article_url in gemini_extraction_cache:
        update_status(f"Using cached Gemini extraction data for URL: {article_url}")
        return gemini_extraction_cache[article_url]

    if not GEMINI_CLIENT_OK:
        update_status("!!! Gemini client not configured. Cannot extract data.")
        if 'window' in globals() and window.winfo_exists(): # Check if window exists for messagebox
            messagebox.showerror("Gemini Error", "Gemini is not configured correctly.")
        return None

    update_status(f"Fetching article content for Gemini: {article_url}")
    try:
        # Check if it's a CurseForge URL and use special handling
        is_curseforge = "curseforge.com" in article_url.lower()

        if is_curseforge:
            update_status("Detected CurseForge URL. Using direct API approach...")

            # Extract the project ID and slug from the URL
            # Example URL: https://www.curseforge.com/minecraft-bedrock/addons/more-body-actions-cf
            try:
                # Parse the URL to get the project slug
                parsed_url = urlparse(article_url)
                path_parts = parsed_url.path.strip('/').split('/')

                if len(path_parts) >= 3:
                    # The last part of the path is usually the project slug
                    project_slug = path_parts[-1]
                    update_status(f"Extracted project slug: {project_slug}")

                    # Instead of trying to scrape the page, we'll construct a direct download URL
                    # This is a workaround since we can't easily scrape CurseForge due to their protections

                    # Try to fetch the actual page with a more browser-like approach
                    try:
                        # Use a more sophisticated browser-like request
                        headers = {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                            'Accept-Language': 'en-US,en;q=0.9',
                            'Referer': 'https://www.google.com/',
                            'Connection': 'keep-alive',
                            'Upgrade-Insecure-Requests': '1',
                            'Cache-Control': 'max-age=0',
                            'Sec-Fetch-Dest': 'document',
                            'Sec-Fetch-Mode': 'navigate',
                            'Sec-Fetch-Site': 'cross-site',
                            'Sec-Fetch-User': '?1',
                            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120"',
                            'sec-ch-ua-mobile': '?0',
                            'sec-ch-ua-platform': '"Windows"',
                            'DNT': '1',
                            'Pragma': 'no-cache'
                        }

                        # Try to use cloudscraper if available
                        try:
                            import cloudscraper
                            update_status("Using cloudscraper to bypass Cloudflare protection")
                            scraper = cloudscraper.create_scraper(
                                browser={
                                    'browser': 'chrome',
                                    'platform': 'windows',
                                    'desktop': True
                                },
                                delay=10
                            )
                            scraper.headers.update(headers)

                            # First visit the homepage to get cookies
                            update_status("Visiting CurseForge homepage to get cookies...")
                            scraper.get('https://www.curseforge.com/', timeout=60)

                            # Now visit the actual page
                            update_status("Visiting CurseForge project page...")
                            response = scraper.get(article_url, timeout=60)

                            if response.status_code == 200:
                                update_status("Successfully fetched CurseForge page with cloudscraper!")
                                page_html = response.text

                                # Try to extract images from the page
                                soup = BeautifulSoup(page_html, 'html.parser')

                                # Extract title
                                title_element = soup.find('h1', class_='project-title')
                                mod_title = project_slug.replace('-', ' ').title()
                                if title_element:
                                    mod_title = title_element.get_text(strip=True)

                                # Extract description
                                description = ""
                                desc_element = soup.find('div', class_='project-description')
                                if desc_element:
                                    description = desc_element.get_text(strip=True)

                                # Extract images
                                image_urls = []

                                # Look for gallery images
                                gallery_images = soup.select('.gallery-image img')
                                for img in gallery_images:
                                    if img.has_attr('src'):
                                        image_urls.append(img['src'])
                                    elif img.has_attr('data-src'):
                                        image_urls.append(img['data-src'])

                                # Look for main project image
                                main_image = soup.select_one('.project-avatar img')
                                if main_image and main_image.has_attr('src'):
                                    image_urls.insert(0, main_image['src'])

                                # Look for images in the description
                                desc_images = soup.select('.project-description img')
                                for img in desc_images:
                                    if img.has_attr('src'):
                                        image_urls.append(img['src'])
                                    elif img.has_attr('data-src'):
                                        image_urls.append(img['data-src'])

                                # Create HTML with the extracted information
                                article_html = f"""
                                <html>
                                <head><title>{mod_title}</title></head>
                                <body>
                                    <h1>{mod_title}</h1>
                                    <div class="project-description">
                                        {description}
                                    </div>
                                    <div class="gallery">
                                """

                                # Add images to the HTML
                                for img_url in image_urls:
                                    article_html += f'<img src="{img_url}" alt="Mod image" />\n'

                                article_html += f"""
                                    </div>
                                    <div>
                                        <a href="{article_url}/download" class="button button--download">Download</a>
                                    </div>
                                </body>
                                </html>
                                """

                                update_status(f"Created HTML with {len(image_urls)} images from CurseForge page")
                            else:
                                raise Exception(f"Failed to fetch page: Status code {response.status_code}")

                        except ImportError:
                            # If cloudscraper is not available, try a direct approach
                            update_status("cloudscraper not available, trying direct approach with requests...")

                            # Try to fetch the page directly
                            session = requests.Session()
                            session.headers.update(headers)

                            # Try to get the gallery page which might be less protected
                            gallery_url = f"{article_url}/screenshots"
                            update_status(f"Trying to fetch gallery page: {gallery_url}")

                            try:
                                gallery_response = session.get(gallery_url, timeout=60)
                                if gallery_response.status_code == 200:
                                    update_status("Successfully fetched gallery page!")

                                    # Parse the gallery page to extract images
                                    gallery_soup = BeautifulSoup(gallery_response.text, 'html.parser')
                                    gallery_images = gallery_soup.select('.screenshot img')

                                    image_urls = []
                                    for img in gallery_images:
                                        if img.has_attr('src'):
                                            image_urls.append(img['src'])
                                        elif img.has_attr('data-src'):
                                            image_urls.append(img['data-src'])

                                    # Create HTML with the extracted information and images
                                    article_html = f"""
                                    <html>
                                    <head><title>{project_slug.replace('-', ' ').title()}</title></head>
                                    <body>
                                        <h1>{project_slug.replace('-', ' ').title()}</h1>
                                        <p>This is a Minecraft Bedrock mod from CurseForge.</p>
                                        <p>Project URL: {article_url}</p>
                                        <div class="gallery">
                                    """

                                    # Add images to the HTML
                                    for img_url in image_urls:
                                        article_html += f'<img src="{img_url}" alt="Mod image" />\n'

                                    article_html += f"""
                                        </div>
                                        <div>
                                            <a href="{article_url}/download" class="button button--download">Download</a>
                                        </div>
                                    </body>
                                    </html>
                                    """

                                    update_status(f"Created HTML with {len(image_urls)} images from gallery page")
                                else:
                                    raise Exception(f"Failed to fetch gallery page: Status code {gallery_response.status_code}")
                            except Exception as gallery_e:
                                update_status(f"Error fetching gallery page: {gallery_e}")

                                # Fallback to a simple HTML with no images
                                article_html = f"""
                                <html>
                                <head><title>{project_slug.replace('-', ' ').title()}</title></head>
                                <body>
                                    <h1>{project_slug.replace('-', ' ').title()}</h1>
                                    <p>This is a Minecraft Bedrock mod from CurseForge.</p>
                                    <p>Project URL: {article_url}</p>
                                    <p>Due to CurseForge's anti-bot protection, we cannot extract the full description or images.</p>
                                    <p>Please visit the original page for more details.</p>
                                    <div>
                                        <a href="{article_url}/download" class="button button--download">Download</a>
                                    </div>
                                </body>
                                </html>
                                """
                    except Exception as e:
                        update_status(f"Error trying to fetch CurseForge page: {e}")
                        update_status(f"Traceback: {traceback.format_exc()}")

                        # Fallback to a simple HTML with no images
                        article_html = f"""
                        <html>
                        <head><title>{project_slug.replace('-', ' ').title()}</title></head>
                        <body>
                            <h1>{project_slug.replace('-', ' ').title()}</h1>
                            <p>This is a Minecraft Bedrock mod from CurseForge.</p>
                            <p>Project URL: {article_url}</p>
                            <p>Due to CurseForge's anti-bot protection, we cannot extract the full description or images.</p>
                            <p>Please visit the original page for more details.</p>
                            <div>
                                <a href="{article_url}/download" class="button button--download">Download</a>
                            </div>
                        </body>
                        </html>
                        """

                    update_status(f"Created simplified HTML for CurseForge project: {project_slug}")
                else:
                    update_status("!!! Could not extract project slug from CurseForge URL.")
                    article_html = f"<html><body><p>Error: Could not parse CurseForge URL: {article_url}</p></body></html>"
            except Exception as e:
                update_status(f"!!! Error processing CurseForge URL: {e}")
                update_status(f"Traceback: {traceback.format_exc()}")
                article_html = f"<html><body><p>Error processing CurseForge URL: {e}</p></body></html>"
        else:
            # Regular handling for non-CurseForge URLs
            headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}
            response = requests.get(article_url, headers=headers, timeout=45)
            response.raise_for_status()
            article_html = response.text

        update_status(f"Fetched article HTML (length: {len(article_html)}).")
    except requests.exceptions.RequestException as e:
        update_status(f"!!! Failed to fetch article HTML for Gemini: {e}")
        if 'window' in globals() and window.winfo_exists():
             messagebox.showerror("Network Error", f"Could not fetch article content: {e}")
        return None
    except Exception as e:
        update_status(f"!!! Error processing article content for Gemini: {e}")
        if 'window' in globals() and window.winfo_exists():
            messagebox.showerror("Error", f"Could not process article content: {e}")
        return None

    prompt = f"""
    Analyze the following **full HTML source code** of a Minecraft article page from the URL {article_url}:
    ```html
    {article_html}
    ```
    Extract the following information by parsing the HTML structure and provide it ONLY in JSON format. Do not add any introductory text or explanations outside the JSON structure.
    JSON Structure:
    {{
      "mod_name": "The short, user-friendly name of the mod/addon/pack (e.g., 'Furniture Mod', not the full title with version)",
      "version": "The specific version number mentioned (e.g., '1.21', '1.20.5', '1.19'. Format as X.Y or X.Y.Z. If a 'v' prefix is found, remove it. Null if not found).",
      "full_mod_description": "The complete description of the mod as presented on the page. Extract all relevant paragraphs that describe what the mod is and does. Null if not found.",
      "mod_features": "A list of key features of the mod, extracted from sections like 'Features:' or bullet points. Each feature should be a string in the list. Null or empty list if not found.",
      "primary_image_url": "The URL of the main representative image (usually the first one, ensure it's a direct image link, or null)",
      "other_image_urls": [
        "URL of the second image (direct link, or null)",
        "URL of the third image (direct link, or null)",
        "URL of the fourth image (direct link, or null)",
        "URL of the fifth image (direct link, or null)",
        "URL of the sixth image (direct link, or null)",
        "URL of the seventh image (direct link, or null)"
      ],
      "download_link": "The main download page link (usually a cdn9mc or 9minecraft linkout URL, or null if only BP/RP found)",
      "bp_download_link": "The specific Behavior Pack (BP) download page link (cdn9mc/linkout URL, if explicitly mentioned, otherwise null)",
      "rp_download_link": "The specific Resource Pack (RP) download page link (cdn9mc/linkout URL, if explicitly mentioned, otherwise null)",
      "creator_name": "The name of the mod creator/author. Look for 'Author:', 'Creator:', 'By:', or similar indicators near the top or bottom of the article. Null if not found.",
      "creator_contact_info": "Any direct contact information for the creator, such as an email address or personal website link. Null if not found.",
      "custom_social_sites": [
        {{
          "site_name": "Name of creator's custom platform or website (e.g., 'Discord Server', 'YouTube Channel', 'Personal Website', 'Telegram Channel')",
          "site_url": "URL of the creator's custom platform or website"
        }}
      ]
    }}
    Instructions for Parsing HTML:
    - **Carefully analyze the entire HTML structure** to find the most relevant information.
    - For 'mod_name': Look for the main heading, often an `<h1>` tag (e.g., `<h1 class="post-title">...</h1>`). Extract the core name from the text content, excluding version numbers, Minecraft versions, and generic terms like 'Mod', 'Addon', 'Texture Pack', 'Shader'. Example: If heading is "Awesome Cars Mod (1.20.1)", extract "Awesome Cars".
    - For 'version': Find the specific mod/pack version number (e.g., '1.20.1', '3.5', 'v2', '1.21'). Look in the main heading text or near download links. Format as X.Y or X.Y.Z (e.g., '1.21', not 'v1.21' or 'v121'). If multiple Minecraft versions are mentioned (like 1.19/1.18), try to find the specific *mod* version. If none found, use null.
    - For 'full_mod_description': Locate the main descriptive text of the mod. This is usually found in `<p>` tags after the main title and before sections like 'Features', 'Screenshots', or 'Download Links'. Concatenate all relevant paragraphs into a single string. Ensure to capture the complete description.
    - For 'mod_features': Look for sections explicitly titled 'Features' (often in `<h3>` or `<strong>` tags). Extract text from subsequent `<p>` tags or `<li>` items within `<ul>` lists under this heading. Store each feature as a string in an array. Capture all listed features.
    - For 'primary_image_url': Find the *first* relevant `<img>` tag within the main content area (often a `div` with class 'post-content' or 'entry-content'). Get the URL from its `src` or `data-src` attribute. Ensure it's a direct link ending in .jpg, .png, .webp, etc. Avoid logos, ads, or images inside download buttons. If none found, use null.
    - For 'other_image_urls': Find the next 6 relevant `<img>` tags after the primary one within the main content, following the same criteria. Extract their URLs and add them to the list. If fewer than 6 additional images are found, only include the ones found. Ensure all URLs are absolute and are direct image links.
    - For 'download_link', 'bp_download_link', 'rp_download_link':
        - **Identify all Minecraft PE/Bedrock versions mentioned in download sections** (e.g., look for patterns like "For MCPE/Bedrock 1.21", "For 1.20", etc.).
        - **Determine the LATEST (highest) version number** found among these sections (e.g., if 1.21, 1.20, and 1.19 are found, the latest is 1.21).
        - **PRIORITIZE extracting links ONLY from the section corresponding to the LATEST version.**
        - Within the LATEST version's section, look for text labels like "mcaddon:", "Behavior:", "BP:", "Resource:", "RP:" immediately preceding the download links (`<a>` tags).
        - Extract the `href` attribute from the first valid `<a>` tag (containing 'cdn9mc.com' or '9minecraft.net/linkout') found *after* the label within the LATEST version's section.
        - Populate the JSON fields based on the labels and links found **ONLY for the LATEST version**:
            - If "mcaddon:" is found for the latest version, put its link in "download_link". Set "bp_download_link" and "rp_download_link" to null.
            - If "Behavior:"/"BP:" is found for the latest version, put its link in "bp_download_link".
            - If "Resource:"/"RP:" is found for the latest version, put its link in "rp_download_link".
            - If both BP and RP links are found for the latest version, populate both "bp_download_link" and "rp_download_link", and set "download_link" to null.
        - **If NO download links are found specifically under the LATEST version heading**, then leave all three download fields ("download_link", "bp_download_link", "rp_download_link") as null. Do NOT fall back to older versions.
    - **For 'creator_name', 'creator_contact_info', 'custom_social_sites'**:
        - **IMPORTANT**: IGNORE all social media links that appear at the top/header of the page (like Facebook, Twitter share buttons) as these belong to the website itself, NOT the mod creator.
        - **IMPORTANT**: IGNORE social media links in the website's navigation, footer, or sidebar areas.
        - **FOCUS ONLY** on creator-specific information that appears in the main article content or in dedicated creator/author sections.
        - Look for sections like "Author:", "Creator:", "By:", "Contact:", "Support:", "Follow me:", "Join my Discord:", "Subscribe to my YouTube:", etc.
        - Extract the name associated with "Author", "Creator", or "By" (e.g., "By Sugger" → extract "Sugger").
        - For `creator_contact_info`: Extract any direct contact information, communication instructions, or calls to action from the creator such as:
            * Email addresses
            * Personal website links
            * Messages like "For support, join my Discord server"
            * "Subscribe to my YouTube channel"
            * "Follow me on Twitter for updates"
            * Any text where the creator asks users to contact them or follow them
        - For `custom_social_sites`: Look for creator's personal social media platforms, communication channels, or websites mentioned in the article content. This includes:
            * Discord servers (creator's own server, not general gaming Discord)
            * YouTube channels (creator's channel, not random videos)
            * Twitter/X profiles (creator's profile, not website's social media)
            * Telegram channels/groups (creator's own)
            * Personal websites or blogs
            * Custom forums or community sites
            * Patreon pages
            * Any platform where the creator specifically asks users to contact them or follow them
        - For each custom social site, extract both the platform name (e.g., "Discord Server", "YouTube Channel", "Personal Website") and the URL.
        - **AVOID**: Generic website social media, share buttons, website navigation links, or any social media that belongs to the hosting website rather than the mod creator.
    - **Output ONLY the JSON structure.** Do not include any text before or after the JSON object. Use `null` for any value that cannot be found according to the instructions above.
    """
    retries = 0
    success = False
    extracted_data = None
    response_text_for_error_log = "" # Store response text for logging on error

    while retries < MAX_GEMINI_RETRIES and not success:
        if not GEMINI_CLIENT_OK:
            update_status("!!! Gemini client not configured. Cannot extract data.")
            break
        try:
            update_status(f"إرسال طلب استخراج البيانات إلى Gemini (باستخدام مفتاح {current_gemini_key_index})...")
            update_status(">>> Calling gemini_model.generate_content...")
            response = gemini_model.generate_content(prompt)
            update_status("<<< Call to gemini_model.generate_content returned.")

            response_text = response.text.strip()
            response_text_for_error_log = response_text # Save for potential error logging
            update_status(f"Raw Gemini Response Text (len={len(response_text)}): {response_text[:500]}...")

            update_status("Attempting to parse JSON from response...")

            # Clean potential markdown code block fences
            if response_text.startswith("```json"):
                response_text = response_text[7:]
            if response_text.endswith("```"):
                response_text = response_text[:-3]
            response_text = response_text.strip()

            # 1. Specific fix for '\\\\&' which becomes '\\&' in string literals
            if '\\\\&' in response_text:
                response_text = response_text.replace('\\\\&', '&')
                update_status("Applied specific '\\\\&' to '&' conversion.")

            # 2. General fix for '\xHH' if they appear as string literals (e.g. "\xa0")
            # This regex looks for literal "\x" followed by two hex chars in the string
            # and converts to a standard JSON unicode escape "\u00HH".
            if r'\x' in response_text: # Check for literal '\x'
                response_text = re.sub(r'\\x([0-9a-fA-F]{2})', r'\\u00\1', response_text)
                update_status("Applied general '\\xHH' to '\\u00HH' string escape conversion.")

            extracted_data = json.loads(response_text)
            update_status("--> تم تحليل بيانات JSON بنجاح من Gemini.")

            required_keys = ["mod_name", "version", "full_mod_description", "mod_features",
                             "primary_image_url", "other_image_urls", "download_link",
                             "bp_download_link", "rp_download_link"]
            if all(key in extracted_data for key in required_keys):
                 update_status("JSON response contains all expected keys.")
            else:
                 missing_keys = [key for key in required_keys if key not in extracted_data]
                 update_status(f"!!! Warning: JSON response is missing some expected keys: {missing_keys}")

            gemini_extraction_cache[article_url] = extracted_data
            update_status(f"Cached Gemini extraction data for URL: {article_url}")
            success = True
        except json.JSONDecodeError as json_e:
            update_status(f"!!! فشل تحليل JSON من رد Gemini: {json_e}")
            update_status(f"Raw Gemini Response that failed parsing:\n{response_text_for_error_log}") # Log the problematic text
            if 'window' in globals() and window.winfo_exists():
                messagebox.showerror("Gemini Error", f"Failed to parse JSON response from Gemini: {json_e}")
            break
        except Exception as e:
            error_type = type(e).__name__
            error_message = f"!!! خطأ ({error_type}) أثناء استخراج البيانات بواسطة Gemini (المفتاح {current_gemini_key_index}): {e}"
            update_status(error_message)
            if hasattr(e, 'response') and hasattr(e.response, 'text'):
                 update_status(f"Gemini API Response Text (from exception): {e.response.text}")
            update_status(f"Traceback: {traceback.format_exc()}")
            is_rate_limit = "rate limit" in str(e).lower() or "quota" in str(e).lower() or "resource has been exhausted" in str(e).lower() or "429" in str(e)
            if is_rate_limit:
                update_status(f"!!! تم الوصول إلى حد المعدل للمفتاح {current_gemini_key_index}. محاولة استخدام المفتاح التالي...")
                retries += 1
                if not configure_gemini_client(current_gemini_key_index + 1):
                    if 'window' in globals() and window.winfo_exists():
                        messagebox.showerror("خطأ Gemini", "فشل استخراج البيانات: تم استنفاد جميع مفاتيح API أو فشل التكوين.")
                    break
            else:
                if 'window' in globals() and window.winfo_exists():
                    messagebox.showerror("خطأ Gemini", f"فشل استخراج البيانات: {e}")
                break
    if not success:
        update_status("!!! فشل استخراج البيانات بواسطة Gemini بعد كل المحاولات.")
        return None
    return extracted_data


# --- Handler for Scraping ---
def handle_scrape_article():
    article_url = scrape_url_entry.get().strip()
    if not article_url:
        messagebox.showerror("خطأ", "الرجاء إدخال رابط المقال.") # Generic message
        return

    # Basic URL validation (allow any http/https URL)
    parsed_url = urlparse(article_url)
    if not all([parsed_url.scheme, parsed_url.netloc]) or parsed_url.scheme not in ['http', 'https']:
         messagebox.showerror("خطأ", "الرابط المدخل لا يبدو كرابط URL صالح.") # Generic message
         return

    update_status(f"بدء استخراج البيانات من المقال: {article_url}")
    scrape_button.config(state=tk.DISABLED)
    # Use the new Gemini extraction task
    run_in_thread(scrape_article_task_gemini, article_url)

def scrape_article_task_gemini(article_url):
    """Task wrapper for Gemini data extraction and GUI update."""
    global last_scraped_data
    last_scraped_data = None
    extracted_data = None
    try:
        extracted_data = extract_data_with_gemini_task(article_url)

        if extracted_data:
            update_status("Data extracted via Gemini successfully. Updating fields...")
            last_scraped_data = extracted_data # Save for AI description context

            # Auto-detect category and add it to extracted_data before GUI update
            detected_category_from_url = detect_category_from_url(article_url, CATEGORIES)
            if detected_category_from_url:
                extracted_data["category"] = detected_category_from_url
                update_status(f"Auto-detected category from URL: {detected_category_from_url}")
            else:
                update_status("Could not auto-detect category from URL. Will rely on Gemini or manual input if category was not in Gemini's output.")
                if "category" not in extracted_data:
                    extracted_data["category"] = None # Ensure key exists if detection failed

            # Update GUI on main thread
            if 'window' in globals() and window.winfo_exists():
                widgets_to_update = {
                    'name': publish_name_entry,
                    'version': publish_version_entry,
                    'primary_img': publish_primary_image_entry, # For clearing Section 5
                    'other_imgs': publish_other_images_text,   # For clearing Section 5
                    'mod_page_url': mod_page_url_entry,      # Section 3
                    'bp_url': bp_url_entry,                  # Section 3b
                    'rp_url': rp_url_entry,                  # Section 3b
                    'category_combobox': category_combobox,    # Section 5
                    'desc': publish_desc_text,               # Section 5 (publish_desc_text for clearing)
                    'features': mod_features_text            # Section 4 (mod_features_text for clearing)
                }
                # *** CRITICAL: Pass article_url to update_gui_with_gemini_data ***
                window.after(0, update_gui_with_gemini_data, extracted_data, widgets_to_update, article_url)
            else:
                 update_status("Error: Cannot update GUI (window not found).")

            # --- Attempt Automatic Description Generation ---
            update_status("Attempting automatic description generation using Gemini's extracted content...")
            mod_name_for_desc = extracted_data.get("mod_name")
            # Use full_mod_description as the primary scraped text for AI generation
            scraped_text_for_desc_ai = extracted_data.get("full_mod_description")
            # Use mod_features as the manual_features input for AI generation
            manual_features_for_desc_ai_list = extracted_data.get("mod_features")
            manual_features_for_desc_ai_str = ""
            if isinstance(manual_features_for_desc_ai_list, list) and manual_features_for_desc_ai_list:
                manual_features_for_desc_ai_str = "\n".join([f"- {feat}" for feat in manual_features_for_desc_ai_list if feat and isinstance(feat, str)])

            mod_category_for_desc = CATEGORIES[0] # Default
            if 'category_combobox' in globals() and category_combobox.winfo_exists(): # Get current category from GUI
                mod_category_for_desc = category_combobox.get() or CATEGORIES[0]
            elif extracted_data.get("category") and extracted_data.get("category") in CATEGORIES: # Fallback to detected
                mod_category_for_desc = extracted_data.get("category")


            if mod_name_for_desc and (scraped_text_for_desc_ai or manual_features_for_desc_ai_str):
                if 'generate_desc_button' in globals() and generate_desc_button.winfo_exists():
                    generate_desc_button.config(state=tk.DISABLED) # Disable button before thread
                # Pass extracted description and features to the AI task
                run_in_thread(generate_description_task, mod_name_for_desc, mod_category_for_desc, scraped_text_for_desc_ai, manual_features_for_desc_ai_str)

                # إضافة إنشاء الوصف العربي التلقائي
                update_status("🤖 بدء إنشاء الوصف العربي التلقائي...")
                if 'generate_arabic_desc_button' in globals() and generate_arabic_desc_button.winfo_exists():
                    generate_arabic_desc_button.config(state=tk.DISABLED) # Disable button before thread
                # تأخير بسيط لتجنب تداخل طلبات Gemini
                def delayed_arabic_generation():
                    import time
                    time.sleep(3)  # انتظار 3 ثوانٍ لضمان انتهاء الوصف الإنجليزي
                    update_status("📝 إرسال طلب إنشاء الوصف العربي إلى Gemini...")
                    run_in_thread(generate_arabic_description_task, mod_name_for_desc, mod_category_for_desc, scraped_text_for_desc_ai, manual_features_for_desc_ai_str)

                # تشغيل إنشاء الوصف العربي في خيط منفصل مع تأخير
                threading.Thread(target=delayed_arabic_generation, daemon=True).start()
            else:
                update_status("Skipping automatic description generation: Mod name or sufficient context (description/features) not extracted by Gemini.")
        else:
            update_status("!!! Failed to extract data using Gemini. Cannot proceed with GUI update or AI description.")

    except Exception as e:
        update_status(f"!!! Critical error during Gemini scraping task wrapper: {e}")
        update_status(f"Traceback: {traceback.format_exc()}")
        if 'window' in globals() and window.winfo_exists():
            messagebox.showerror("Error", f"An unexpected error occurred during Gemini data extraction: {e}")
    finally:
        # Re-enable scrape button safely on the main thread
        if 'window' in globals() and window.winfo_exists():
             window.after(0, lambda: scrape_button.config(state=tk.NORMAL) if 'scrape_button' in globals() and scrape_button.winfo_exists() else None)
            # --- End Automatic Description Trigger ---


def update_gui_with_gemini_data(data, widgets, article_url):
    """Updates the GUI fields with data extracted by Gemini, populating managed images (Section 2)."""
    global managed_images

    update_status("--- Starting GUI field update with Gemini data (Section 2 images) ---")
    if not data or not isinstance(data, dict):
        update_status("!!! No valid Gemini data provided to update GUI.")
        return
    if not widgets:
        update_status("!!! Error: Widgets dictionary not passed for update.")
        return
    # article_url is now critical for resolving relative image paths from Gemini
    if not article_url:
        update_status("!!! Error: article_url not provided to update_gui_with_gemini_data. Cannot reliably resolve image URLs.")
        # Proceeding, but relative image URLs from Gemini might not work as expected.

    update_status(f"Gemini data (for GUI update): {json.dumps({k: (v[:50] + '...' if isinstance(v, str) and v and len(v) > 50 else v) for k, v in data.items()}, indent=2)}")
    # --- Helper function for version formatting ---
    def format_extracted_version(version_str):
        if not version_str or not isinstance(version_str, str): return None
        cleaned_version = version_str.lower().lstrip('v').strip()
        match = re.match(r'^(\d{1,2}(\.\d{1,3}){1,2})$', cleaned_version)
        if match:
            formatted = match.group(1)
            if '.' in formatted or cleaned_version == formatted : # Allow single numbers if that's what was extracted
                update_status(f"Formatted version '{version_str}' to '{formatted}'")
                return formatted
        if re.match(r'^\d+(\.\d+)*$', cleaned_version): # Fallback for 1, 1.2, 1.2.3 etc.
            update_status(f"Version '{version_str}' (cleaned: '{cleaned_version}') used after basic cleaning.")
            return cleaned_version
        update_status(f"Could not reliably format version '{version_str}'. Using original: '{version_str}'")
        return version_str # Return original cleaned if specific patterns fail

    name_entry = widgets.get('name')
    version_entry = widgets.get('version')
    primary_img_entry_publish_section = widgets.get('primary_img') # Section 5 field (for clearing)
    other_imgs_text_publish_section = widgets.get('other_imgs')   # Section 5 field (for clearing)
    mod_page_entry = widgets.get('mod_page_url')    # Section 3
    bp_entry = widgets.get('bp_url')                # Section 3b
    rp_entry = widgets.get('rp_url')                # Section 3b
    category_combo = widgets.get('category_combobox') # Section 5
    desc_widget = widgets.get('desc')               # Section 5 (publish_desc_text for clearing)
    features_widget = widgets.get('features')       # Section 4 (mod_features_text for clearing)

    # --- 1. Clear and Populate Managed Images (Section 2) ---
    update_status("Clearing existing managed images (Section 2).")
    managed_images = []

    if 'image_preview_inner_frame' in globals() and image_preview_inner_frame and image_preview_inner_frame.winfo_exists():
        for child_widget in image_preview_inner_frame.winfo_children():
            child_widget.destroy()

    gemini_primary_image = data.get("primary_image_url")
    gemini_other_images = data.get("other_image_urls", [])
    images_to_add_to_manager = []

    if gemini_primary_image and isinstance(gemini_primary_image, str):
        images_to_add_to_manager.append(gemini_primary_image)
    if isinstance(gemini_other_images, list):
        for img_url in gemini_other_images:
            if img_url and isinstance(img_url, str):
                images_to_add_to_manager.append(img_url)

    update_status(f"Found {len(images_to_add_to_manager)} image URL(s) from Gemini to add to image manager (Section 2).")

    parsed_article_url_base = None
    if article_url:
        try:
            parsed_temp = urlparse(article_url)
            if parsed_temp.scheme and parsed_temp.netloc:
                parsed_article_url_base = f"{parsed_temp.scheme}://{parsed_temp.netloc}"
        except Exception as e_parse_base:
            update_status(f"!!! Warning: Could not parse article_url '{article_url}' to form a base for relative images: {e_parse_base}")

    for img_url_from_gemini in images_to_add_to_manager:
        absolute_img_url = img_url_from_gemini
        if img_url_from_gemini.startswith('/'):
            if parsed_article_url_base:
                absolute_img_url = urljoin(parsed_article_url_base, img_url_from_gemini)
                update_status(f"Resolved relative Gemini image '{img_url_from_gemini}' to '{absolute_img_url}'")
            else:
                update_status(f"!!! Skipping relative image '{img_url_from_gemini}': Invalid or missing article_url base.")
                continue

        if absolute_img_url:
            add_image_to_managed_list(absolute_img_url, 'url')

    if not managed_images and 'image_preview_inner_frame' in globals() and image_preview_inner_frame: # Ensure preview is updated if no images
        display_managed_images()
    update_status("Finished populating managed images (Section 2) from Gemini.")

    # --- 2. Clear Publish Section Image Fields (Section 5) ---
    if primary_img_entry_publish_section and primary_img_entry_publish_section.winfo_exists():
        clear_widget_content(primary_img_entry_publish_section)
        update_status("Cleared Primary Image field (Section 5).")
    if other_imgs_text_publish_section and other_imgs_text_publish_section.winfo_exists():
        clear_widget_content(other_imgs_text_publish_section)
        update_status("Cleared Other Images field (Section 5).")

    # --- 3. Populate Other Text Fields (Name, Version, Links, Category) ---
    mod_name = data.get("mod_name")
    if mod_name and name_entry and name_entry.winfo_exists():
        auto_populate_field(name_entry, mod_name); update_status("Updated Name field.")

    raw_version = data.get("version")
    formatted_version = format_extracted_version(raw_version)
    if formatted_version and version_entry and version_entry.winfo_exists():
        auto_populate_field(version_entry, formatted_version); update_status(f"Updated Version field: {formatted_version}")
    elif raw_version and version_entry and version_entry.winfo_exists(): # Fallback
        auto_populate_field(version_entry, raw_version); update_status(f"Updated Version field (raw): {raw_version}")

    # Populate Download Links (Section 3 and 3b)
    download_link = data.get("download_link")
    bp_link = data.get("bp_download_link")
    rp_link = data.get("rp_download_link")

    if bp_link and bp_entry and bp_entry.winfo_exists():
        auto_populate_field(bp_entry, bp_link); update_status("Updated BP Download Link field (Section 3b).")
    if rp_link and rp_entry and rp_entry.winfo_exists():
        auto_populate_field(rp_entry, rp_link); update_status("Updated RP Download Link field (Section 3b).")

    if download_link and mod_page_entry and mod_page_entry.winfo_exists(): # This is for Section 3 "Process Mod"
        auto_populate_field(mod_page_entry, download_link)
        update_status("Updated Mod Page URL field (Section 3) from Gemini 'download_link'.")
        update_status("-> Mod Page URL (Section 3) populated. Press 'Process Mod' or 'Combine BP/RP'.")
    elif not download_link and (bp_link or rp_link):
        update_status("Gemini provided BP/RP links but no main 'download_link'. Mod Page URL (Section 3) not populated. Use 'Combine BP/RP'.")
    elif not download_link and not bp_link and not rp_link:
        update_status("!!! No download links (Main, BP, or RP) found in Gemini data for Section 3/3b.")


    # --- 4. Clear Description and Features fields ---
    if desc_widget and desc_widget.winfo_exists():
        clear_widget_content(desc_widget); update_status("Cleared Description field (Section 5 - AI Generate or manual).")
    if features_widget and features_widget.winfo_exists():
        clear_widget_content(features_widget); update_status("Cleared Features field (Section 4 - AI or manual).")

    # --- 5. Update Category (Section 5) ---
    category_value = data.get("category") # This should be populated by detect_category_from_url
    if category_value and category_combo and category_combo.winfo_exists():
        if category_value in CATEGORIES:
            category_combo.set(category_value)
            update_status(f"Updated Category field (Section 5) to: {category_value}")
        else:
            update_status(f"Warning: Auto-detected category '{category_value}' not in predefined list. Not setting combobox.")
            # category_combo.set(CATEGORIES[0]) # Optionally set to default

    # --- 6. Update Creator Information ---
    creator_name = data.get("creator_name")
    creator_contact = data.get("creator_contact_info")
    custom_social_sites = data.get("custom_social_sites", [])

    if creator_name and 'publish_creator_name_entry' in globals() and publish_creator_name_entry.winfo_exists():
        auto_populate_field(publish_creator_name_entry, creator_name)
        update_status(f"Updated Creator Name field: {creator_name}")

    if creator_contact and 'publish_creator_contact_entry' in globals() and publish_creator_contact_entry.winfo_exists():
        auto_populate_field(publish_creator_contact_entry, creator_contact)
        update_status(f"Updated Creator Contact field: {creator_contact}")

    # --- 7. Update Custom Social Sites Information ---
    if custom_social_sites and isinstance(custom_social_sites, list) and 'custom_site_entries' in globals():
        update_status(f"Found {len(custom_social_sites)} custom social sites from Gemini")

        # Ensure we have enough rows
        while len(custom_site_entries) < len(custom_social_sites):
            add_custom_site_row()

        # Clear extra rows if we have too many
        while len(custom_site_entries) > len(custom_social_sites) and len(custom_site_entries) > 1:
            remove_custom_site_row(len(custom_site_entries) - 1)

        # Populate the entries with extracted data
        for i, site in enumerate(custom_social_sites):
            if isinstance(site, dict) and i < len(custom_site_entries):
                site_name = site.get("site_name", "")
                site_url = site.get("site_url", "")

                if site_name and site_url:
                    entry_data = custom_site_entries[i]
                    # Clear and populate name entry
                    entry_data['name_entry'].delete(0, tk.END)
                    entry_data['name_entry'].insert(0, site_name)
                    # Clear and populate URL entry
                    entry_data['url_entry'].delete(0, tk.END)
                    entry_data['url_entry'].insert(0, site_url)
                    update_status(f"Updated Custom Site {i+1}: {site_name} -> {site_url}")

        update_status(f"Successfully updated {len(custom_social_sites)} custom social sites")

    update_status("--- GUI field update with Gemini data (Section 2 images) finished ---")

def load_incomplete_mods():
    """Loads the list of incomplete mods from the JSON file."""
    if os.path.exists(INCOMPLETE_MODS_FILE):
        try:
            with open(INCOMPLETE_MODS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError:
            update_status(f"!!! Error decoding JSON from {INCOMPLETE_MODS_FILE}. Returning empty list.")
            return []
        except Exception as e:
            update_status(f"!!! Error loading {INCOMPLETE_MODS_FILE}: {e}. Returning empty list.")
            return []
    return []

def save_incomplete_mod(mod_data):
    """Appends a single incomplete mod's data to the JSON file."""
    incomplete_mods = load_incomplete_mods()
    incomplete_mods.append(mod_data)
    try:
        with open(INCOMPLETE_MODS_FILE, 'w', encoding='utf-8') as f:
            json.dump(incomplete_mods, f, indent=2, ensure_ascii=False)
        update_status(f"Saved incomplete mod data to {INCOMPLETE_MODS_FILE}")
    except Exception as e:
        update_status(f"!!! Error saving incomplete mod data to {INCOMPLETE_MODS_FILE}: {e}")
        messagebox.showerror("File Save Error", f"Failed to save incomplete mod data: {e}")

def handle_load_incomplete():
    """Loads incomplete mod data and populates the GUI for editing."""
    incomplete_mods = load_incomplete_mods()
    if not incomplete_mods:
        messagebox.showinfo("No Incomplete Mods", f"No incomplete mods found in {INCOMPLETE_MODS_FILE}.")
        return

    # Create a simple selection list for the dialog
    choices = []
    for idx, mod in enumerate(incomplete_mods):
        name = mod.get("extracted_data", {}).get("mod_name", "Unknown Name")
        url = mod.get("article_url", "Unknown URL")
        reason = mod.get("failure_reason", "Unknown Reason")
        choices.append(f"{idx}: {name[:30]}... ({reason[:40]}...)")

    # Use simpledialog to ask the user to choose by index (more robust than asking for name)
    choice_str = "\n".join(choices)
    prompt_text = f"Select mod index to load:\n\n{choice_str}\n\nEnter index number:"

    chosen_index_str = simpledialog.askstring("Load Incomplete Mod", prompt_text, parent=window)

    if chosen_index_str is None: # User cancelled
        return

    try:
        chosen_index = int(chosen_index_str)
        if 0 <= chosen_index < len(incomplete_mods):
            selected_mod_data = incomplete_mods[chosen_index]
            update_status(f"Loading incomplete mod data for index {chosen_index}...")

            # Populate GUI fields (similar to update_gui_with_gemini_data but more manual)

            # Clear existing fields first
            clear_all_app_fields()

            # Get data points
            article_url = selected_mod_data.get("article_url")
            extracted = selected_mod_data.get("extracted_data", {})
            category = selected_mod_data.get("category")
            images = selected_mod_data.get("processed_image_urls", [])
            mod_url = selected_mod_data.get("final_mod_url")
            mod_size = selected_mod_data.get("final_mod_size")

            # Populate Section 1
            if article_url and 'scrape_url_entry' in globals():
                auto_populate_field(scrape_url_entry, article_url)

            # Populate Section 5 (Publish Fields) - Use extracted data
            if 'publish_name_entry' in globals(): auto_populate_field(publish_name_entry, extracted.get("mod_name"))
            # Description is usually not saved on failure, leave blank or add placeholder
            if 'publish_desc_text' in globals(): auto_populate_text_widget(publish_desc_text, f"--- LOADED INCOMPLETE ---\nReason: {selected_mod_data.get('failure_reason', 'Unknown')}\nOriginal URL: {article_url}\n--- Enter Description ---")
            if category and 'category_combobox' in globals():
                 if category in CATEGORIES: category_combobox.set(category)
                 else: category_combobox.set(CATEGORIES[0]) # Default if invalid
            if 'publish_version_entry' in globals(): auto_populate_field(publish_version_entry, extracted.get("version"))
            if mod_size and 'publish_size_entry' in globals():
                 publish_size_entry.config(state=tk.NORMAL)
                 auto_populate_field(publish_size_entry, mod_size)
                 publish_size_entry.config(state=tk.DISABLED)

            # Populate creator information
            if 'publish_creator_name_entry' in globals(): auto_populate_field(publish_creator_name_entry, extracted.get("creator_name"))
            if 'publish_creator_contact_entry' in globals(): auto_populate_field(publish_creator_contact_entry, extracted.get("creator_contact_info"))
            # Update custom social sites from extracted data
            creator_social_channels = extracted.get("creator_social_channels", [])
            if creator_social_channels and isinstance(creator_social_channels, list) and 'custom_site_entries' in globals():
                # Clear existing entries first (except the first one)
                while len(custom_site_entries) > 1:
                    remove_custom_site_row(len(custom_site_entries) - 1)

                # Parse and populate custom sites from creator_social_channels
                for i, channel in enumerate(creator_social_channels):
                    if isinstance(channel, str) and ':' in channel:
                        parts = channel.split(':', 1)
                        if len(parts) == 2:
                            site_name = parts[0].strip()
                            site_url = parts[1].strip()

                            # Add new row if needed
                            if i >= len(custom_site_entries):
                                add_custom_site_row()

                            # Populate the entry
                            if i < len(custom_site_entries):
                                entry_data = custom_site_entries[i]
                                entry_data['name_entry'].delete(0, tk.END)
                                entry_data['name_entry'].insert(0, site_name)
                                entry_data['url_entry'].delete(0, tk.END)
                                entry_data['url_entry'].insert(0, site_url)

            # Populate Image fields (Primary and Other) - MODIFIED for handle_load_incomplete
            global managed_images # Ensure it's global if not already in this scope
            managed_images = [] # Clear existing managed images from previous operations (already done by clear_all_app_fields)

            images_to_load_into_manager = []
            # Check if 'processed_image_urls' exists (from a previous partial batch attempt that saved to incomplete)
            processed_urls_from_incomplete = selected_mod_data.get("processed_image_urls", [])

            if processed_urls_from_incomplete and isinstance(processed_urls_from_incomplete, list):
                images_to_load_into_manager.extend(url for url in processed_urls_from_incomplete if url)
                update_status(f"Found {len(images_to_load_into_manager)} 'processed_image_urls' in incomplete data.")
            else:
                # Fallback to 'extracted_data' if 'processed_image_urls' is not present or empty
                extracted_imgs_data = selected_mod_data.get("extracted_data", {})
                primary_img_extracted = extracted_imgs_data.get("primary_image_url")
                other_imgs_extracted = extracted_imgs_data.get("other_image_urls", [])

                if primary_img_extracted:
                    images_to_load_into_manager.append(primary_img_extracted)
                if isinstance(other_imgs_extracted, list):
                    images_to_load_into_manager.extend(url for url in other_imgs_extracted if url)
                update_status(f"Using 'extracted_data' for images. Found {len(images_to_load_into_manager)} image URLs.")

            if images_to_load_into_manager:
                update_status(f"Loading {len(images_to_load_into_manager)} image(s) into image management area...")
                for img_url in images_to_load_into_manager:
                    add_image_to_managed_list(img_url, 'url') # This will trigger display_managed_images
            else:
                update_status("No image URLs found in incomplete mod data to load into management area.")
                display_managed_images() # Ensure preview is cleared if no images

            # The publish_primary_image_entry and publish_other_images_text fields are already cleared
            # by clear_all_app_fields() and will be populated by the image management section (Section 2)
            # when the user processes images.

            # Populate Mod URL field (Section 3 and/or 5)
            if mod_url and 'publish_mod_url_entry' in globals(): auto_populate_field(publish_mod_url_entry, mod_url)
            # Also populate Section 3 if it was the main link originally
            if mod_url and extracted.get("download_link") == mod_url and 'mod_page_url_entry' in globals():
                 auto_populate_field(mod_page_url_entry, mod_url)
            # Populate BP/RP if they were extracted
            if extracted.get("bp_download_link") and 'bp_url_entry' in globals(): auto_populate_field(bp_url_entry, extracted.get("bp_download_link"))
            if extracted.get("rp_download_link") and 'rp_url_entry' in globals(): auto_populate_field(rp_url_entry, extracted.get("rp_download_link"))


            update_status("GUI fields populated with incomplete mod data. Please review, correct, and use 'Publish Mod Now'.")
            messagebox.showinfo("Loaded", "Incomplete mod data loaded into fields.\nPlease review and correct any missing/incorrect information (especially download links or images) before attempting to publish again using the main 'Publish Mod Now' button.")

        else:
            messagebox.showerror("Invalid Index", f"Index {chosen_index} is out of range.")
    except ValueError:
        messagebox.showerror("Invalid Input", "Please enter a valid index number.")
    except Exception as e:
         update_status(f"!!! Error loading incomplete mod data: {e}")
         update_status(f"Traceback: {traceback.format_exc()}")
         messagebox.showerror("Load Error", f"Failed to load incomplete mod data: {e}")

# --- Pending Review Mod Handling ---
def handle_load_pending_review_mod():
    """Loads a mod from pending_review.json and populates the GUI for review and publishing."""
    global current_review_mod_identifier
    if not os.path.exists(PENDING_REVIEW_FILE):
        messagebox.showinfo("No Pending Mods", f"'{PENDING_REVIEW_FILE}' not found. No mods are pending review.")
        return

    try:
        with open(PENDING_REVIEW_FILE, 'r', encoding='utf-8') as f:
            pending_mods = json.load(f)
    except (json.JSONDecodeError, FileNotFoundError) as e:
        messagebox.showerror("Error Loading Review File", f"Could not load or parse '{PENDING_REVIEW_FILE}': {e}")
        return

    if not pending_mods:
        messagebox.showinfo("No Pending Mods", f"No mods found in '{PENDING_REVIEW_FILE}' for review.")
        return

    choices = []
    for idx, mod_data in enumerate(pending_mods):
        name = mod_data.get("extracted_data", {}).get("mod_name", "Unknown Name")
        article_url = mod_data.get("article_url", "Unknown URL")
        choices.append(f"{idx}: {name[:40]}... (From: {article_url[:30]}...)")

    choice_str = "\n".join(choices)
    prompt_text = f"Select mod index to load for review:\n\n{choice_str}\n\nEnter index number:"

    chosen_index_str = simpledialog.askstring("Load Mod for Review", prompt_text, parent=window)

    if chosen_index_str is None: # User cancelled
        return

    try:
        chosen_index = int(chosen_index_str)
        if 0 <= chosen_index < len(pending_mods):
            selected_mod_data = pending_mods[chosen_index]
            # Use article_url as a unique identifier for now.
            current_review_mod_identifier = selected_mod_data.get("article_url")
            update_status(f"Loading mod for review (Index {chosen_index}): {selected_mod_data.get('extracted_data', {}).get('mod_name', 'N/A')}")
            populate_gui_for_pending_review(selected_mod_data)
        else:
            messagebox.showerror("Invalid Index", f"Index {chosen_index} is out of range.")
    except ValueError:
        messagebox.showerror("Invalid Input", "Please enter a valid index number.")
    except Exception as e:
        update_status(f"!!! Error loading mod for review: {e}")
        update_status(f"Traceback: {traceback.format_exc()}")
        messagebox.showerror("Load Error", f"Failed to load mod for review: {e}")

def populate_gui_for_pending_review(mod_data):
    """Populates GUI fields with data from a mod selected from pending_review.json."""
    update_status(f"Populating GUI with review data for: {mod_data.get('article_url')}")

    # مسح جميع الحقول أولاً، هذا سيقوم أيضًا بمسح managed_images
    clear_all_app_fields()

    article_url = mod_data.get("article_url")
    extracted_data = mod_data.get("extracted_data", {}) # هذا يحتوي على روابط الصور الأصلية من Gemini
    mod_category = mod_data.get("mod_category")
    # final_image_urls هي الروابط التي قد تكون تمت معالجتها بالفعل إلى Supabase (إذا تم تشغيل المعالجة الدفعية سابقًا بهذا المنطق)
    # أو قد تكون هي نفسها الروابط الأصلية إذا لم تتم معالجة الصور بعد.
    image_urls_from_pending_data = mod_data.get("final_image_urls", [])
    final_mod_url = mod_data.get("final_mod_url")
    final_mod_size = mod_data.get("final_mod_size")

    # --- Populate Section 1: Article URL ---
    if article_url and 'scrape_url_entry' in globals():
        auto_populate_field(scrape_url_entry, article_url)

    # --- Populate Publish Fields (Section 5) ---
    mod_name = extracted_data.get("mod_name")
    if mod_name and 'publish_name_entry' in globals():
        auto_populate_field(publish_name_entry, mod_name)

    version = extracted_data.get("version") # افترض أن Gemini استخرج الإصدار
    if not version: # إذا لم يستخرجه Gemini، حاول الحصول عليه من اسم المود (منطق قديم)
        if mod_name:
            version_match_title = re.search(r'\(?([\d\.]+)\)?$', mod_name)
            if not version_match_title: version_match_title = re.search(r'\((\d+(\.\d+)+)\)', mod_name)
            if version_match_title: version = version_match_title.group(1).strip()
    if version and 'publish_version_entry' in globals():
        auto_populate_field(publish_version_entry, version)


    if mod_category and 'category_combobox' in globals():
        if mod_category in CATEGORIES: category_combobox.set(mod_category)
        else: category_combobox.set(CATEGORIES[0])

    description_to_display = extracted_data.get("full_mod_description")
    if not description_to_display:
        description_to_display = (
            f"--- MOD LOADED FOR REVIEW ---\n"
            f"Original Article URL: {article_url}\n\n"
            f"!!! NO DESCRIPTION EXTRACTED BY GEMINI OR FOUND IN SAVED DATA !!!\n"
            f"Please provide a suitable description for this mod."
        )
    if 'publish_desc_text' in globals() and publish_desc_text.winfo_exists():
        auto_populate_text_widget(publish_desc_text, description_to_display)

    # --- Populate Section 2: Managed Images ---
    # global managed_images # تم مسحها بواسطة clear_all_app_fields()

    # الأولوية لـ final_image_urls إذا كانت موجودة (قد تكون روابط Supabase المعالجة)
    # وإلا، استخدم الروابط الأصلية من extracted_data
    images_to_load_in_manager = []
    if image_urls_from_pending_data and isinstance(image_urls_from_pending_data, list) and any(image_urls_from_pending_data):
        update_status(f"Loading images from 'final_image_urls' (count: {len(image_urls_from_pending_data)}) into image manager.")
        images_to_load_in_manager = [url for url in image_urls_from_pending_data if url]
    else:
        update_status("No 'final_image_urls' found or they are empty. Falling back to 'extracted_data' for images.")
        primary_img_extracted = extracted_data.get("primary_image_url")
        other_imgs_extracted = extracted_data.get("other_image_urls", [])
        if primary_img_extracted and isinstance(primary_img_extracted, str):
            images_to_load_in_manager.append(primary_img_extracted)
        if isinstance(other_imgs_extracted, list):
            images_to_load_in_manager.extend(url for url in other_imgs_extracted if url and isinstance(url, str))
        update_status(f"Loading images from 'extracted_data' (count: {len(images_to_load_in_manager)}) into image manager.")

    if images_to_load_in_manager:
        # قبل إضافة الصور، تأكد من أن الروابط نسبية يتم تحويلها إلى مطلقة باستخدام article_url
        parsed_article_url_base = None
        if article_url:
            try:
                parsed_temp = urlparse(article_url)
                if parsed_temp.scheme and parsed_temp.netloc:
                    parsed_article_url_base = f"{parsed_temp.scheme}://{parsed_temp.netloc}"
            except Exception as e_parse_base:
                update_status(f"!!! Warning: Could not parse article_url '{article_url}' to form a base for relative images: {e_parse_base}")

        for img_url_from_pending in images_to_load_in_manager:
            absolute_img_url = img_url_from_pending
            if img_url_from_pending.startswith('/'): # إذا كان الرابط نسبيًا
                if parsed_article_url_base:
                    absolute_img_url = urljoin(parsed_article_url_base, img_url_from_pending)
                    update_status(f"Resolved relative image '{img_url_from_pending}' to '{absolute_img_url}' for image manager.")
                else:
                    update_status(f"!!! Skipping relative image '{img_url_from_pending}' for manager: Invalid or missing article_url base.")
                    continue # تخطي هذا الرابط النسبي

            if absolute_img_url: # تأكد من أن الرابط ليس فارغًا بعد المعالجة
                add_image_to_managed_list(absolute_img_url, 'url') # سيؤدي هذا إلى استدعاء display_managed_images
    else:
        update_status("No image URLs found in pending review data to load into image management area.")
        # استدعاء display_managed_images لضمان مسح المعاينة إذا لم تكن هناك صور
        if 'image_preview_inner_frame' in globals() and image_preview_inner_frame.winfo_exists():
             display_managed_images()


    # --- Populate Publish Section Fields (Continued) ---
    # حقول الصور في قسم النشر (publish_primary_image_entry, publish_other_images_text)
    # سيتم ملؤها *بعد* الضغط على زر "معالجة ورفع الصور المعروضة" في القسم 2.
    # لذلك، لا حاجة لملئها هنا مباشرة من mod_data. clear_all_app_fields قام بمسحها.

    if final_mod_url and 'publish_mod_url_entry' in globals():
        auto_populate_field(publish_mod_url_entry, final_mod_url)
    elif not final_mod_url: # إذا لم يتم تحديد final_mod_url (مثلاً في حالة BP/RP)
        # املأ حقول BP/RP إذا كانت موجودة في extracted_data
        bp_link_extracted = extracted_data.get("bp_download_link")
        rp_link_extracted = extracted_data.get("rp_download_link")
        if bp_link_extracted and 'bp_url_entry' in globals():
            auto_populate_field(bp_url_entry, bp_link_extracted)
            update_status("Populated BP URL field (Section 3b) from extracted_data for review.")
        if rp_link_extracted and 'rp_url_entry' in globals():
            auto_populate_field(rp_url_entry, rp_link_extracted)
            update_status("Populated RP URL field (Section 3b) from extracted_data for review.")


    if final_mod_size and 'publish_size_entry' in globals():
        publish_size_entry.config(state=tk.NORMAL)
        auto_populate_field(publish_size_entry, final_mod_size)
        publish_size_entry.config(state=tk.DISABLED)

    # Populate creator information
    if 'publish_creator_name_entry' in globals(): auto_populate_field(publish_creator_name_entry, extracted_data.get("creator_name"))
    if 'publish_creator_contact_entry' in globals(): auto_populate_field(publish_creator_contact_entry, extracted_data.get("creator_contact_info"))
    # Update custom social sites from extracted data
    creator_social_channels = extracted_data.get("creator_social_channels", [])
    if creator_social_channels and isinstance(creator_social_channels, list) and 'custom_site_entries' in globals():
        # Clear existing entries first (except the first one)
        while len(custom_site_entries) > 1:
            remove_custom_site_row(len(custom_site_entries) - 1)

        # Parse and populate custom sites from creator_social_channels
        for i, channel in enumerate(creator_social_channels):
            if isinstance(channel, str) and ':' in channel:
                parts = channel.split(':', 1)
                if len(parts) == 2:
                    site_name = parts[0].strip()
                    site_url = parts[1].strip()

                    # Add new row if needed
                    if i >= len(custom_site_entries):
                        add_custom_site_row()

                    # Populate the entry
                    if i < len(custom_site_entries):
                        entry_data = custom_site_entries[i]
                        entry_data['name_entry'].delete(0, tk.END)
                        entry_data['name_entry'].insert(0, site_name)
                        entry_data['url_entry'].delete(0, tk.END)
                        entry_data['url_entry'].insert(0, site_url)

    # مسح حقول معالجة المود (القسم 3) إذا لم تكن ذات صلة مباشرة الآن
    if 'mod_page_url_entry' in globals(): clear_widget_content(mod_page_url_entry)
    # لا تمسح bp_url_entry و rp_url_entry إذا تم ملؤهما أعلاه
    # if 'bp_url_entry' in globals(): clear_widget_content(bp_url_entry)
    # if 'rp_url_entry' in globals(): clear_widget_content(rp_url_entry)

    update_status("GUI populated for review. Verify images in Section 2 and click 'Process & Upload Displayed Images', then 'Publish Mod Now'.")
    messagebox.showinfo("Mod Loaded for Review", "Mod data loaded. Please:\n1. Review/process images in Section 2.\n2. Verify all other fields.\n3. Click 'Publish Mod Now'.")

import webbrowser # تأكد من استيراد هذه المكتبة في بداية الملف

def open_article_url_in_browser():
    """Opens the URL from the scrape_url_entry in the default web browser."""
    if 'scrape_url_entry' in globals() and scrape_url_entry.winfo_exists():
        url_to_open = scrape_url_entry.get().strip()
        if url_to_open and (url_to_open.startswith("http://") or url_to_open.startswith("https://")):
            try:
                webbrowser.open_new_tab(url_to_open)
                update_status(f"Opening URL in browser: {url_to_open}")
            except Exception as e:
                update_status(f"!!! Failed to open URL '{url_to_open}': {e}")
                messagebox.showerror("Browser Error", f"Could not open URL: {e}")
        elif url_to_open:
            update_status(f"!!! Invalid URL format to open: {url_to_open}")
            messagebox.showwarning("Invalid URL", "The URL in the field does not seem valid.")
        else:
            update_status("Article URL field is empty. Nothing to open.")
    else:
        update_status("Scrape URL entry widget not found.")

def handle_delete_pending_review_mod():
    """Handles deleting a mod from the pending_review.json file."""
    if not os.path.exists(PENDING_REVIEW_FILE):
        messagebox.showinfo("No Pending Mods", f"'{PENDING_REVIEW_FILE}' not found. No mods to delete.")
        return

    try:
        with open(PENDING_REVIEW_FILE, 'r', encoding='utf-8') as f:
            pending_mods = json.load(f)
    except (json.JSONDecodeError, FileNotFoundError) as e:
        messagebox.showerror("Error Loading Review File", f"Could not load or parse '{PENDING_REVIEW_FILE}': {e}")
        return

    if not pending_mods:
        messagebox.showinfo("No Pending Mods", f"No mods found in '{PENDING_REVIEW_FILE}' to delete.")
        return

    choices = []
    for idx, mod_data in enumerate(pending_mods):
        name = mod_data.get("extracted_data", {}).get("mod_name", "Unknown Name")
        article_url = mod_data.get("article_url", "Unknown URL")
        choices.append(f"{idx}: {name[:40]}... (From: {article_url[:30]}...)")

    choice_str = "\n".join(choices)
    prompt_text = f"Select mod index to DELETE:\n\n{choice_str}\n\nEnter index number:"

    chosen_index_str = simpledialog.askstring("Delete Mod from Review List", prompt_text, parent=window)

    if chosen_index_str is None: # User cancelled
        return

    try:
        chosen_index = int(chosen_index_str)
        if 0 <= chosen_index < len(pending_mods):
            mod_to_delete = pending_mods[chosen_index]
            mod_name_to_delete = mod_to_delete.get("extracted_data", {}).get("mod_name", "this mod")

            if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete '{mod_name_to_delete}' (Index: {chosen_index}) from the pending review list? This action cannot be undone.", parent=window):
                pending_mods.pop(chosen_index)
                try:
                    with open(PENDING_REVIEW_FILE, 'w', encoding='utf-8') as f:
                        json.dump(pending_mods, f, indent=2, ensure_ascii=False)
                    update_status(f"Successfully deleted mod '{mod_name_to_delete}' (Index: {chosen_index}) from '{PENDING_REVIEW_FILE}'.")
                    messagebox.showinfo("Deleted", f"Mod '{mod_name_to_delete}' has been deleted from the review list.")
                except Exception as save_err:
                    update_status(f"!!! Error saving '{PENDING_REVIEW_FILE}' after deletion: {save_err}")
                    messagebox.showerror("Save Error", f"Failed to save changes to '{PENDING_REVIEW_FILE}': {save_err}")
            else:
                update_status("Deletion cancelled by user.")
        else:
            messagebox.showerror("Invalid Index", f"Index {chosen_index} is out of range.")
    except ValueError:
        messagebox.showerror("Invalid Input", "Please enter a valid index number.")
    except Exception as e:
        update_status(f"!!! Error deleting mod from review: {e}")
        update_status(f"Traceback: {traceback.format_exc()}")
        messagebox.showerror("Delete Error", f"Failed to delete mod from review: {e}")

def handle_delete_all_pending_review_mods():
    """Handles deleting ALL mods from the pending_review.json file."""
    if not os.path.exists(PENDING_REVIEW_FILE):
        messagebox.showinfo("File Not Found", f"'{PENDING_REVIEW_FILE}' not found. No mods to delete.")
        update_status(f"'{PENDING_REVIEW_FILE}' not found. No action taken.")
        return

    try:
        with open(PENDING_REVIEW_FILE, 'r', encoding='utf-8') as f:
            pending_mods = json.load(f)
    except (json.JSONDecodeError, FileNotFoundError) as e:
        messagebox.showerror("Error Loading Review File", f"Could not load or parse '{PENDING_REVIEW_FILE}': {e}")
        update_status(f"Error loading '{PENDING_REVIEW_FILE}': {e}")
        return

    if not pending_mods:
        messagebox.showinfo("No Pending Mods", f"'{PENDING_REVIEW_FILE}' is already empty.")
        update_status(f"'{PENDING_REVIEW_FILE}' is already empty. No action taken.")
        return

    if messagebox.askyesno("Confirm Delete All", f"Are you sure you want to delete ALL ({len(pending_mods)}) mods from the pending review list?\n\nThis action CANNOT BE UNDONE.", parent=window, icon='warning'):
        try:
            with open(PENDING_REVIEW_FILE, 'w', encoding='utf-8') as f:
                json.dump([], f, indent=2, ensure_ascii=False) # Write an empty list
            update_status(f"Successfully deleted all mods from '{PENDING_REVIEW_FILE}'.")
            messagebox.showinfo("All Deleted", f"All mods have been deleted from '{PENDING_REVIEW_FILE}'.")
        except Exception as save_err:
            update_status(f"!!! Error saving '{PENDING_REVIEW_FILE}' after deleting all: {save_err}")
            messagebox.showerror("Save Error", f"Failed to save changes to '{PENDING_REVIEW_FILE}': {save_err}")
    else:
        update_status("Delete all mods operation cancelled by user.")

# --- Batch Processing Functions ---
selected_batch_file = None # Global variable to store the path
DEFAULT_BATCH_FILENAME = "link1.txt" # Changed default filename
PENDING_REVIEW_FILE = "pending_review.json" # New file for mods pending review
stop_batch_event = threading.Event() # For stopping batch processing

def check_default_batch_file():
    """Checks for the default batch file and updates the GUI if found."""
    global selected_batch_file
    default_path = os.path.join(os.path.dirname(__file__) or '.', DEFAULT_BATCH_FILENAME)
    if os.path.exists(default_path):
        selected_batch_file = default_path
        if 'batch_file_entry' in globals() and batch_file_entry.winfo_exists():
            batch_file_entry.config(state=tk.NORMAL)
            batch_file_entry.delete(0, tk.END)
            batch_file_entry.insert(0, default_path)
            batch_file_entry.config(state=tk.DISABLED)
            update_status(f"Default batch file found: {default_path}")
            if 'start_batch_button' in globals() and start_batch_button.winfo_exists():
                start_batch_button.config(state=tk.NORMAL) # Enable start button
            return True
    return False


def select_batch_file():
    """Opens a file dialog to select a text file containing URLs."""
    global selected_batch_file
    filepath = tk.filedialog.askopenfilename(
        title="Select URL File",
        filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
    )
    if filepath:
        selected_batch_file = filepath
        if 'batch_file_entry' in globals() and batch_file_entry.winfo_exists():
            batch_file_entry.config(state=tk.NORMAL)
            batch_file_entry.delete(0, tk.END)
            batch_file_entry.insert(0, filepath)
            batch_file_entry.config(state=tk.DISABLED)
            update_status(f"Selected batch file: {filepath}")
            if 'start_batch_button' in globals() and start_batch_button.winfo_exists():
                start_batch_button.config(state=tk.NORMAL) # Enable start button
        else:
            update_status("Error: Batch file entry widget not found.")
    else:
        update_status("Batch file selection cancelled.")
        if 'start_batch_button' in globals() and start_batch_button.winfo_exists():
            start_batch_button.config(state=tk.DISABLED) # Keep disabled if no file selected

def handle_batch_publish():
    """Starts the batch publishing process."""
    global selected_batch_file
    if not selected_batch_file:
        messagebox.showerror("Error", "Please select a file containing URLs first.")
        return
    if not os.path.exists(selected_batch_file):
         messagebox.showerror("Error", f"Selected file not found: {selected_batch_file}")
         return

    if not messagebox.askyesno("Confirm Batch Publish", f"Start automatic publishing for all URLs in:\n{selected_batch_file}\n\nThis may take a long time and consume API quota. Ensure all settings (API keys, etc.) are correct."):
        return

    update_status(f"\n--- === STARTING BATCH PUBLISH from {os.path.basename(selected_batch_file)} === ---")
    stop_batch_event.clear() # Clear event before starting

    if 'start_batch_button' in globals() and start_batch_button.winfo_exists():
        start_batch_button.config(state=tk.DISABLED)
    if 'select_batch_file_button' in globals() and select_batch_file_button.winfo_exists():
        select_batch_file_button.config(state=tk.DISABLED)
    if 'stop_batch_process_button' in globals() and stop_batch_process_button.winfo_exists(): # Ensure this new button exists
        stop_batch_process_button.config(state=tk.NORMAL)


    # Run the actual batch process in a thread
    run_in_thread(batch_publish_task, selected_batch_file)

def request_stop_batch():
    global stop_batch_event
    if not stop_batch_event.is_set(): # Check if already set to avoid redundant updates
        update_status("!!! طلب إيقاف المعالجة الدفعية...")
        stop_batch_event.set()
        if 'stop_batch_process_button' in globals() and stop_batch_process_button.winfo_exists():
            stop_batch_process_button.config(state=tk.DISABLED)
    # The batch_publish_task's finally block will manage re-enabling start button and itself.

def batch_publish_task(filepath):
    """Processes URLs from a file, extracts data, and saves for review instead of direct publishing.
    MODIFIED: Does NOT download or re-upload mod files. Uses extracted links directly.
    """
    global stop_batch_event
    urls_processed = 0
    mods_saved_for_review = 0
    mods_failed_critically = 0 # For critical failures like Gemini extraction failure
    stop_batch = False
    start_time = time.time()
    total_urls = 0
    mods_pending_review = []
    stop_batch_event.clear()

    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            all_urls = [line.strip() for line in f if line.strip() and line.strip().startswith('http')]

        total_urls = len(all_urls)
        update_status(f"Found {total_urls} URLs in the file for batch processing and review.")

        for i, article_url in enumerate(all_urls):
            if stop_batch_event.is_set():
                update_status("!!! تم إيقاف المعالجة الدفعية بواسطة المستخدم.")
                stop_batch = True
                break

            urls_processed += 1
            update_status(f"\n--- Processing URL {i+1}/{total_urls} for review: {article_url} ---")

            critical_failure_data = {
                "article_url": article_url,
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "failure_reason": "Unknown critical failure",
                "extracted_data": None
            }

            try:
                # 1. Extract Data using Gemini
                update_status("Step 1: Extracting data via Gemini...")
                extracted_data = extract_data_with_gemini_task(article_url)
                if not extracted_data:
                    raise ValueError("Failed to extract data using Gemini.")
                critical_failure_data["extracted_data"] = extracted_data
                update_status("Data extracted successfully.")

                # 2. Detect Category
                update_status("Step 2: Detecting category...")
                mod_category = detect_category_from_url(article_url, CATEGORIES) or CATEGORIES[0]
                update_status(f"Using category: {mod_category}")

                # 3. Process Images (This part remains the same as it uploads images to Supabase)
                update_status("Step 3: Processing images...")
                image_urls_to_process = []
                primary_img = extracted_data.get("primary_image_url")
                other_imgs = extracted_data.get("other_image_urls")
                if primary_img: image_urls_to_process.append(primary_img)
                if isinstance(other_imgs, list): image_urls_to_process.extend([img for img in other_imgs if img])

                processed_image_supabase_urls = []
                if image_urls_to_process:
                    processed_image_supabase_urls = process_and_get_supabase_urls(image_urls_to_process, article_url)
                    update_status(f"Processed {len(processed_image_supabase_urls)} image URLs for Supabase.")
                else:
                    update_status("No image URLs found to process.")

                final_image_urls_for_review = processed_image_supabase_urls[:7]

                # 4. Assign Mod File Link (NO DOWNLOAD/UPLOAD)
                update_status("Step 4: Assigning mod file link (using extracted URL directly)...")
                final_mod_url = None
                mod_size_str = "N/A (not downloaded)" # Size will be N/A

                bp_link = extracted_data.get("bp_download_link")
                rp_link = extracted_data.get("rp_download_link")
                main_link = extracted_data.get("download_link")

                # --- LOGIC FOR CHOOSING THE MOD URL ---
                # If BP and RP links are present, we assume they are meant to be used together.
                # For the purpose of 'pending_review.json', we can decide how to store this.
                # Option 1: Store both if available, and let manual review decide.
                # Option 2: Prioritize main_link if available, otherwise store BP/RP.
                # Let's go with storing the most specific links found.
                # The `publish_mod_task` expects a single `download_url`.
                # For pending_review, we can be more flexible or decide a convention.

                if main_link: # Prioritize .mcaddon or single download link
                    final_mod_url = main_link
                    update_status(f"Using main download link extracted by Gemini: {final_mod_url}")
                elif bp_link and rp_link:
                    # If we only want to store one URL for final_mod_url for simplicity in pending_review,
                    # we might just pick one (e.g., BP) or concatenate them, or store them separately.
                    # For now, let's assume if only BP/RP exist, the user will handle them during review.
                    # We'll store the BP link as the primary if no main_link is found.
                    # Or better: if both BP and RP exist, it implies they are separate and need combining.
                    # The `pending_review.json` should reflect this.
                    # We will set final_mod_url to None in this case, and the reviewer will see BP/RP in extracted_data.
                    update_status(f"Found BP ({bp_link}) and RP ({rp_link}) links. No single main mod URL. Reviewer will need to combine or select.")
                    # final_mod_url will remain None, but extracted_data contains bp_link and rp_link.
                elif bp_link: # Only BP link
                    final_mod_url = bp_link
                    update_status(f"Using BP download link extracted by Gemini (no main or RP): {final_mod_url}")
                elif rp_link: # Only RP link
                    final_mod_url = rp_link
                    update_status(f"Using RP download link extracted by Gemini (no main or BP): {final_mod_url}")
                else:
                    # No download links at all. This is a potential issue.
                    update_status("!!! Warning: No download link (main, BP, or RP) found in extracted data from Gemini.")
                    # `final_mod_url` remains None. This item might be problematic for review.

                # (Size is N/A as we are not downloading)
                update_status(f"Mod size: {mod_size_str}")

                # --- END MODIFIED SECTION ---

                # 5. Compile Data for Review
                mod_review_data = {
                    "article_url": article_url,
                    "extracted_data": extracted_data, # This contains full_mod_description and all extracted links
                    "mod_category": mod_category,
                    "final_image_urls": final_image_urls_for_review,
                    "final_mod_url": final_mod_url, # This will be the single link (main, BP, or RP) or None
                    "final_mod_size": mod_size_str,
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                }
                # Log key data points for verification
                log_mod_name = extracted_data.get("mod_name", "N/A")
                log_main_link = extracted_data.get("download_link", "N/A")
                log_bp_link = extracted_data.get("bp_download_link", "N/A")
                log_rp_link = extracted_data.get("rp_download_link", "N/A")

                update_status(f"SUCCESS: Data compiled for review. Name: '{log_mod_name}'. MainLink: '{log_main_link}'. BP: '{log_bp_link}'. RP: '{log_rp_link}'. Final URL for review: '{final_mod_url}'")
                mods_pending_review.append(mod_review_data)
                mods_saved_for_review += 1
                update_status(f"--- Mod data compiled for review (URL {i+1}/{total_urls}). Current 'mods_pending_review' list size: {len(mods_pending_review)} ---")

            except ValueError as ve: # Specifically for Gemini data extraction failure
                mods_failed_critically += 1
                error_msg = f"!!! CRITICAL FAILURE processing URL {i+1}/{total_urls} (Gemini extraction): {ve}"
                critical_failure_data["failure_reason"] = f"Gemini data extraction: {ve}"
                # `critical_failure_data["extracted_data"]` would be None here
                update_status(error_msg)
                update_status(f"Traceback (if any from this error): {traceback.format_exc()}") # May not have much traceback for this one
                save_incomplete_mod(critical_failure_data)
                update_status(f"Data for failed URL {article_url} saved to {INCOMPLETE_MODS_FILE}. Continuing batch...")

            except Exception as url_proc_err: # Other unexpected errors during a URL's processing
                mods_failed_critically += 1
                error_msg = f"!!! CRITICAL FAILURE processing URL {i+1}/{total_urls} (Unexpected): {url_proc_err}"
                critical_failure_data["failure_reason"] = f"Unexpected error: {url_proc_err}"
                # `extracted_data` might or might not be populated depending on where the error occurred
                update_status(error_msg)
                update_status(f"Traceback: {traceback.format_exc()}")
                save_incomplete_mod(critical_failure_data)
                update_status(f"Data for failed URL {article_url} saved to {INCOMPLETE_MODS_FILE}. Continuing batch...")

            if not stop_batch and i < total_urls -1:
                delay_seconds = 1 # Can reduce delay if not downloading files
                update_status(f"Waiting {delay_seconds} seconds before next URL...")
                time.sleep(delay_seconds)

    except FileNotFoundError:
        update_status(f"!!! Error: Batch file not found at {filepath}")
        messagebox.showerror("File Error", f"Batch file not found:\n{filepath}")
        stop_batch = True
    except Exception as batch_err:
        update_status(f"!!! CRITICAL BATCH ERROR (outside URL loop): {batch_err}")
        update_status(f"Traceback: {traceback.format_exc()}")
        messagebox.showerror("Batch Error", f"A critical error occurred during batch processing: {batch_err}")
        stop_batch = True
    finally:
        end_time = time.time()
        duration = end_time - start_time
        update_status(f"\n--- === BATCH PROCESSING FOR REVIEW {'STOPPED' if stop_batch else 'FINISHED'} === ---")
        update_status(f"Duration: {duration:.2f} seconds")
        update_status(f"Total URLs in file: {total_urls}")
        update_status(f"Attempted to process: {urls_processed}")
        update_status(f"Mods successfully compiled for 'mods_pending_review' list: {mods_saved_for_review}")
        update_status(f"Mods with critical failures (saved to incomplete_mods.json): {mods_failed_critically}")

        if not mods_pending_review:
            update_status(f"!!! The 'mods_pending_review' list is empty. No data will be saved to '{PENDING_REVIEW_FILE}'.")
        else:
            update_status(f"Attempting to save {len(mods_pending_review)} mod(s) to '{PENDING_REVIEW_FILE}'.")
            try:
                # Load existing pending mods to append, not overwrite (IMPORTANT)
                existing_pending_mods = []
                if os.path.exists(PENDING_REVIEW_FILE):
                    try:
                        with open(PENDING_REVIEW_FILE, 'r', encoding='utf-8') as f_read:
                            existing_pending_mods = json.load(f_read)
                        if not isinstance(existing_pending_mods, list):
                            update_status(f"Warning: '{PENDING_REVIEW_FILE}' does not contain a valid list. Overwriting with new data.")
                            existing_pending_mods = []
                    except json.JSONDecodeError:
                        update_status(f"Warning: Could not decode JSON from '{PENDING_REVIEW_FILE}'. Overwriting with new data.")
                        existing_pending_mods = []

                existing_pending_mods.extend(mods_pending_review) # Append new mods

                with open(PENDING_REVIEW_FILE, 'w', encoding='utf-8') as f_write:
                    json.dump(existing_pending_mods, f_write, indent=2, ensure_ascii=False)
                update_status(f"Successfully saved/appended {len(mods_pending_review)} new mods to '{PENDING_REVIEW_FILE}'. Total pending: {len(existing_pending_mods)}.")
            except Exception as save_err:
                update_status(f"!!! Error saving mods for review to '{PENDING_REVIEW_FILE}': {save_err}")
                messagebox.showerror("Save Error", f"Failed to save pending review file: {save_err}")

        if 'window' in globals() and window.winfo_exists():
            window.after(0, lambda: {
                (start_batch_button.config(state=tk.NORMAL) if 'start_batch_button' in globals() and start_batch_button.winfo_exists() else None),
                (select_batch_file_button.config(state=tk.NORMAL) if 'select_batch_file_button' in globals() and select_batch_file_button.winfo_exists() else None),
                (stop_batch_process_button.config(state=tk.DISABLED) if 'stop_batch_process_button' in globals() and stop_batch_process_button.winfo_exists() else None)
            })
        stop_batch_event.clear()# Clear the event for the next potential run


# --- Main Execution ---
if __name__ == "__main__":
    window = tk.Tk()
    window.title("أداة معالجة ونشر المودات والصور")
    # Set a minimum size, but allow resizing
    window.minsize(700, 600)
    # Start with a reasonable size, but scrolling will handle overflow
    window.geometry("750x700") # Slightly wider to accommodate scrollbar

    # --- Create Scrollable Frame ---
    main_frame = ttk.Frame(window)
    main_frame.pack(fill=tk.BOTH, expand=1)

    canvas = tk.Canvas(main_frame)
    scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas) # This frame will hold all the content

    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(
            scrollregion=canvas.bbox("all")
        )
    )

    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)

    canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=1)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    # --- End Scrollable Frame Setup ---


    style = ttk.Style()
    available_themes = style.theme_names()
    preferred_themes = ['clam', 'alt', 'vista', 'xpnative']
    chosen_theme = 'default'
    for theme in preferred_themes:
        if theme in available_themes:
            chosen_theme = theme
            break
    try:
        style.theme_use(chosen_theme)
    except tk.TclError:
        chosen_theme = 'default'
        style.theme_use(chosen_theme)

    default_font = ("Tahoma", 10)
    status_font = ("Consolas", 9)
    paste_btn_width = 5 # تأكد من تعريف هذا المتغير إذا لم يكن معرفًا عالميًا
    style.configure("TLabel", font=default_font)
    style.configure("TButton", font=default_font, padding=3)
    style.configure("TEntry", font=default_font)
    style.configure("TCombobox", font=default_font)
    style.configure("TFrame", padding=5)
    style.configure("TLabelframe", padding=5, font=default_font)
    style.configure("TLabelframe.Label", font=default_font)

    # --- Content Sections (Now inside scrollable_frame) ---

    # --- NEW: Article Scraping Section ---
    # تم تصحيح المسافة البادئة هنا وما يليها
    scrape_frame = ttk.LabelFrame(scrollable_frame, text="1. استخراج البيانات من رابط المقال") # Generic Label
    scrape_frame.pack(pady=5, padx=10, fill="x")
    ttk.Label(scrape_frame, text="رابط المقال:").grid(row=0, column=0, padx=5, pady=2, sticky="w")
    scrape_url_entry = ttk.Entry(scrape_frame, width=55, font=default_font) # Adjusted width slightly
    scrape_url_entry.grid(row=0, column=1, padx=5, pady=2, sticky="ew")

    paste_scrape_url_button = ttk.Button(scrape_frame, text="لصق", command=lambda w=scrape_url_entry: paste_into_widget(w), width=paste_btn_width)
    paste_scrape_url_button.grid(row=0, column=2, padx=(0,1), pady=2) # col 2

    clear_scrape_url_button = ttk.Button(scrape_frame, text="مسح", command=lambda w=scrape_url_entry: clear_widget_content(w), width=paste_btn_width)
    clear_scrape_url_button.grid(row=0, column=3, padx=(1,1), pady=2) # col 3

    # NEW: Open Article URL Button
    open_article_button = ttk.Button(scrape_frame, text="فتح الرابط", command=open_article_url_in_browser, width=8) # width can be adjusted
    open_article_button.grid(row=0, column=4, padx=(1,5), pady=2) # col 4

    scrape_button = ttk.Button(scrape_frame, text="استخراج البيانات", command=handle_scrape_article)
    scrape_button.grid(row=0, column=5, padx=5, pady=2) # col 5

    scrape_frame.grid_columnconfigure(1, weight=1)


    # --- NEW: Image Management Section ---
    image_manage_frame = ttk.LabelFrame(scrollable_frame, text="2. إدارة الصور ومعالجتها")
    image_manage_frame.pack(pady=5, padx=10, fill="x")

    # Input row
    image_input_frame = ttk.Frame(image_manage_frame)
    image_input_frame.pack(fill="x", padx=5, pady=(5,0))
    ttk.Label(image_input_frame, text="إضافة رابط صورة:").pack(side=tk.LEFT, padx=(0, 5))
    new_image_url_entry = ttk.Entry(image_input_frame, width=45, font=default_font) # Adjusted width
    new_image_url_entry.pack(side=tk.LEFT, fill="x", expand=True, padx=5)
    add_image_url_button = ttk.Button(image_input_frame, text="إضافة URL", command=handle_add_image_url) # Removed lambda
    add_image_url_button.pack(side=tk.LEFT, padx=(5, 2))
    paste_new_image_url_button = ttk.Button(image_input_frame, text="لصق", command=lambda w=new_image_url_entry: paste_into_widget(w), width=paste_btn_width)
    paste_new_image_url_button.pack(side=tk.LEFT, padx=(2, 5))
    add_local_image_button = ttk.Button(image_input_frame, text="إضافة من القرص", command=handle_add_local_image) # Removed lambda
    add_local_image_button.pack(side=tk.LEFT, padx=(5, 0))

    # --- NEW: Phosus Enhancement Options ---
    phosus_options_frame = ttk.Frame(image_manage_frame)
    phosus_options_frame.pack(fill="x", padx=5, pady=5)

    ttk.Label(phosus_options_frame, text="نوع تحسين الصورة:").pack(side=tk.LEFT, padx=(0,10))

    phosus_enhancement_var = tk.StringVar(value="none") # Default to no enhancement

    none_rb = ttk.Radiobutton(phosus_options_frame, text="بدون تحسين", variable=phosus_enhancement_var, value="none")
    none_rb.pack(side=tk.LEFT, padx=5)
    auto_enhance_rb = ttk.Radiobutton(phosus_options_frame, text="تحسين تلقائي", variable=phosus_enhancement_var, value="auto_enhance")
    auto_enhance_rb.pack(side=tk.LEFT, padx=5)
    super_res_rb = ttk.Radiobutton(phosus_options_frame, text="دقة فائقة", variable=phosus_enhancement_var, value="super_resolution")
    super_res_rb.pack(side=tk.LEFT, padx=5)
    # --- END NEW Phosus Enhancement Options ---

    # Preview Area (Canvas + Scrollbar + Inner Frame)
    image_preview_outer_frame = ttk.Frame(image_manage_frame, relief=tk.SUNKEN, borderwidth=1)
    image_preview_outer_frame.pack(pady=5, padx=5, fill="x", expand=True)

    # Adjusted height for a slightly larger box, added horizontal scrollbar
    image_preview_canvas = tk.Canvas(image_preview_outer_frame, height=150) # Increased height from 120 to 150
    image_preview_scrollbar_y = ttk.Scrollbar(image_preview_outer_frame, orient=tk.VERTICAL, command=image_preview_canvas.yview)
    image_preview_scrollbar_x = ttk.Scrollbar(image_preview_outer_frame, orient=tk.HORIZONTAL, command=image_preview_canvas.xview) # Added horizontal scrollbar
    image_preview_inner_frame = ttk.Frame(image_preview_canvas) # Frame to hold the actual image previews

    image_preview_inner_frame.bind(
        "<Configure>",
        lambda e: image_preview_canvas.configure(
            # Update scrollregion for both x and y
            scrollregion=image_preview_canvas.bbox("all")
        )
    )

    image_preview_canvas.create_window((0, 0), window=image_preview_inner_frame, anchor="nw")
    # Configure canvas for both scrollbars
    image_preview_canvas.configure(yscrollcommand=image_preview_scrollbar_y.set, xscrollcommand=image_preview_scrollbar_x.set)

    # Pack canvas and scrollbars
    image_preview_canvas.pack(side=tk.TOP, fill=tk.BOTH, expand=True) # Canvas takes top part
    image_preview_scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y) # Y scrollbar on the right
    image_preview_scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X) # X scrollbar on the bottom

    # Process Button
    process_upload_images_button = ttk.Button(image_manage_frame, text="معالجة ورفع الصور المعروضة", command=handle_process_upload_selected)
    process_upload_images_button.pack(pady=(5, 5), padx=5)


    # --- Mod Processing Section ---
    mod_frame = ttk.LabelFrame(scrollable_frame, text="3. معالجة المود (من رابط أو ملف مباشر)")
    mod_frame.pack(pady=5, padx=10, fill="x")

    # --- Row for URL Processing ---
    ttk.Label(mod_frame, text="رابط صفحة المود:").grid(row=0, column=0, padx=5, pady=2, sticky="w")
    mod_page_url_entry = ttk.Entry(mod_frame, width=55, font=default_font) # Adjusted width
    mod_page_url_entry.grid(row=0, column=1, padx=5, pady=2, sticky="ew")
    paste_mod_button = ttk.Button(mod_frame, text="لصق", command=lambda w=mod_page_url_entry: paste_into_widget(w), width=paste_btn_width)
    paste_mod_button.grid(row=0, column=2, padx=(0,1), pady=2)
    clear_mod_button = ttk.Button(mod_frame, text="مسح", command=lambda w=mod_page_url_entry: clear_widget_content(w), width=paste_btn_width)
    clear_mod_button.grid(row=0, column=3, padx=(1,5), pady=2) # تم تغيير (1,1) إلى (1,5) لتوسيط أوضح

    process_mod_button = ttk.Button(mod_frame, text="معالجة من الرابط", command=handle_process_mod)
    process_mod_button.grid(row=0, column=4, padx=5, pady=2, sticky="ew")

    # --- Row for Direct Download Checkbox & File Upload ---
    direct_download_var = tk.BooleanVar(value=False)
    direct_download_checkbox = ttk.Checkbutton(mod_frame, text="رابط مباشر (عند استخدام الرابط أعلاه)", variable=direct_download_var)
    direct_download_checkbox.grid(row=1, column=0, columnspan=3, padx=5, pady=(2,5), sticky="w") # Spans 3 columns now

    # NEW: Upload Mod File Button
    upload_mod_file_button = ttk.Button(mod_frame, text="رفع ملف مود مباشر", command=handle_upload_mod_file)
    upload_mod_file_button.grid(row=1, column=3, padx=5, pady=(2,5), sticky="ew") # Changed to column 3

    # NEW: Get Latest Mod from Downloads Button
    get_latest_mod_button = ttk.Button(mod_frame, text="جلب آخر ملف من التنزيلات", command=handle_get_latest_mod)
    get_latest_mod_button.grid(row=1, column=4, padx=5, pady=(2,5), sticky="ew") # Same column as process_mod_button but next row

    mod_frame.grid_columnconfigure(1, weight=1) # Allow entry field to expand


    # --- NEW: BP/RP Combining Section (from URLs) ---
    bp_rp_frame = ttk.LabelFrame(scrollable_frame, text="3b. (اختياري) دمج BP و RP من روابط في ملف mcaddon واحد")
    bp_rp_frame.pack(pady=5, padx=10, fill="x")

    ttk.Label(bp_rp_frame, text="رابط صفحة BP:").grid(row=0, column=0, padx=5, pady=2, sticky="w")
    bp_url_entry = ttk.Entry(bp_rp_frame, width=65, font=default_font)
    bp_url_entry.grid(row=0, column=1, padx=5, pady=2, sticky="ew")
    paste_bp_button = ttk.Button(bp_rp_frame, text="لصق", command=lambda w=bp_url_entry: paste_into_widget(w), width=paste_btn_width)
    paste_bp_button.grid(row=0, column=2, padx=(0,2), pady=2)
    clear_bp_button = ttk.Button(bp_rp_frame, text="مسح", command=lambda w=bp_url_entry: clear_widget_content(w), width=paste_btn_width)
    clear_bp_button.grid(row=0, column=3, padx=(2,5), pady=2)

    ttk.Label(bp_rp_frame, text="رابط صفحة RP:").grid(row=1, column=0, padx=5, pady=2, sticky="w")
    rp_url_entry = ttk.Entry(bp_rp_frame, width=65, font=default_font)
    rp_url_entry.grid(row=1, column=1, padx=5, pady=2, sticky="ew")
    paste_rp_button = ttk.Button(bp_rp_frame, text="لصق", command=lambda w=rp_url_entry: paste_into_widget(w), width=paste_btn_width)
    paste_rp_button.grid(row=1, column=2, padx=(0,2), pady=2)
    clear_rp_button = ttk.Button(bp_rp_frame, text="مسح", command=lambda w=rp_url_entry: clear_widget_content(w), width=paste_btn_width)
    clear_rp_button.grid(row=1, column=3, padx=(2,5), pady=2)

    process_bp_rp_button = ttk.Button(bp_rp_frame, text="دمج ومعالجة BP و RP", command=handle_process_bp_rp) # Connect command
    process_bp_rp_button.grid(row=0, column=4, rowspan=2, padx=5, pady=2, sticky="ns") # Span button over two rows

    bp_rp_frame.grid_columnconfigure(1, weight=1)

    # --- NEW: BP/RP Combining Section (from Local Files) ---
    bp_rp_local_frame = ttk.LabelFrame(scrollable_frame, text="3c. (اختياري) دمج BP و RP من ملفات محلية في ملف mcpack واحد")
    bp_rp_local_frame.pack(pady=5, padx=10, fill="x")

    # BP File Selection Row
    ttk.Label(bp_rp_local_frame, text="ملف BP المحلي:").grid(row=0, column=0, padx=5, pady=2, sticky="w")
    bp_file_path_entry = ttk.Entry(bp_rp_local_frame, width=65, font=default_font, state=tk.DISABLED)
    bp_file_path_entry.grid(row=0, column=1, padx=5, pady=2, sticky="ew")
    select_bp_file_button = ttk.Button(bp_rp_local_frame, text="اختيار ملف BP", command=handle_select_bp_file)
    select_bp_file_button.grid(row=0, column=2, padx=5, pady=2)
    clear_bp_file_button = ttk.Button(bp_rp_local_frame, text="مسح", command=lambda: clear_bp_file_path(), width=paste_btn_width)
    clear_bp_file_button.grid(row=0, column=3, padx=(2,5), pady=2)

    # RP File Selection Row
    ttk.Label(bp_rp_local_frame, text="ملف RP المحلي:").grid(row=1, column=0, padx=5, pady=2, sticky="w")
    rp_file_path_entry = ttk.Entry(bp_rp_local_frame, width=65, font=default_font, state=tk.DISABLED)
    rp_file_path_entry.grid(row=1, column=1, padx=5, pady=2, sticky="ew")
    select_rp_file_button = ttk.Button(bp_rp_local_frame, text="اختيار ملف RP", command=handle_select_rp_file)
    select_rp_file_button.grid(row=1, column=2, padx=5, pady=2)
    clear_rp_file_button = ttk.Button(bp_rp_local_frame, text="مسح", command=lambda: clear_rp_file_path(), width=paste_btn_width)
    clear_rp_file_button.grid(row=1, column=3, padx=(2,5), pady=2)

    # Process Button
    process_bp_rp_local_button = ttk.Button(bp_rp_local_frame, text="دمج ومعالجة الملفات المحلية", command=handle_process_bp_rp_local)
    process_bp_rp_local_button.grid(row=0, column=4, rowspan=2, padx=5, pady=2, sticky="ns") # Span button over two rows

    bp_rp_local_frame.grid_columnconfigure(1, weight=1)


    ai_desc_frame = ttk.LabelFrame(scrollable_frame, text="4. إنشاء وصف تلقائي (AI)")
    ai_desc_frame.pack(pady=5, padx=10, fill="x")
    ttk.Label(ai_desc_frame, text="مميزات المود (كل ميزة في سطر):").grid(row=0, column=0, columnspan=5, padx=5, pady=(5,0), sticky="w")
    mod_features_text = tk.Text(ai_desc_frame, height=4, width=65, font=default_font, relief=tk.SOLID, borderwidth=1)
    mod_features_text.grid(row=1, column=0, columnspan=2, padx=5, pady=2, sticky="ew") # تم تغيير columnspan إلى 2
    paste_features_button = ttk.Button(ai_desc_frame, text="لصق", command=lambda w=mod_features_text: paste_into_widget(w), width=paste_btn_width)
    paste_features_button.grid(row=1, column=2, padx=(0,2), pady=2, sticky="n")
    clear_features_button = ttk.Button(ai_desc_frame, text="مسح", command=lambda w=mod_features_text: clear_widget_content(w), width=paste_btn_width)
    clear_features_button.grid(row=1, column=3, padx=(2,5), pady=2, sticky="n")
    features_scroll = ttk.Scrollbar(ai_desc_frame, orient=tk.VERTICAL, command=mod_features_text.yview)
    features_scroll.grid(row=1, column=4, padx=(0,5), pady=2, sticky="ns") # تم تغيير column إلى 4
    mod_features_text['yscrollcommand'] = features_scroll.set

    # إضافة إطار للأزرار
    ai_buttons_frame = ttk.Frame(ai_desc_frame)
    ai_buttons_frame.grid(row=2, column=0, columnspan=5, padx=5, pady=2)

    generate_desc_button = ttk.Button(ai_buttons_frame, text="إنشاء وصف تلقائي", command=handle_generate_description)
    generate_desc_button.pack(side=tk.LEFT, padx=5)

    # زر نسخ الوصف المنشأ
    copy_generated_desc_button = ttk.Button(
        ai_buttons_frame,
        text="نسخ الوصف المنشأ",
        command=lambda: copy_from_widget(publish_desc_text) if 'publish_desc_text' in globals() and publish_desc_text.winfo_exists() else messagebox.showwarning("تنبيه", "لم يتم إنشاء وصف بعد")
    )
    copy_generated_desc_button.pack(side=tk.LEFT, padx=5)

    # زر إنشاء الوصف العربي
    generate_arabic_desc_button = ttk.Button(ai_buttons_frame, text="إنشاء وصف عربي", command=handle_generate_arabic_description)
    generate_arabic_desc_button.pack(side=tk.LEFT, padx=5)

    # زر نسخ الوصف العربي المنشأ
    copy_generated_arabic_desc_button = ttk.Button(
        ai_buttons_frame,
        text="نسخ الوصف العربي",
        command=lambda: copy_from_widget(publish_arabic_desc_text) if 'publish_arabic_desc_text' in globals() and publish_arabic_desc_text.winfo_exists() else messagebox.showwarning("تنبيه", "لم يتم إنشاء وصف عربي بعد")
    )
    copy_generated_arabic_desc_button.pack(side=tk.LEFT, padx=5)

    ai_desc_frame.grid_columnconfigure(1, weight=1) # يجب أن تكون 0 أو 1 هنا، اعتمادًا على أي عمود تريد توسيعه


    # --- NEW: MCPEDL Extraction Section ---
    if MCPEDL_SCRAPER_AVAILABLE:
        mcpedl_frame = ttk.LabelFrame(scrollable_frame, text="4.5. استخراج من MCPEDL")
        mcpedl_frame.pack(pady=5, padx=10, fill="x")

        # MCPEDL URL input
        ttk.Label(mcpedl_frame, text="رابط صفحة المود من MCPEDL:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        mcpedl_url_entry = ttk.Entry(mcpedl_frame, width=50, font=default_font)
        mcpedl_url_entry.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

        # Paste button for MCPEDL URL
        paste_mcpedl_url_button = ttk.Button(mcpedl_frame, text="لصق", command=lambda w=mcpedl_url_entry: paste_into_widget(w), width=paste_btn_width)
        paste_mcpedl_url_button.grid(row=0, column=2, padx=(0,2), pady=5)

        # Clear button for MCPEDL URL
        clear_mcpedl_url_button = ttk.Button(mcpedl_frame, text="مسح", command=lambda w=mcpedl_url_entry: clear_widget_content(w), width=paste_btn_width)
        clear_mcpedl_url_button.grid(row=0, column=3, padx=(2,5), pady=5)

        # Extract button
        mcpedl_extract_button = ttk.Button(mcpedl_frame, text="استخراج البيانات من MCPEDL", command=handle_mcpedl_extraction)
        mcpedl_extract_button.grid(row=1, column=0, columnspan=2, padx=5, pady=5, sticky="ew")

        # Clear fields button
        clear_mcpedl_button = ttk.Button(mcpedl_frame, text="مسح الحقول", command=clear_mcpedl_fields)
        clear_mcpedl_button.grid(row=1, column=2, columnspan=2, padx=5, pady=5, sticky="ew")

        # Configure grid weights
        mcpedl_frame.grid_columnconfigure(1, weight=1)

        # Add info label
        info_label = ttk.Label(mcpedl_frame, text="💡 سيتم ملء جميع الحقول تلقائياً من بيانات MCPEDL",
                              font=('Arial', 8), foreground='blue')
        info_label.grid(row=2, column=0, columnspan=4, padx=5, pady=(0,5))

    # --- Publish Section ---
    publish_frame = ttk.LabelFrame(scrollable_frame, text="5. نشر المود في التطبيق (املأ الحقول يدويًا أو عبر الاستخراج/AI)")
    publish_frame.pack(pady=5, padx=10, fill="x")
    col_count = 7 # أو أي عدد أعمدة تستخدمه فعليًا في هذا الإطار

    # Row 0: Name
    ttk.Label(publish_frame, text="اسم المود:").grid(row=0, column=0, padx=5, pady=2, sticky="w")
    publish_name_entry = ttk.Entry(publish_frame, width=45)
    publish_name_entry.grid(row=0, column=1, columnspan=col_count-3, padx=5, pady=2, sticky="ew")
    paste_name_button = ttk.Button(publish_frame, text="لصق", command=lambda w=publish_name_entry: paste_into_widget(w), width=paste_btn_width)
    paste_name_button.grid(row=0, column=col_count-2, padx=(0,2), pady=2)
    clear_name_button = ttk.Button(publish_frame, text="مسح", command=lambda w=publish_name_entry: clear_widget_content(w), width=paste_btn_width)
    clear_name_button.grid(row=0, column=col_count-1, padx=(2,5), pady=2)

    # Row 1: Description
    ttk.Label(publish_frame, text="الوصف:").grid(row=1, column=0, padx=5, pady=2, sticky="nw")
    publish_desc_text = scrolledtext.ScrolledText(publish_frame, height=12, width=45, font=default_font, relief=tk.SOLID, borderwidth=1, wrap=tk.WORD)
    publish_desc_text.grid(row=1, column=1, columnspan=col_count-2, padx=5, pady=2, sticky="nsew") # تم تغيير -1 إلى -2 لتناسب الأزرار

    # إضافة إطار للأزرار الخاصة بالوصف
    desc_buttons_frame = ttk.Frame(publish_frame)
    desc_buttons_frame.grid(row=1, column=col_count-1, padx=5, pady=2, sticky="n")

    paste_desc_button = ttk.Button(desc_buttons_frame, text="لصق", command=lambda w=publish_desc_text: paste_into_widget(w), width=paste_btn_width)
    paste_desc_button.pack(side=tk.TOP, pady=(0,2))

    copy_desc_button = ttk.Button(desc_buttons_frame, text="نسخ", command=lambda w=publish_desc_text: copy_from_widget(w), width=paste_btn_width)
    copy_desc_button.pack(side=tk.TOP, pady=2)

    clear_desc_button = ttk.Button(desc_buttons_frame, text="مسح", command=lambda w=publish_desc_text: clear_widget_content(w), width=paste_btn_width)
    clear_desc_button.pack(side=tk.TOP, pady=(2,0))

    # Row 1.5: Arabic Description
    ttk.Label(publish_frame, text="الوصف العربي:").grid(row=2, column=0, padx=5, pady=2, sticky="nw")
    publish_arabic_desc_text = scrolledtext.ScrolledText(publish_frame, height=8, width=45, font=default_font, relief=tk.SOLID, borderwidth=1, wrap=tk.WORD)
    publish_arabic_desc_text.grid(row=2, column=1, columnspan=col_count-2, padx=5, pady=2, sticky="nsew")

    # إضافة إطار للأزرار الخاصة بالوصف العربي
    arabic_desc_buttons_frame = ttk.Frame(publish_frame)
    arabic_desc_buttons_frame.grid(row=2, column=col_count-1, padx=5, pady=2, sticky="n")

    paste_arabic_desc_button = ttk.Button(arabic_desc_buttons_frame, text="لصق", command=lambda w=publish_arabic_desc_text: paste_into_widget(w), width=paste_btn_width)
    paste_arabic_desc_button.pack(side=tk.TOP, pady=(0,2))

    copy_arabic_desc_button = ttk.Button(arabic_desc_buttons_frame, text="نسخ", command=lambda w=publish_arabic_desc_text: copy_from_widget(w), width=paste_btn_width)
    copy_arabic_desc_button.pack(side=tk.TOP, pady=2)

    clear_arabic_desc_button = ttk.Button(arabic_desc_buttons_frame, text="مسح", command=lambda w=publish_arabic_desc_text: clear_widget_content(w), width=paste_btn_width)
    clear_arabic_desc_button.pack(side=tk.TOP, pady=(2,0))

    # Row 3: Category (Combobox), Version
    ttk.Label(publish_frame, text="الفئة:").grid(row=3, column=0, padx=5, pady=2, sticky="w")
    category_combobox = ttk.Combobox(publish_frame, values=CATEGORIES, state="readonly", width=18)
    category_combobox.grid(row=3, column=1, columnspan=2, padx=5, pady=2, sticky="w")
    if CATEGORIES: category_combobox.set(CATEGORIES[0])

    ttk.Label(publish_frame, text="الإصدار:").grid(row=3, column=3, padx=5, pady=2, sticky="e")
    publish_version_entry = ttk.Entry(publish_frame, width=8)
    publish_version_entry.grid(row=3, column=4, padx=5, pady=2, sticky="w")
    paste_ver_button = ttk.Button(publish_frame, text="لصق", command=lambda w=publish_version_entry: paste_into_widget(w), width=paste_btn_width)
    paste_ver_button.grid(row=3, column=5, padx=(0,2), pady=2, sticky="w")
    clear_ver_button = ttk.Button(publish_frame, text="مسح", command=lambda w=publish_version_entry: clear_widget_content(w), width=paste_btn_width)
    clear_ver_button.grid(row=3, column=6, padx=(2,5), pady=2, sticky="w")

    # Row 4: Size (New Row)
    ttk.Label(publish_frame, text="الحجم:").grid(row=4, column=0, padx=5, pady=2, sticky="w")
    publish_size_entry = ttk.Entry(publish_frame, width=15, state=tk.DISABLED)
    publish_size_entry.grid(row=4, column=1, columnspan=2, padx=5, pady=2, sticky="w")

    # Row 5: Primary Image URL
    ttk.Label(publish_frame, text="رابط الصورة الرئيسية:").grid(row=5, column=0, padx=5, pady=2, sticky="w")
    publish_primary_image_entry = ttk.Entry(publish_frame, width=45)
    publish_primary_image_entry.grid(row=5, column=1, columnspan=col_count-3, padx=5, pady=2, sticky="ew")
    paste_primary_img_button = ttk.Button(publish_frame, text="لصق", command=lambda w=publish_primary_image_entry: paste_into_widget(w), width=paste_btn_width)
    paste_primary_img_button.grid(row=5, column=col_count-2, padx=(0,2), pady=2)
    clear_primary_img_button = ttk.Button(publish_frame, text="مسح", command=lambda w=publish_primary_image_entry: clear_widget_content(w), width=paste_btn_width)
    clear_primary_img_button.grid(row=5, column=col_count-1, padx=(2,5), pady=2)

    # Row 6: Other Image URLs
    ttk.Label(publish_frame, text="روابط صور إضافية (حد أقصى 6):").grid(row=6, column=0, padx=5, pady=2, sticky="nw")
    publish_other_images_text = tk.Text(publish_frame, height=4, width=45, font=default_font, relief=tk.SOLID, borderwidth=1)
    publish_other_images_text.grid(row=6, column=1, columnspan=col_count-4, padx=5, pady=2, sticky="ew")
    paste_other_img_button = ttk.Button(publish_frame, text="لصق", command=lambda w=publish_other_images_text: paste_into_widget(w), width=paste_btn_width)
    paste_other_img_button.grid(row=6, column=col_count-3, padx=(0,2), pady=2, sticky="n")
    clear_other_img_button = ttk.Button(publish_frame, text="مسح", command=lambda w=publish_other_images_text: clear_widget_content(w), width=paste_btn_width)
    clear_other_img_button.grid(row=6, column=col_count-2, padx=(2,5), pady=2, sticky="n")
    other_img_scroll = ttk.Scrollbar(publish_frame, orient=tk.VERTICAL, command=publish_other_images_text.yview)
    other_img_scroll.grid(row=6, column=col_count-1, padx=(0,5), pady=2, sticky="ns")
    publish_other_images_text['yscrollcommand'] = other_img_scroll.set

    # Row 7: Mod Download URL
    ttk.Label(publish_frame, text="رابط تحميل المود:").grid(row=7, column=0, padx=5, pady=2, sticky="w")
    publish_mod_url_entry = ttk.Entry(publish_frame, width=45)
    publish_mod_url_entry.grid(row=7, column=1, columnspan=col_count-3, padx=5, pady=2, sticky="ew")
    paste_mod_url_button = ttk.Button(publish_frame, text="لصق", command=lambda w=publish_mod_url_entry: paste_into_widget(w), width=paste_btn_width)
    paste_mod_url_button.grid(row=7, column=col_count-2, padx=(0,2), pady=2)
    clear_mod_url_button = ttk.Button(publish_frame, text="مسح", command=lambda w=publish_mod_url_entry: clear_widget_content(w), width=paste_btn_width)
    clear_mod_url_button.grid(row=7, column=col_count-1, padx=(2,5), pady=2)

    # Row 8: Creator Name
    ttk.Label(publish_frame, text="اسم صانع المود:").grid(row=8, column=0, padx=5, pady=2, sticky="w")
    publish_creator_name_entry = ttk.Entry(publish_frame, width=45)
    publish_creator_name_entry.grid(row=8, column=1, columnspan=col_count-3, padx=5, pady=2, sticky="ew")
    paste_creator_name_button = ttk.Button(publish_frame, text="لصق", command=lambda w=publish_creator_name_entry: paste_into_widget(w), width=paste_btn_width)
    paste_creator_name_button.grid(row=8, column=col_count-2, padx=(0,2), pady=2)
    clear_creator_name_button = ttk.Button(publish_frame, text="مسح", command=lambda w=publish_creator_name_entry: clear_widget_content(w), width=paste_btn_width)
    clear_creator_name_button.grid(row=8, column=col_count-1, padx=(2,5), pady=2)

    # Row 9: Creator Contact Info
    ttk.Label(publish_frame, text="معلومات التواصل:").grid(row=9, column=0, padx=5, pady=2, sticky="w")
    publish_creator_contact_entry = ttk.Entry(publish_frame, width=45)
    publish_creator_contact_entry.grid(row=9, column=1, columnspan=col_count-3, padx=5, pady=2, sticky="ew")
    paste_creator_contact_button = ttk.Button(publish_frame, text="لصق", command=lambda w=publish_creator_contact_entry: paste_into_widget(w), width=paste_btn_width)
    paste_creator_contact_button.grid(row=9, column=col_count-2, padx=(0,2), pady=2)
    clear_creator_contact_button = ttk.Button(publish_frame, text="مسح", command=lambda w=publish_creator_contact_entry: clear_widget_content(w), width=paste_btn_width)
    clear_creator_contact_button.grid(row=9, column=col_count-1, padx=(2,5), pady=2)

    # Row 10: Custom Social Sites (Multiple)
    ttk.Label(publish_frame, text="مواقع التواصل المخصصة:").grid(row=10, column=0, padx=5, pady=2, sticky="nw")

    # Frame for custom social sites
    custom_sites_frame = ttk.Frame(publish_frame)
    custom_sites_frame.grid(row=10, column=1, columnspan=col_count-1, padx=5, pady=2, sticky="ew")

    # Configure grid weights for custom_sites_frame
    custom_sites_frame.grid_columnconfigure(0, weight=2)  # Name entry
    custom_sites_frame.grid_columnconfigure(3, weight=3)  # URL entry

    # List to store custom site entries
    custom_site_entries = []

    def add_custom_site_row():
        """Add a new row for custom social site"""
        row_num = len(custom_site_entries)

        # Site name entry
        site_name_entry = ttk.Entry(custom_sites_frame, width=18)
        site_name_entry.grid(row=row_num, column=0, padx=(0,2), pady=2, sticky="ew")
        site_name_entry.insert(0, "اسم الموقع")

        # Paste button for site name
        paste_name_button = ttk.Button(custom_sites_frame, text="لصق", width=4,
                                     command=lambda w=site_name_entry: paste_into_widget(w))
        paste_name_button.grid(row=row_num, column=1, padx=1, pady=2)

        # Clear button for site name
        clear_name_button = ttk.Button(custom_sites_frame, text="مسح", width=4,
                                     command=lambda w=site_name_entry: clear_widget_content(w))
        clear_name_button.grid(row=row_num, column=2, padx=1, pady=2)

        # Site URL entry
        site_url_entry = ttk.Entry(custom_sites_frame, width=25)
        site_url_entry.grid(row=row_num, column=3, padx=(2,2), pady=2, sticky="ew")
        site_url_entry.insert(0, "رابط الموقع")

        # Paste button for site URL
        paste_url_button = ttk.Button(custom_sites_frame, text="لصق", width=4,
                                    command=lambda w=site_url_entry: paste_into_widget(w))
        paste_url_button.grid(row=row_num, column=4, padx=1, pady=2)

        # Clear button for site URL
        clear_url_button = ttk.Button(custom_sites_frame, text="مسح", width=4,
                                    command=lambda w=site_url_entry: clear_widget_content(w))
        clear_url_button.grid(row=row_num, column=5, padx=1, pady=2)

        # Remove button
        remove_button = ttk.Button(custom_sites_frame, text="حذف", width=6,
                                 command=lambda: remove_custom_site_row(row_num))
        remove_button.grid(row=row_num, column=6, padx=(2,0), pady=2)

        # Store the entries and buttons
        custom_site_entries.append({
            'name_entry': site_name_entry,
            'url_entry': site_url_entry,
            'paste_name_button': paste_name_button,
            'clear_name_button': clear_name_button,
            'paste_url_button': paste_url_button,
            'clear_url_button': clear_url_button,
            'remove_button': remove_button,
            'row': row_num
        })

        # Update add button position
        add_site_button.grid(row=len(custom_site_entries), column=0, columnspan=7, pady=5)

    def remove_custom_site_row(row_to_remove):
        """Remove a custom social site row"""
        if row_to_remove < len(custom_site_entries):
            # Destroy widgets
            entry_data = custom_site_entries[row_to_remove]
            entry_data['name_entry'].destroy()
            entry_data['url_entry'].destroy()
            entry_data['paste_name_button'].destroy()
            entry_data['clear_name_button'].destroy()
            entry_data['paste_url_button'].destroy()
            entry_data['clear_url_button'].destroy()
            entry_data['remove_button'].destroy()

            # Remove from list
            custom_site_entries.pop(row_to_remove)

            # Re-grid remaining entries
            for i, entry_data in enumerate(custom_site_entries):
                # Re-grid name entry and buttons
                entry_data['name_entry'].grid(row=i, column=0, padx=(0,2), pady=2, sticky="ew")
                entry_data['paste_name_button'].grid(row=i, column=1, padx=1, pady=2)
                entry_data['clear_name_button'].grid(row=i, column=2, padx=1, pady=2)

                # Re-grid URL entry and buttons
                entry_data['url_entry'].grid(row=i, column=3, padx=(2,2), pady=2, sticky="ew")
                entry_data['paste_url_button'].grid(row=i, column=4, padx=1, pady=2)
                entry_data['clear_url_button'].grid(row=i, column=5, padx=1, pady=2)

                # Re-grid remove button with updated command
                entry_data['remove_button'].configure(command=lambda idx=i: remove_custom_site_row(idx))
                entry_data['remove_button'].grid(row=i, column=6, padx=(2,0), pady=2)
                entry_data['row'] = i

            # Update add button position
            add_site_button.grid(row=len(custom_site_entries), column=0, columnspan=7, pady=5)

    # Add site button
    add_site_button = ttk.Button(custom_sites_frame, text="إضافة موقع", command=add_custom_site_row)
    add_site_button.grid(row=0, column=0, columnspan=7, pady=5)

    # Add first row by default
    add_custom_site_row()

    # Row 11: Publish Button
    publish_mod_button = ttk.Button(publish_frame, text="نشر المود الآن", command=handle_publish_mod)
    publish_mod_button.grid(row=11, column=0, columnspan=col_count, padx=5, pady=5)

    publish_frame.grid_columnconfigure(1, weight=1)
    publish_frame.grid_rowconfigure(1, weight=1) # للسماح بتوسيع حقل الوصف


    # --- Results Section (Mod Only) ---
    results_outer_frame = ttk.Frame(scrollable_frame)
    results_outer_frame.pack(pady=(0, 5), padx=5, fill="x")

    mod_result_frame = ttk.LabelFrame(results_outer_frame, text="رابط المود النهائي")
    mod_result_frame.pack(side=tk.LEFT, pady=0, padx=5, fill="x", expand=True)
    mod_result_text = scrolledtext.ScrolledText(mod_result_frame, wrap=tk.WORD, height=4, state=tk.DISABLED, font=status_font, relief=tk.SOLID, borderwidth=1)
    mod_result_text.pack(side=tk.LEFT, fill="x", expand=True, padx=5, pady=5)
    copy_mod_result_button = ttk.Button(mod_result_frame, text="نسخ", command=lambda: copy_from_widget(mod_result_text), width=5)
    copy_mod_result_button.pack(side=tk.RIGHT, padx=5, pady=5)

    # --- Status Section ---
    status_frame = ttk.LabelFrame(scrollable_frame, text="سجل الحالة")
    status_frame.pack(pady=(0, 10), padx=10, fill="both", expand=True)

    status_text_container = ttk.Frame(status_frame) # إطار لاحتواء status_text والأزرار الجانبية
    status_text_container.pack(fill="both", expand=True)

    status_text = scrolledtext.ScrolledText(status_text_container, wrap=tk.WORD, height=8, state=tk.DISABLED, font=status_font, relief=tk.SOLID, borderwidth=1)
    status_text.pack(side=tk.LEFT, fill="both", expand=True, padx=(5,0), pady=5)

    status_button_frame = ttk.Frame(status_text_container) # إطار للأزرار بجانب status_text
    status_button_frame.pack(side=tk.RIGHT, fill="y", padx=(2,5), pady=5)

    copy_log_button = ttk.Button(status_button_frame, text="نسخ\nالسجل", command=lambda: copy_from_widget(status_text), width=5)
    copy_log_button.pack(side=tk.TOP, pady=2, anchor='n')

    manage_keys_button = ttk.Button(status_button_frame, text="إدارة\nمفاتيح\nAPI", command=open_api_key_dialog, width=5)
    manage_keys_button.pack(side=tk.TOP, pady=5, anchor='n')


    # --- NEW: Batch Processing Section ---
    batch_frame = ttk.LabelFrame(scrollable_frame, text="6. نشر تلقائي من ملف")
    batch_frame.pack(pady=5, padx=10, fill="x")

    select_batch_file_button = ttk.Button(batch_frame, text="اختيار ملف الروابط (.txt)", command=select_batch_file)
    select_batch_file_button.grid(row=0, column=0, padx=5, pady=5, sticky="w")

    batch_file_entry = ttk.Entry(batch_frame, width=50, state=tk.DISABLED, font=default_font)
    batch_file_entry.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

    start_batch_button = ttk.Button(batch_frame, text="بدء النشر التلقائي", command=handle_batch_publish, state=tk.DISABLED)
    start_batch_button.grid(row=0, column=2, padx=5, pady=5, sticky="e")

    stop_batch_process_button = ttk.Button(batch_frame, text="إيقاف المعالجة", command=request_stop_batch, state=tk.DISABLED)
    stop_batch_process_button.grid(row=0, column=3, padx=5, pady=5, sticky="e")

    batch_frame.grid_columnconfigure(1, weight=1)


    # --- NEW: Review & Recovery Section ---
    incomplete_frame = ttk.LabelFrame(scrollable_frame, text="7. Review & Recovery Section")
    incomplete_frame.pack(pady=5, padx=10, fill="x")

    load_incomplete_button = ttk.Button(incomplete_frame, text="Load Failed Mod (incomplete_mods.json)", command=handle_load_incomplete)
    load_incomplete_button.grid(row=0, column=0, padx=5, pady=5, sticky="ew")

    load_pending_review_button = ttk.Button(incomplete_frame, text="Load for Review (pending_review.json)", command=lambda: handle_load_pending_review_mod())
    load_pending_review_button.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

    delete_pending_review_button = ttk.Button(incomplete_frame, text="Delete from Review (pending_review.json)", command=handle_delete_pending_review_mod)
    delete_pending_review_button.grid(row=0, column=2, padx=5, pady=5, sticky="ew")

    delete_all_pending_review_button = ttk.Button(incomplete_frame, text="حذف جميع المودات من قائمة المراجعة", command=handle_delete_all_pending_review_mods)
    delete_all_pending_review_button.grid(row=0, column=3, padx=5, pady=5, sticky="ew")

    incomplete_frame.grid_columnconfigure(0, weight=1)
    incomplete_frame.grid_columnconfigure(1, weight=1)
    incomplete_frame.grid_columnconfigure(2, weight=1)
    incomplete_frame.grid_columnconfigure(3, weight=1)


    # --- Initial Checks & Button States ---
    update_status("بدء تشغيل الأداة...")
    if not check_default_batch_file():
        update_status(f"Default batch file '{DEFAULT_BATCH_FILENAME}' not found. Please select a file manually.")

    all_paste_buttons = []
    if 'paste_mod_button' in locals(): all_paste_buttons.append(paste_mod_button)
    if 'paste_name_button' in locals(): all_paste_buttons.append(paste_name_button)
    if 'paste_desc_button' in locals(): all_paste_buttons.append(paste_desc_button)
    if 'paste_ver_button' in locals(): all_paste_buttons.append(paste_ver_button)
    if 'paste_primary_img_button' in locals(): all_paste_buttons.append(paste_primary_img_button)
    if 'paste_other_img_button' in locals(): all_paste_buttons.append(paste_other_img_button)
    if 'paste_mod_url_button' in locals(): all_paste_buttons.append(paste_mod_url_button)
    if 'paste_scrape_url_button' in locals(): all_paste_buttons.append(paste_scrape_url_button)
    if 'paste_bp_button' in locals(): all_paste_buttons.append(paste_bp_button)
    if 'paste_rp_button' in locals(): all_paste_buttons.append(paste_rp_button)
    if 'paste_features_button' in locals(): all_paste_buttons.append(paste_features_button)
    if 'paste_new_image_url_button' in locals(): all_paste_buttons.append(paste_new_image_url_button) # زر اللصق لرابط الصورة الجديدة
    if 'paste_creator_name_button' in locals(): all_paste_buttons.append(paste_creator_name_button)
    if 'paste_creator_contact_button' in locals(): all_paste_buttons.append(paste_creator_contact_button)
    if 'paste_mcpedl_url_button' in locals(): all_paste_buttons.append(paste_mcpedl_url_button)



    all_copy_buttons = []
    # إذا كان لديك زر لنسخ نتائج الصور، أضفه هنا
    # if 'copy_image_results_button' in locals(): all_copy_buttons.append(copy_image_results_button)
    if 'copy_mod_result_button' in locals(): all_copy_buttons.append(copy_mod_result_button)
    if 'copy_log_button' in locals(): all_copy_buttons.append(copy_log_button)


    # تأكد من أن process_image_button معرف قبل استخدامه هنا إذا كان هذا هو الاسم الصحيح
    # إذا كنت تقصد process_upload_images_button، فتأكد من أن هذا هو الاسم المستخدم
    process_image_btn_ref = None
    if 'process_upload_images_button' in globals(): # هذا هو الاسم المستخدم في الكود أعلاه
        process_image_btn_ref = process_upload_images_button


    if not STORAGE_CLIENT_OK:
        update_status("!!! خطأ فادح: فشل الاتصال بـ Supabase Storage (رفع الملفات).")
        messagebox.showerror("خطأ في الإعداد", "فشل الاتصال بـ Supabase Storage.")
        if process_image_btn_ref: process_image_btn_ref.config(state=tk.DISABLED)
        if 'process_mod_button' in locals(): process_mod_button.config(state=tk.DISABLED)
        if 'publish_mod_button' in locals(): publish_mod_button.config(state=tk.DISABLED)
        if 'upload_mod_file_button' in locals(): upload_mod_file_button.config(state=tk.DISABLED) # تعطيل زر رفع الملف أيضًا
    else:
        update_status("تم تهيئة Supabase Storage بنجاح.")

    if not APP_DB_CLIENT_OK:
        update_status("!!! خطأ فادح: فشل الاتصال بقاعدة بيانات التطبيق (نشر المودات).")
        messagebox.showerror("خطأ في الإعداد", "فشل الاتصال بقاعدة بيانات التطبيق.")
        if 'publish_mod_button' in locals(): publish_mod_button.config(state=tk.DISABLED)
    else:
        update_status("تم تهيئة قاعدة بيانات التطبيق بنجاح.")

    if not GEMINI_CLIENT_OK:
        update_status("تحذير: لم يتم تهيئة Gemini. زر إنشاء الوصف التلقائي معطل.")
        if 'generate_desc_button' in globals() and generate_desc_button.winfo_exists():
            generate_desc_button.config(state=tk.DISABLED)

    if not PYPERCLIP_AVAILABLE:
        update_status("تحذير: مكتبة pyperclip غير موجودة. أزرار اللصق/النسخ معطلة.")
        for btn in all_paste_buttons:
            if btn and btn.winfo_exists(): btn.config(state=tk.DISABLED)
        for btn in all_copy_buttons:
            if btn and btn.winfo_exists(): btn.config(state=tk.DISABLED)
    else:
        update_status("تم العثور على pyperclip. أزرار اللصق/النسخ مفعلة.")

    update_status("الأداة جاهزة.")
    window.mainloop()
