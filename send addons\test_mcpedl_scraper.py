#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وحدة استخراج MCPEDL
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_mcpedl_scraper():
    """اختبار وحدة استخراج MCPEDL"""
    print("🧪 بدء اختبار وحدة استخراج MCPEDL")
    print("=" * 50)
    
    try:
        # محاولة استيراد الوحدة
        from mcpedl_scraper_module import MCPEDLScraper, scrape_mcpedl_mod
        print("✅ تم استيراد وحدة MCPEDL بنجاح")
    except ImportError as e:
        print(f"❌ فشل في استيراد وحدة MCPEDL: {e}")
        return False
    
    # إنشاء مثيل من المستخرج
    try:
        scraper = MCPEDLScraper()
        print("✅ تم إنشاء مثيل من المستخرج")
    except Exception as e:
        print(f"❌ فشل في إنشاء المستخرج: {e}")
        return False
    
    # اختبار التحقق من صحة الروابط
    print("\n🔍 اختبار التحقق من صحة الروابط:")
    
    valid_urls = [
        "https://mcpedl.com/dragon-mounts-v1-3-25/",
        "http://mcpedl.com/some-mod/",
        "https://www.mcpedl.com/another-mod/"
    ]
    
    invalid_urls = [
        "https://google.com",
        "https://minecraft.net",
        "not-a-url",
        ""
    ]
    
    # اختبار الروابط الصحيحة
    for url in valid_urls:
        if scraper.is_valid_mcpedl_url(url):
            print(f"✅ رابط صحيح: {url}")
        else:
            print(f"❌ رابط خاطئ (يجب أن يكون صحيح): {url}")
    
    # اختبار الروابط الخاطئة
    for url in invalid_urls:
        if not scraper.is_valid_mcpedl_url(url):
            print(f"✅ رابط خاطئ (كما متوقع): {url}")
        else:
            print(f"❌ رابط صحيح (يجب أن يكون خاطئ): {url}")
    
    # اختبار دوال التنظيف
    print("\n🧹 اختبار دوال تنظيف النص:")
    
    test_texts = [
        "  نص مع مسافات زائدة  ",
        "نص\nمع\nأسطر\nجديدة",
        "نص مع أحرف خاصة @#$%",
        ""
    ]
    
    for text in test_texts:
        cleaned = scraper.clean_text(text)
        print(f"الأصلي: '{text}' → المنظف: '{cleaned}'")
    
    # اختبار استخراج أرقام الإصدارات
    print("\n🎮 اختبار استخراج أرقام الإصدارات:")
    
    version_texts = [
        "For Minecraft 1.20.1+",
        "Requires Minecraft PE 1.19.0",
        "Compatible with 1.18.2, 1.19.0, 1.20.0",
        "No version info here"
    ]
    
    for text in version_texts:
        versions = scraper.extract_version_numbers(text)
        print(f"النص: '{text}' → الإصدارات: {versions}")
    
    # اختبار استخراج حجم الملف
    print("\n📦 اختبار استخراج حجم الملف:")
    
    size_texts = [
        "File size: 2.5 MB",
        "Download (1.2 KB)",
        "Size: 15.7 GB",
        "No size info"
    ]
    
    for text in size_texts:
        size = scraper.extract_file_size(text)
        print(f"النص: '{text}' → الحجم: {size}")
    
    # اختبار تحديد منصة التواصل الاجتماعي
    print("\n🌐 اختبار تحديد منصة التواصل الاجتماعي:")
    
    social_urls = [
        "https://youtube.com/channel/123",
        "https://discord.gg/abc123",
        "https://twitter.com/username",
        "https://github.com/user/repo",
        "https://unknown-site.com"
    ]
    
    for url in social_urls:
        platform = scraper.extract_social_platform(url)
        print(f"الرابط: '{url}' → المنصة: {platform}")
    
    # اختبار التحقق من صحة روابط الصور
    print("\n🖼️ اختبار التحقق من صحة روابط الصور:")
    
    image_urls = [
        "https://example.com/image.jpg",
        "https://example.com/photo.png",
        "https://example.com/animation.gif",
        "https://example.com/document.pdf",
        ""
    ]
    
    for url in image_urls:
        is_valid = scraper.validate_image_url(url)
        print(f"الرابط: '{url}' → صالح: {is_valid}")
    
    # إغلاق المستخرج
    scraper.close()
    print("\n✅ تم إغلاق المستخرج")
    
    print("\n" + "=" * 50)
    print("✅ انتهى اختبار وحدة MCPEDL بنجاح!")
    return True

def test_real_extraction():
    """اختبار استخراج حقيقي من MCPEDL (اختياري)"""
    print("\n🌐 اختبار استخراج حقيقي من MCPEDL")
    print("=" * 50)
    
    # رابط مود للاختبار (يمكن تغييره)
    test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
    
    try:
        print(f"📥 محاولة استخراج من: {test_url}")
        
        from mcpedl_scraper_module import scrape_mcpedl_mod
        
        mod_data = scrape_mcpedl_mod(test_url)
        
        if mod_data:
            print("✅ تم الاستخراج بنجاح!")
            print("\n📋 البيانات المستخرجة:")
            
            for key, value in mod_data.items():
                if isinstance(value, list):
                    print(f"  {key}: {len(value)} عنصر")
                    if value:  # إذا كانت القائمة غير فارغة
                        for i, item in enumerate(value[:3]):  # أول 3 عناصر فقط
                            print(f"    [{i}]: {str(item)[:50]}...")
                elif isinstance(value, str) and len(value) > 100:
                    print(f"  {key}: {value[:100]}...")
                else:
                    print(f"  {key}: {value}")
        else:
            print("❌ فشل في الاستخراج")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاستخراج: {e}")
        return False
    
    print("\n✅ انتهى الاختبار الحقيقي بنجاح!")
    return True

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار شامل لوحدة استخراج MCPEDL")
    print("=" * 60)
    
    try:
        # اختبار الوحدة الأساسية
        if not test_mcpedl_scraper():
            print("❌ فشل في الاختبار الأساسي")
            return False
        
        # سؤال المستخدم عن الاختبار الحقيقي
        try:
            response = input("\n❓ هل تريد تشغيل اختبار استخراج حقيقي من الإنترنت؟ (y/n): ").lower().strip()
            
            if response in ['y', 'yes', 'نعم', 'ن']:
                if not test_real_extraction():
                    print("⚠️ الاختبار الحقيقي فشل لكن الوحدة تعمل")
            else:
                print("⏭️ تم تخطي الاختبار الحقيقي")
        
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف الاختبار")
        
        print("\n" + "=" * 60)
        print("🎉 انتهت جميع الاختبارات!")
        print("💡 الوحدة جاهزة للاستخدام في الأداة الرئيسية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
