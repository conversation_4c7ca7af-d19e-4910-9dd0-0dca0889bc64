# -*- coding: utf-8 -*-
"""
مستخرج MCPEDL باستخدام Selenium للمواقع التي تعتمد على JavaScript
"""

import time
from typing import Dict, List, Optional, Any
from bs4 import BeautifulSoup

# محاولة استيراد selenium
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, WebDriverException
    SELENIUM_AVAILABLE = True
    print("Selenium متوفر - يمكن استخدامه للمواقع المعقدة")
except ImportError:
    SELENIUM_AVAILABLE = False
    print("Selenium غير متوفر - سيتم استخدام الطريقة العادية فقط")

class MCPEDLSeleniumScraper:
    """مستخرج MCPEDL باستخدام Selenium"""
    
    def __init__(self):
        self.driver = None
        
    def setup_driver(self):
        """إعداد متصفح Chrome"""
        if not SELENIUM_AVAILABLE:
            return False
        
        try:
            chrome_options = Options()
            
            # إعدادات لتجاوز الكشف
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # إعدادات إضافية
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-images')  # تسريع التحميل
            
            # تشغيل في الخلفية (اختياري)
            # chrome_options.add_argument('--headless')
            
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # إخفاء خصائص webdriver
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print("تم إعداد متصفح Chrome بنجاح")
            return True
            
        except Exception as e:
            print(f"فشل في إعداد متصفح Chrome: {e}")
            return False
    
    def fetch_page_with_selenium(self, url: str) -> Optional[BeautifulSoup]:
        """جلب الصفحة باستخدام Selenium"""
        if not self.driver:
            if not self.setup_driver():
                return None
        
        try:
            print(f"جلب الصفحة باستخدام Selenium: {url}")
            
            # الانتقال إلى الصفحة
            self.driver.get(url)
            
            # انتظار تحميل العنوان
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "title"))
            )
            
            # انتظار إضافي لتحميل المحتوى
            time.sleep(5)
            
            # انتظار تحميل المحتوى الأساسي
            try:
                WebDriverWait(self.driver, 15).until(
                    EC.any_of(
                        EC.presence_of_element_located((By.TAG_NAME, "article")),
                        EC.presence_of_element_located((By.CLASS_NAME, "entry-content")),
                        EC.presence_of_element_located((By.CLASS_NAME, "post-content")),
                        EC.presence_of_element_located((By.TAG_NAME, "main"))
                    )
                )
                print("تم تحميل المحتوى الأساسي")
            except TimeoutException:
                print("انتهت مهلة انتظار المحتوى الأساسي - المتابعة بالمحتوى الحالي")
            
            # التمرير لأسفل لتحميل المحتوى الديناميكي
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
            time.sleep(2)
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(2)
            
            # الحصول على HTML
            html_content = self.driver.page_source
            
            # حفظ HTML للتشخيص
            try:
                with open('debug_selenium_page.html', 'w', encoding='utf-8') as f:
                    f.write(html_content)
                print("تم حفظ HTML من Selenium للتشخيص")
            except:
                pass
            
            # تحليل HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # التحقق من جودة المحتوى
            page_text = soup.get_text()
            if len(page_text.strip()) < 1000:
                print(f"محتوى قليل من Selenium ({len(page_text.strip())} حرف)")
                return None
            
            print("تم جلب الصفحة بنجاح باستخدام Selenium")
            return soup
            
        except TimeoutException:
            print("انتهت مهلة تحميل الصفحة")
            return None
        except WebDriverException as e:
            print(f"خطأ في WebDriver: {e}")
            return None
        except Exception as e:
            print(f"خطأ عام في Selenium: {e}")
            return None
    
    def close(self):
        """إغلاق المتصفح"""
        if self.driver:
            try:
                self.driver.quit()
                print("تم إغلاق متصفح Selenium")
            except:
                pass
            self.driver = None

def scrape_mcpedl_with_selenium(url: str) -> Optional[Dict[str, Any]]:
    """استخراج بيانات MCPEDL باستخدام Selenium"""
    if not SELENIUM_AVAILABLE:
        print("Selenium غير متوفر")
        return None
    
    scraper = MCPEDLSeleniumScraper()
    
    try:
        # جلب الصفحة
        soup = scraper.fetch_page_with_selenium(url)
        
        if not soup:
            return None
        
        # استخراج البيانات الأساسية باستخدام نفس منطق المستخرج العادي
        from mcpedl_scraper_module import MCPEDLScraper
        
        # إنشاء مستخرج عادي لاستخدام دوال الاستخراج
        normal_scraper = MCPEDLScraper()
        
        mod_data = {
            'name': normal_scraper.extract_title(soup),
            'description': normal_scraper.extract_description(soup),
            'category': normal_scraper.extract_category(soup),
            'image_urls': normal_scraper.extract_images(soup, url),
            'version': normal_scraper.extract_versions(soup),
            'source_url': url
        }
        
        # معلومات التحميل
        download_info = normal_scraper.extract_download_info(soup, url)
        mod_data.update(download_info)
        
        # معلومات المطور
        creator_info = normal_scraper.extract_creator_info(soup)
        mod_data.update(creator_info)
        
        normal_scraper.close()
        
        # التحقق من البيانات الأساسية
        if not mod_data['name']:
            print("لم يتم العثور على اسم المود")
            return None
        
        print(f"تم استخراج بيانات المود بنجاح باستخدام Selenium: {mod_data['name']}")
        return mod_data
        
    finally:
        scraper.close()

# دالة للاختبار
def test_selenium_scraper():
    """اختبار مستخرج Selenium"""
    if not SELENIUM_AVAILABLE:
        print("❌ Selenium غير متوفر للاختبار")
        return False
    
    test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
    
    print(f"🧪 اختبار Selenium مع: {test_url}")
    
    result = scrape_mcpedl_with_selenium(test_url)
    
    if result:
        print("✅ نجح اختبار Selenium!")
        print(f"اسم المود: {result.get('name', 'غير متوفر')}")
        return True
    else:
        print("❌ فشل اختبار Selenium")
        return False

if __name__ == "__main__":
    test_selenium_scraper()
