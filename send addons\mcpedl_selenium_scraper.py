# -*- coding: utf-8 -*-
"""
مستخرج MCPEDL باستخدام Selenium للمواقع التي تعتمد على JavaScript
"""

import time
import re
from typing import Dict, List, Optional, Any
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse

# محاولة استيراد selenium
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, WebDriverException
    SELENIUM_AVAILABLE = True
    print("Selenium متوفر - يمكن استخدامه للمواقع المعقدة")
except ImportError:
    SELENIUM_AVAILABLE = False
    print("Selenium غير متوفر - سيتم استخدام الطريقة العادية فقط")

class MCPEDLSeleniumScraper:
    """مستخرج MCPEDL باستخدام Selenium"""

    def __init__(self):
        self.driver = None

    def setup_driver(self):
        """إعداد متصفح Chrome"""
        if not SELENIUM_AVAILABLE:
            return False

        try:
            chrome_options = Options()

            # إعدادات لتجاوز الكشف
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # إعدادات إضافية
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-images')  # تسريع التحميل

            # تشغيل في الخلفية (اختياري)
            # chrome_options.add_argument('--headless')

            self.driver = webdriver.Chrome(options=chrome_options)

            # إخفاء خصائص webdriver
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            print("تم إعداد متصفح Chrome بنجاح")
            return True

        except Exception as e:
            print(f"فشل في إعداد متصفح Chrome: {e}")
            return False

    def fetch_page_with_selenium(self, url: str) -> Optional[BeautifulSoup]:
        """جلب الصفحة باستخدام Selenium"""
        if not self.driver:
            if not self.setup_driver():
                return None

        try:
            print(f"جلب الصفحة باستخدام Selenium: {url}")

            # الانتقال إلى الصفحة
            self.driver.get(url)

            # انتظار تحميل العنوان
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "title"))
            )

            # انتظار إضافي لتحميل المحتوى
            time.sleep(5)

            # انتظار تحميل المحتوى الأساسي
            try:
                WebDriverWait(self.driver, 20).until(
                    EC.any_of(
                        EC.presence_of_element_located((By.TAG_NAME, "article")),
                        EC.presence_of_element_located((By.CLASS_NAME, "entry-content")),
                        EC.presence_of_element_located((By.CLASS_NAME, "post-content")),
                        EC.presence_of_element_located((By.TAG_NAME, "main")),
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".post")),
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".content"))
                    )
                )
                print("تم تحميل المحتوى الأساسي")
            except TimeoutException:
                print("انتهت مهلة انتظار المحتوى الأساسي - المتابعة بالمحتوى الحالي")

            # انتظار إضافي لتحميل الصور
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "img"))
                )
                print("تم تحميل الصور")
            except TimeoutException:
                print("انتهت مهلة انتظار الصور")

            # التمرير لأسفل لتحميل المحتوى الديناميكي
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
            time.sleep(2)
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(2)

            # الحصول على HTML
            html_content = self.driver.page_source

            # حفظ HTML للتشخيص
            try:
                with open('debug_selenium_page.html', 'w', encoding='utf-8') as f:
                    f.write(html_content)
                print("تم حفظ HTML من Selenium للتشخيص")
            except:
                pass

            # تحليل HTML
            soup = BeautifulSoup(html_content, 'html.parser')

            # التحقق من جودة المحتوى
            page_text = soup.get_text()
            if len(page_text.strip()) < 1000:
                print(f"محتوى قليل من Selenium ({len(page_text.strip())} حرف)")
                return None

            print("تم جلب الصفحة بنجاح باستخدام Selenium")
            return soup

        except TimeoutException:
            print("انتهت مهلة تحميل الصفحة")
            return None
        except WebDriverException as e:
            print(f"خطأ في WebDriver: {e}")
            return None
        except Exception as e:
            print(f"خطأ عام في Selenium: {e}")
            return None

    def close(self):
        """إغلاق المتصفح"""
        if self.driver:
            try:
                self.driver.quit()
                print("تم إغلاق متصفح Selenium")
            except:
                pass
            self.driver = None

class EnhancedMCPEDLExtractor:
    """مستخرج محسن لبيانات MCPEDL من HTML المحمل بـ Selenium"""

    def clean_text(self, text: str) -> str:
        """تنظيف النص"""
        if not text:
            return ""
        text = re.sub(r'\s+', ' ', text.strip())
        return text.strip()

    def extract_title(self, soup: BeautifulSoup) -> str:
        """استخراج عنوان المود"""
        # محاولة عدة طرق لاستخراج العنوان
        selectors = [
            'h1.entry-title',
            'h1.post-title',
            '.post-header h1',
            '.entry-header h1',
            'h1',
            '.page-title',
            'title'
        ]

        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                title = self.clean_text(element.get_text())
                if title and len(title) > 3:
                    # تنظيف العنوان من كلمات غير مرغوبة
                    title = title.replace(' - MCPEDL', '').replace(' | MCPEDL', '')
                    title = title.replace('MCPEDL', '').strip()

                    # اختصار اسم المود
                    title = self.shorten_mod_name(title)
                    return title

        return ""

    def shorten_mod_name(self, title: str) -> str:
        """اختصار اسم المود"""
        # قائمة الكلمات التي يجب إزالتها للاختصار
        words_to_remove = [
            ': Community Edition',
            ' Community Edition',
            ': Extended',
            ' Extended',
            ': Remastered',
            ' Remastered',
            ': Ultimate',
            ' Ultimate',
            ': Enhanced',
            ' Enhanced',
            ': Plus',
            ' Plus',
            ': Pro',
            ' Pro',
            ': Advanced',
            ' Advanced',
            ': Deluxe',
            ' Deluxe',
            ': Complete',
            ' Complete',
            ': Special Edition',
            ' Special Edition'
        ]

        shortened_title = title
        for word in words_to_remove:
            if word in shortened_title:
                shortened_title = shortened_title.replace(word, '')
                break  # إزالة أول تطابق فقط

        return shortened_title.strip()

    def extract_description(self, soup: BeautifulSoup) -> str:
        """استخراج وصف مفصل للمود"""
        description_parts = []

        # البحث عن المحتوى الرئيسي
        content_areas = soup.find_all(['div', 'article'], class_=['entry-content', 'post-content', 'content', 'post-body'])

        for area in content_areas:
            # استخراج الفقرات
            paragraphs = area.find_all('p')
            for p in paragraphs[:10]:  # أول 10 فقرات
                text = self.clean_text(p.get_text())

                # تجاهل النصوص غير المفيدة
                skip_phrases = [
                    'visit dragon mounts 2',
                    'join our discord',
                    'were mostly active there',
                    'download',
                    'click here',
                    'advertisement',
                    'ads by',
                    'sponsored'
                ]

                if text and len(text) > 20:
                    if not any(phrase in text.lower() for phrase in skip_phrases):
                        description_parts.append(text)

            # استخراج القوائم (features)
            lists = area.find_all(['ul', 'ol'])
            for ul in lists:
                items = ul.find_all('li')
                if len(items) > 1:  # قائمة حقيقية
                    list_text = "Features: " + ", ".join([self.clean_text(li.get_text()) for li in items[:5]])
                    if len(list_text) > 20:
                        description_parts.append(list_text)

        # دمج الوصف
        description = ' '.join(description_parts[:5])  # أول 5 أجزاء

        if description and len(description) > 50:
            return description[:1500]  # حد أقصى

        return "A Minecraft addon that adds new features and content to enhance your gameplay experience."

    def extract_category(self, soup: BeautifulSoup) -> str:
        """استخراج فئة المود"""
        # البحث في breadcrumbs
        breadcrumbs = soup.select('.breadcrumbs a, .breadcrumb a, nav a')
        for crumb in breadcrumbs:
            text = self.clean_text(crumb.get_text()).lower()
            if text in ['addons', 'mods', 'behavior']:
                return 'Addons'
            elif text in ['shaders', 'shader']:
                return 'Shaders'
            elif text in ['texture', 'resource', 'pack']:
                return 'Texture Pack'

        # البحث في العنوان أو المحتوى
        page_text = soup.get_text().lower()
        if 'shader' in page_text:
            return 'Shaders'
        elif 'texture' in page_text or 'resource pack' in page_text:
            return 'Texture Pack'
        else:
            return 'Addons'

    def extract_images(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """استخراج روابط الصور"""
        image_urls = []

        print("🔍 بدء استخراج الصور...")

        # البحث عن الصور في المحتوى الرئيسي أولاً
        content_selectors = [
            '.entry-content',
            '.post-content',
            '.content',
            'article',
            '.post-body',
            '.main-content'
        ]

        for selector in content_selectors:
            content_areas = soup.select(selector)
            print(f"البحث في {selector}: وجد {len(content_areas)} منطقة")

            for area in content_areas:
                images = area.find_all('img')
                print(f"وجد {len(images)} صورة في {selector}")

                for img in images:
                    # محاولة الحصول على رابط الصورة من عدة مصادر
                    src = (img.get('src') or
                          img.get('data-src') or
                          img.get('data-lazy-src') or
                          img.get('data-original') or
                          img.get('data-srcset', '').split(',')[0].strip().split(' ')[0])

                    if src:
                        # تحويل إلى رابط كامل
                        if src.startswith('//'):
                            src = 'https:' + src
                        elif src.startswith('/'):
                            src = urljoin(base_url, src)

                        print(f"فحص صورة: {src[:80]}...")

                        # التحقق من صحة الرابط
                        if self.is_valid_image_url(src) and src not in image_urls:
                            image_urls.append(src)
                            print(f"✅ صورة صالحة مضافة: {len(image_urls)}")
                        else:
                            print(f"❌ صورة مرفوضة")

        # إذا لم نجد صور كافية، ابحث في كامل الصفحة مع فلترة أقوى
        if len(image_urls) < 5:
            print("🔍 البحث في كامل الصفحة...")
            all_images = soup.find_all('img')
            print(f"وجد {len(all_images)} صورة في كامل الصفحة")

            for img in all_images:
                src = (img.get('src') or
                      img.get('data-src') or
                      img.get('data-lazy-src') or
                      img.get('data-original'))

                if src:
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = urljoin(base_url, src)

                    if self.is_valid_image_url(src) and src not in image_urls:
                        image_urls.append(src)
                        print(f"✅ صورة إضافية: {len(image_urls)}")

                        if len(image_urls) >= 15:  # حد أقصى أعلى
                            break

        print(f"📊 إجمالي الصور المستخرجة: {len(image_urls)}")
        return image_urls[:15]  # أول 15 صورة

    def is_valid_image_url(self, url: str) -> bool:
        """التحقق من صحة رابط الصورة"""
        if not url or len(url) < 10:
            return False

        # قائمة شاملة للصور الثابتة والغير مرغوبة
        site_static_images = [
            'shield.png',
            'shield.6982c20.png',
            '_nuxt',
            'logo',
            'icon',
            'avatar',
            'thumb',
            '16x16',
            '32x32',
            '64x64',
            'favicon',
            'header',
            'footer',
            'sidebar',
            'banner',
            'ad',
            'advertisement',
            'social',
            'share',
            'button',
            'widget',
            'placeholder',
            'loading',
            'spinner'
        ]

        url_lower = url.lower()

        # فحص الصور الثابتة
        if any(static_img in url_lower for static_img in site_static_images):
            print(f"❌ صورة ثابتة: {url[:50]}...")
            return False

        # تجاهل صور التعليقات والمستخدمين
        if '/users/' in url_lower:
            print(f"❌ صورة مستخدم: {url[:50]}...")
            return False

        # تجاهل الصور الصغيرة
        small_sizes = ['50x50', '64x64', '80x80', '100x100', '120x120', '150x150']
        if any(size in url_lower for size in small_sizes):
            print(f"❌ صورة صغيرة: {url[:50]}...")
            return False

        # تجاهل أيقونات SVG المشفرة
        if url.startswith('data:image/svg+xml') or url.startswith('data:image/png;base64'):
            print(f"❌ صورة مشفرة: {url[:50]}...")
            return False

        # تجاهل روابط غير مكتملة
        if not url.startswith('http'):
            print(f"❌ رابط غير مكتمل: {url[:50]}...")
            return False

        # التحقق من امتداد الصورة
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
        has_valid_extension = any(ext in url_lower for ext in image_extensions)

        if not has_valid_extension:
            print(f"❌ امتداد غير صالح: {url[:50]}...")
            return False

        # التأكد من أن الصورة من مصادر موثوقة
        trusted_domains = ['mcpedl.com', 'r2.mcpedl.com']
        is_trusted_domain = any(domain in url_lower for domain in trusted_domains)

        # مسارات المحتوى الصالحة
        valid_paths = [
            '/content/',
            '/uploads/',
            '/images/',
            '/media/',
            '/wp-content/',
            '/assets/',
            '/files/',
            '/storage/',
            '/static/'
        ]
        has_valid_path = any(path in url_lower for path in valid_paths)

        # قبول الصورة إذا كانت من نطاق موثوق أو مسار صالح
        if is_trusted_domain and has_valid_path:
            print(f"✅ صورة صالحة: {url[:50]}...")
            return True
        elif is_trusted_domain and not any(bad in url_lower for bad in ['_nuxt', 'shield', '/users/']):
            # قبول الصور من mcpedl.com إذا لم تكن من المجلدات المحظورة
            print(f"✅ صورة من نطاق موثوق: {url[:50]}...")
            return True
        else:
            print(f"❌ مصدر غير موثوق: {url[:50]}...")
            return False

    def extract_versions(self, soup: BeautifulSoup) -> str:
        """استخراج إصدارات Minecraft"""
        page_text = soup.get_text()

        # البحث عن أرقام الإصدارات
        version_pattern = r'1\.\d+(?:\.\d+)*'
        versions = re.findall(version_pattern, page_text)

        if versions:
            unique_versions = sorted(list(set(versions)), reverse=True)
            return ', '.join(unique_versions[:3])

        return "1.20+"

    def extract_download_info(self, soup: BeautifulSoup, base_url: str) -> Dict[str, Any]:
        """استخراج معلومات التحميل"""
        download_info = {'download_url': '', 'size': ''}

        # البحث عن رابط التحميل
        download_links = soup.find_all('a', href=True)
        for link in download_links:
            href = link.get('href', '').lower()
            text = link.get_text().lower()

            if any(word in href for word in ['download', 'mediafire', 'drive.google', 'dropbox']):
                download_info['download_url'] = link['href']
                break
            elif any(word in text for word in ['download', 'تحميل']):
                download_info['download_url'] = link['href']
                break

        # البحث عن حجم الملف
        size_pattern = r'(\d+(?:\.\d+)?)\s*(KB|MB|GB|kb|mb|gb)'
        size_match = re.search(size_pattern, soup.get_text())
        if size_match:
            download_info['size'] = f"{size_match.group(1)} {size_match.group(2).upper()}"

        return download_info

    def extract_creator_info(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """استخراج معلومات المطور"""
        creator_info = {
            'creator_name': '',
            'creator_contact_info': '',
            'creator_social_channels': []
        }

        # البحث عن اسم المطور بطرق متعددة
        creator_name = self.extract_creator_name(soup)
        creator_info['creator_name'] = creator_name

        # البحث عن روابط التواصل الاجتماعي الحقيقية
        social_channels = self.extract_real_social_links(soup)
        creator_info['creator_social_channels'] = social_channels

        return creator_info

    def extract_creator_name(self, soup: BeautifulSoup) -> str:
        """استخراج اسم المطور فقط"""
        # البحث في عدة أماكن
        selectors = [
            '.author-name',
            '.post-author',
            '.by-author',
            '.creator',
            '.author',
            '[class*="author"]'
        ]

        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                text = self.clean_text(element.get_text())

                # تنظيف اسم المطور من النصوص الإضافية
                if text:
                    # إزالة النصوص الشائعة
                    text = text.replace('By ', '').replace('by ', '')
                    text = text.replace('Author: ', '').replace('author: ', '')
                    text = text.replace('Created by ', '').replace('created by ', '')

                    # إزالة التواريخ والنصوص الإضافية
                    unwanted_phrases = [
                        'Published on',
                        'Updated on',
                        'CurseForge',
                        'March',
                        'May',
                        'April',
                        'June',
                        'July',
                        'August',
                        'September',
                        'October',
                        'November',
                        'December',
                        '2024',
                        '2025',
                        '(',
                        ')'
                    ]

                    for phrase in unwanted_phrases:
                        if phrase in text:
                            # أخذ الجزء قبل العبارة غير المرغوبة
                            text = text.split(phrase)[0].strip()

                    # التأكد من أن النص ليس طويل جداً (اسم المطور يجب أن يكون قصير)
                    if text and len(text) < 50 and not any(char.isdigit() for char in text):
                        return text

        # البحث في النص العام للصفحة
        page_text = soup.get_text()

        # البحث عن نمط "By [Name]"
        by_pattern = r'By\s+([A-Za-z0-9_\s]+?)(?:\s|$|Published|Updated|on)'
        match = re.search(by_pattern, page_text)
        if match:
            name = match.group(1).strip()
            if len(name) < 30:
                return name

        return ""

    def extract_real_social_links(self, soup: BeautifulSoup) -> List[str]:
        """استخراج روابط التواصل الاجتماعي الحقيقية فقط"""
        social_channels = []

        print("🔍 بدء استخراج روابط التواصل الاجتماعي...")

        # البحث عن الروابط في مناطق محددة أولاً
        social_areas = soup.find_all(['div', 'section'], class_=['social', 'author', 'contact', 'links'])

        all_links = []

        # جمع الروابط من المناطق المخصصة
        for area in social_areas:
            links = area.find_all('a', href=True)
            all_links.extend(links)
            print(f"وجد {len(links)} رابط في منطقة اجتماعية")

        # إذا لم نجد روابط كافية، ابحث في كامل الصفحة
        if len(all_links) < 5:
            all_links.extend(soup.find_all('a', href=True))
            print(f"إجمالي الروابط في الصفحة: {len(all_links)}")

        for link in all_links:
            href = link.get('href', '').strip()

            if not href:
                continue

            print(f"فحص رابط: {href[:60]}...")

            # تجاهل الروابط المشفرة أو الثابتة
            if (href.startswith('data:') or
                href.startswith('#') or
                href.startswith('javascript:') or
                not href.startswith('http')):
                print(f"❌ رابط مشفر أو ثابت")
                continue

            # تجاهل الروابط الداخلية للموقع
            if 'mcpedl.com' in href.lower():
                print(f"❌ رابط داخلي للموقع")
                continue

            href_lower = href.lower()

            # البحث عن منصات التواصل الحقيقية
            social_platforms = {
                'youtube.com/channel/': 'YouTube',
                'youtube.com/c/': 'YouTube',
                'youtube.com/user/': 'YouTube',
                'youtube.com/@': 'YouTube',
                'youtu.be/': 'YouTube',
                'discord.gg/': 'Discord',
                'discord.com/invite/': 'Discord',
                'twitter.com/': 'Twitter',
                'x.com/': 'Twitter',
                'instagram.com/': 'Instagram',
                'facebook.com/': 'Facebook',
                'tiktok.com/@': 'TikTok',
                'github.com/': 'GitHub',
                'twitch.tv/': 'Twitch',
                'reddit.com/u/': 'Reddit',
                'reddit.com/user/': 'Reddit'
            }

            found_platform = False
            for platform_pattern, platform_name in social_platforms.items():
                if platform_pattern in href_lower:
                    # التأكد من أن الرابط ليس مجرد الصفحة الرئيسية
                    if len(href) > len(f"https://{platform_pattern}") + 3:
                        channel_info = f"{platform_name}: {href}"
                        if channel_info not in social_channels:
                            social_channels.append(channel_info)
                            print(f"✅ رابط تواصل صالح: {platform_name}")
                        found_platform = True
                        break

            if not found_platform:
                print(f"❌ منصة غير مدعومة")

        print(f"📊 إجمالي روابط التواصل المستخرجة: {len(social_channels)}")

        # إزالة التكرارات وترتيب حسب الأهمية
        unique_channels = list(set(social_channels))

        # ترتيب حسب الأهمية (YouTube أولاً، ثم Discord، إلخ)
        priority_order = ['YouTube', 'Discord', 'Twitter', 'Instagram', 'GitHub', 'TikTok', 'Facebook', 'Twitch', 'Reddit']
        sorted_channels = []

        for priority in priority_order:
            for channel in unique_channels:
                if channel.startswith(priority):
                    sorted_channels.append(channel)

        # إضافة أي قنوات أخرى لم تكن في قائمة الأولوية
        for channel in unique_channels:
            if channel not in sorted_channels:
                sorted_channels.append(channel)

        return sorted_channels[:5]  # أول 5 قنوات

    def get_social_platform(self, url: str) -> str:
        """تحديد منصة التواصل الاجتماعي"""
        url_lower = url.lower()
        if 'youtube' in url_lower:
            return 'YouTube'
        elif 'discord' in url_lower:
            return 'Discord'
        elif 'twitter' in url_lower:
            return 'Twitter'
        elif 'instagram' in url_lower:
            return 'Instagram'
        else:
            return 'Other'

    def create_custom_description(self, mod_data: Dict[str, Any], soup: BeautifulSoup) -> Dict[str, str]:
        """إنشاء وصف مخصص للمود بالعربية والإنجليزية"""
        mod_name = mod_data.get('name', 'Unknown Mod')
        category = mod_data.get('category', 'Addon')

        # استخراج الميزات والمحتوى من الصفحة
        features = self.extract_features(soup)
        page_content = self.extract_page_content(soup)

        # محاولة استخدام Gemini لإنشاء وصف ذكي
        ai_descriptions = self.generate_ai_description(mod_name, category, features, page_content)

        if ai_descriptions:
            return ai_descriptions

        # إذا فشل Gemini، استخدم الطريقة التقليدية
        english_desc = self.create_english_description(mod_name, category, features)
        arabic_desc = self.create_arabic_description(mod_name, category, features)

        return {
            'english': english_desc,
            'arabic': arabic_desc
        }

    def extract_page_content(self, soup: BeautifulSoup) -> str:
        """استخراج المحتوى الرئيسي للصفحة"""
        content_parts = []

        # البحث عن المحتوى الرئيسي
        content_areas = soup.find_all(['div', 'article'], class_=['entry-content', 'post-content', 'content', 'post-body'])

        for area in content_areas:
            # استخراج الفقرات
            paragraphs = area.find_all('p')
            for p in paragraphs[:5]:  # أول 5 فقرات
                text = self.clean_text(p.get_text())
                if text and len(text) > 30:
                    # تجاهل النصوص غير المفيدة
                    skip_phrases = [
                        'visit dragon mounts 2',
                        'join our discord',
                        'download',
                        'click here',
                        'advertisement'
                    ]

                    if not any(phrase in text.lower() for phrase in skip_phrases):
                        content_parts.append(text)

        return ' '.join(content_parts[:3])  # أول 3 فقرات مفيدة

    def generate_ai_description(self, mod_name: str, category: str, features: List[str], content: str) -> Dict[str, str]:
        """إنشاء وصف باستخدام Gemini AI"""
        try:
            # محاولة استيراد Gemini من الأداة الرئيسية
            import sys
            import os

            # إضافة مسار الأداة الرئيسية
            main_path = os.path.dirname(os.path.abspath(__file__))
            sys.path.insert(0, main_path)

            # محاولة استيراد دالة Gemini
            try:
                from mod_processor import generate_ai_description_gemini

                # إعداد البيانات للـ AI
                mod_info = {
                    'name': mod_name,
                    'category': category,
                    'features': features,
                    'content': content
                }

                # إنشاء prompt للـ AI
                prompt = self.create_ai_prompt(mod_info)

                # استدعاء Gemini
                ai_response = generate_ai_description_gemini(prompt)

                if ai_response and len(ai_response) > 100:
                    # تحليل الاستجابة لفصل العربي عن الإنجليزي
                    return self.parse_ai_response(ai_response)

            except ImportError:
                print("⚠️ لا يمكن استيراد دالة Gemini")
            except Exception as e:
                print(f"⚠️ خطأ في Gemini: {e}")

        except Exception as e:
            print(f"⚠️ خطأ عام في AI: {e}")

        return None

    def parse_ai_response(self, ai_response: str) -> Dict[str, str]:
        """تحليل استجابة AI لفصل الوصف العربي عن الإنجليزي"""
        try:
            # تقسيم الاستجابة إلى أجزاء
            parts = ai_response.split('\n\n')

            english_desc = ""
            arabic_desc = ""

            for part in parts:
                part = part.strip()
                if not part:
                    continue

                # التحقق من وجود نص عربي
                has_arabic = any(ord(char) > 1536 and ord(char) < 1791 for char in part)

                if has_arabic:
                    arabic_desc = part
                elif len(part) > 50:  # نص إنجليزي طويل
                    english_desc = part

            # إذا لم نجد فصل واضح، نحاول طريقة أخرى
            if not english_desc or not arabic_desc:
                # البحث عن علامات فصل
                if '[English]' in ai_response and '[Arabic]' in ai_response:
                    english_start = ai_response.find('[English]')
                    arabic_start = ai_response.find('[Arabic]')

                    if english_start != -1 and arabic_start != -1:
                        if english_start < arabic_start:
                            english_desc = ai_response[english_start+9:arabic_start].strip()
                            arabic_desc = ai_response[arabic_start+8:].strip()
                        else:
                            arabic_desc = ai_response[arabic_start+8:english_start].strip()
                            english_desc = ai_response[english_start+9:].strip()

                # إذا لم نجد علامات، نقسم بناءً على الأسطر
                elif not english_desc or not arabic_desc:
                    lines = ai_response.split('\n')
                    current_desc = ""

                    for line in lines:
                        line = line.strip()
                        if not line:
                            continue

                        has_arabic = any(ord(char) > 1536 and ord(char) < 1791 for char in line)

                        if has_arabic and not arabic_desc:
                            arabic_desc = current_desc + " " + line if current_desc else line
                            current_desc = ""
                        elif not has_arabic and not english_desc:
                            current_desc += " " + line if current_desc else line

                    if current_desc and not english_desc:
                        english_desc = current_desc

            # التأكد من وجود محتوى
            if not english_desc:
                english_desc = "A comprehensive Minecraft addon that enhances your gameplay experience with new features and content."

            if not arabic_desc:
                arabic_desc = "إضافة شاملة لماين كرافت تحسن تجربة اللعب بميزات ومحتوى جديد."

            return {
                'english': english_desc.strip(),
                'arabic': arabic_desc.strip()
            }

        except Exception as e:
            print(f"خطأ في تحليل استجابة AI: {e}")
            return None

    def create_ai_prompt(self, mod_info: Dict[str, Any]) -> str:
        """إنشاء prompt للـ AI"""
        mod_name = mod_info.get('name', '')
        category = mod_info.get('category', '')
        features = mod_info.get('features', [])
        content = mod_info.get('content', '')

        prompt = f"""
Create a detailed and engaging description for a Minecraft {category.lower()} called "{mod_name}".

Available information:
- Mod Name: {mod_name}
- Category: {category}
- Features: {', '.join(features[:5]) if features else 'Not specified'}
- Content: {content[:500] if content else 'Limited information available'}

Please create TWO separate descriptions:

1. ENGLISH DESCRIPTION (100-150 words):
Write an engaging description that explains what the mod does, its key features, and why players would want to use it. Focus on the actual features and gameplay elements. Avoid generic phrases like "enhance your gameplay experience". Make it sound exciting and specific to this mod.

2. ARABIC DESCRIPTION (100-150 words):
Write a complete Arabic description that covers the same points as the English version. Make it natural and engaging in Arabic, not just a direct translation.

Format your response EXACTLY like this:

[English]
[Your English description here]

[Arabic]
[Your Arabic description here]

Make sure to use the exact format with [English] and [Arabic] labels so I can separate them properly.
"""

        return prompt

    def extract_features(self, soup: BeautifulSoup) -> List[str]:
        """استخراج ميزات المود من الصفحة"""
        features = []

        # البحث في القوائم
        lists = soup.find_all(['ul', 'ol'])
        for ul in lists:
            items = ul.find_all('li')
            for li in items:
                text = self.clean_text(li.get_text())
                if text and len(text) > 10 and len(text) < 100:
                    # تجاهل النصوص غير المفيدة
                    skip_words = ['download', 'discord', 'visit', 'click', 'link']
                    if not any(word in text.lower() for word in skip_words):
                        features.append(text)

        # البحث في الفقرات للميزات
        paragraphs = soup.find_all('p')
        for p in paragraphs:
            text = self.clean_text(p.get_text())
            # البحث عن جمل تصف الميزات
            if any(keyword in text.lower() for keyword in ['adds', 'includes', 'features', 'new', 'custom']):
                if len(text) > 20 and len(text) < 200:
                    features.append(text)

        return features[:5]  # أول 5 ميزات

    def create_english_description(self, mod_name: str, category: str, features: List[str]) -> str:
        """إنشاء وصف إنجليزي"""
        # تحديد نوع المحتوى
        content_type = "addon" if category == "Addons" else category.lower()

        # بداية الوصف
        intro = f"{mod_name} is an innovative Minecraft {content_type} that enhances your gaming experience with exciting new content."

        # إضافة الميزات
        features_text = ""
        if features:
            features_text = " This mod includes: " + ", ".join(features[:3]) + "."

        # خاتمة
        outro = f" Perfect for players who want to expand their Minecraft world with unique {content_type} features."

        return intro + features_text + outro

    def create_arabic_description(self, mod_name: str, category: str, features: List[str]) -> str:
        """إنشاء وصف عربي"""
        # تحديد نوع المحتوى بالعربية
        if category == "Addons":
            content_type = "إضافة"
        elif category == "Shaders":
            content_type = "شيدر"
        elif category == "Texture Pack":
            content_type = "حزمة نسيج"
        else:
            content_type = "مود"

        # بداية الوصف
        intro = f"{mod_name} هو {content_type} مبتكر لماين كرافت يحسن تجربة اللعب بمحتوى جديد ومثير."

        # إضافة الميزات
        features_text = ""
        if features:
            features_text = f" يتضمن هذا ال{content_type}: " + "، ".join(features[:3]) + "."

        # خاتمة
        outro = f" مثالي للاعبين الذين يريدون توسيع عالم ماين كرافت بميزات {content_type} فريدة."

        return intro + features_text + outro

def scrape_mcpedl_with_selenium(url: str) -> Optional[Dict[str, Any]]:
    """استخراج بيانات MCPEDL باستخدام Selenium"""
    if not SELENIUM_AVAILABLE:
        print("Selenium غير متوفر")
        return None

    scraper = MCPEDLSeleniumScraper()

    try:
        # جلب الصفحة
        soup = scraper.fetch_page_with_selenium(url)

        if not soup:
            return None

        # استخراج البيانات باستخدام محسن خاص بـ Selenium
        enhanced_scraper = EnhancedMCPEDLExtractor()

        mod_data = {
            'name': enhanced_scraper.extract_title(soup),
            'description': enhanced_scraper.extract_description(soup),
            'category': enhanced_scraper.extract_category(soup),
            'image_urls': enhanced_scraper.extract_images(soup, url),
            'version': enhanced_scraper.extract_versions(soup),
            'source_url': url
        }

        # معلومات التحميل
        download_info = enhanced_scraper.extract_download_info(soup, url)
        mod_data.update(download_info)

        # معلومات المطور
        creator_info = enhanced_scraper.extract_creator_info(soup)
        mod_data.update(creator_info)

        # التحقق من البيانات الأساسية
        if not mod_data['name']:
            print("لم يتم العثور على اسم المود")
            return None

        # تحسين الوصف دائماً باستخدام Gemini
        print("🤖 تحسين الوصف باستخدام Gemini...")
        custom_descriptions = enhanced_scraper.create_custom_description(mod_data, soup)

        if custom_descriptions and isinstance(custom_descriptions, dict):
            # إضافة الوصف الإنجليزي
            if 'english' in custom_descriptions and custom_descriptions['english']:
                mod_data['description'] = custom_descriptions['english']
                print("✅ تم إضافة الوصف الإنجليزي")

            # إضافة الوصف العربي
            if 'arabic' in custom_descriptions and custom_descriptions['arabic']:
                mod_data['description_arabic'] = custom_descriptions['arabic']
                print("✅ تم إضافة الوصف العربي")

            print("✅ تم تحسين الوصف باستخدام Gemini")
        else:
            print("⚠️ فشل تحسين الوصف، استخدام الوصف الأصلي")

        print(f"تم استخراج بيانات المود بنجاح باستخدام Selenium: {mod_data['name']}")
        print(f"عدد الصور المستخرجة: {len(mod_data.get('image_urls', []))}")
        print(f"طول الوصف: {len(mod_data.get('description', ''))} حرف")

        return mod_data

    finally:
        scraper.close()

# دالة للاختبار
def test_selenium_scraper():
    """اختبار مستخرج Selenium"""
    if not SELENIUM_AVAILABLE:
        print("❌ Selenium غير متوفر للاختبار")
        return False

    test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"

    print(f"🧪 اختبار Selenium مع: {test_url}")

    result = scrape_mcpedl_with_selenium(test_url)

    if result:
        print("✅ نجح اختبار Selenium!")
        print(f"اسم المود: {result.get('name', 'غير متوفر')}")
        return True
    else:
        print("❌ فشل اختبار Selenium")
        return False

if __name__ == "__main__":
    test_selenium_scraper()
