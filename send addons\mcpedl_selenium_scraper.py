# -*- coding: utf-8 -*-
"""
مستخرج MCPEDL باستخدام Selenium للمواقع التي تعتمد على JavaScript
"""

import time
import re
from typing import Dict, List, Optional, Any
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse

# محاولة استيراد selenium
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, WebDriverException
    SELENIUM_AVAILABLE = True
    print("Selenium متوفر - يمكن استخدامه للمواقع المعقدة")
except ImportError:
    SELENIUM_AVAILABLE = False
    print("Selenium غير متوفر - سيتم استخدام الطريقة العادية فقط")

class MCPEDLSeleniumScraper:
    """مستخرج MCPEDL باستخدام Selenium"""

    def __init__(self):
        self.driver = None

    def setup_driver(self):
        """إعداد متصفح Chrome"""
        if not SELENIUM_AVAILABLE:
            return False

        try:
            chrome_options = Options()

            # إعدادات لتجاوز الكشف
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # إعدادات إضافية
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-images')  # تسريع التحميل

            # تشغيل في الخلفية (اختياري)
            # chrome_options.add_argument('--headless')

            self.driver = webdriver.Chrome(options=chrome_options)

            # إخفاء خصائص webdriver
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            print("تم إعداد متصفح Chrome بنجاح")
            return True

        except Exception as e:
            print(f"فشل في إعداد متصفح Chrome: {e}")
            return False

    def fetch_page_with_selenium(self, url: str) -> Optional[BeautifulSoup]:
        """جلب الصفحة باستخدام Selenium"""
        if not self.driver:
            if not self.setup_driver():
                return None

        try:
            print(f"جلب الصفحة باستخدام Selenium: {url}")

            # الانتقال إلى الصفحة
            self.driver.get(url)

            # انتظار تحميل العنوان
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "title"))
            )

            # انتظار إضافي لتحميل المحتوى
            time.sleep(5)

            # انتظار تحميل المحتوى الأساسي
            try:
                WebDriverWait(self.driver, 20).until(
                    EC.any_of(
                        EC.presence_of_element_located((By.TAG_NAME, "article")),
                        EC.presence_of_element_located((By.CLASS_NAME, "entry-content")),
                        EC.presence_of_element_located((By.CLASS_NAME, "post-content")),
                        EC.presence_of_element_located((By.TAG_NAME, "main")),
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".post")),
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".content"))
                    )
                )
                print("تم تحميل المحتوى الأساسي")
            except TimeoutException:
                print("انتهت مهلة انتظار المحتوى الأساسي - المتابعة بالمحتوى الحالي")

            # انتظار إضافي لتحميل الصور
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "img"))
                )
                print("تم تحميل الصور")
            except TimeoutException:
                print("انتهت مهلة انتظار الصور")

            # التمرير لأسفل لتحميل المحتوى الديناميكي
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
            time.sleep(2)
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(2)

            # الحصول على HTML
            html_content = self.driver.page_source

            # حفظ HTML للتشخيص
            try:
                with open('debug_selenium_page.html', 'w', encoding='utf-8') as f:
                    f.write(html_content)
                print("تم حفظ HTML من Selenium للتشخيص")
            except:
                pass

            # تحليل HTML
            soup = BeautifulSoup(html_content, 'html.parser')

            # التحقق من جودة المحتوى
            page_text = soup.get_text()
            if len(page_text.strip()) < 1000:
                print(f"محتوى قليل من Selenium ({len(page_text.strip())} حرف)")
                return None

            print("تم جلب الصفحة بنجاح باستخدام Selenium")
            return soup

        except TimeoutException:
            print("انتهت مهلة تحميل الصفحة")
            return None
        except WebDriverException as e:
            print(f"خطأ في WebDriver: {e}")
            return None
        except Exception as e:
            print(f"خطأ عام في Selenium: {e}")
            return None

    def close(self):
        """إغلاق المتصفح"""
        if self.driver:
            try:
                self.driver.quit()
                print("تم إغلاق متصفح Selenium")
            except:
                pass
            self.driver = None

class EnhancedMCPEDLExtractor:
    """مستخرج محسن لبيانات MCPEDL من HTML المحمل بـ Selenium"""

    def clean_text(self, text: str) -> str:
        """تنظيف النص"""
        if not text:
            return ""
        text = re.sub(r'\s+', ' ', text.strip())
        return text.strip()

    def extract_title(self, soup: BeautifulSoup) -> str:
        """استخراج عنوان المود"""
        # محاولة عدة طرق لاستخراج العنوان
        selectors = [
            'h1.entry-title',
            'h1.post-title',
            '.post-header h1',
            '.entry-header h1',
            'h1',
            '.page-title',
            'title'
        ]

        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                title = self.clean_text(element.get_text())
                if title and len(title) > 3:
                    # تنظيف العنوان من كلمات غير مرغوبة
                    title = title.replace(' - MCPEDL', '').replace(' | MCPEDL', '')
                    title = title.replace('MCPEDL', '').strip()
                    return title

        return ""

    def extract_description(self, soup: BeautifulSoup) -> str:
        """استخراج وصف مفصل للمود"""
        description_parts = []

        # البحث عن المحتوى الرئيسي
        content_areas = soup.find_all(['div', 'article'], class_=['entry-content', 'post-content', 'content', 'post-body'])

        for area in content_areas:
            # استخراج الفقرات
            paragraphs = area.find_all('p')
            for p in paragraphs[:10]:  # أول 10 فقرات
                text = self.clean_text(p.get_text())

                # تجاهل النصوص غير المفيدة
                skip_phrases = [
                    'visit dragon mounts 2',
                    'join our discord',
                    'were mostly active there',
                    'download',
                    'click here',
                    'advertisement',
                    'ads by',
                    'sponsored'
                ]

                if text and len(text) > 20:
                    if not any(phrase in text.lower() for phrase in skip_phrases):
                        description_parts.append(text)

            # استخراج القوائم (features)
            lists = area.find_all(['ul', 'ol'])
            for ul in lists:
                items = ul.find_all('li')
                if len(items) > 1:  # قائمة حقيقية
                    list_text = "Features: " + ", ".join([self.clean_text(li.get_text()) for li in items[:5]])
                    if len(list_text) > 20:
                        description_parts.append(list_text)

        # دمج الوصف
        description = ' '.join(description_parts[:5])  # أول 5 أجزاء

        if description and len(description) > 50:
            return description[:1500]  # حد أقصى

        return "A Minecraft addon that adds new features and content to enhance your gameplay experience."

    def extract_category(self, soup: BeautifulSoup) -> str:
        """استخراج فئة المود"""
        # البحث في breadcrumbs
        breadcrumbs = soup.select('.breadcrumbs a, .breadcrumb a, nav a')
        for crumb in breadcrumbs:
            text = self.clean_text(crumb.get_text()).lower()
            if text in ['addons', 'mods', 'behavior']:
                return 'Addons'
            elif text in ['shaders', 'shader']:
                return 'Shaders'
            elif text in ['texture', 'resource', 'pack']:
                return 'Texture Pack'

        # البحث في العنوان أو المحتوى
        page_text = soup.get_text().lower()
        if 'shader' in page_text:
            return 'Shaders'
        elif 'texture' in page_text or 'resource pack' in page_text:
            return 'Texture Pack'
        else:
            return 'Addons'

    def extract_images(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """استخراج روابط الصور"""
        image_urls = []

        # البحث عن الصور في المحتوى
        content_areas = soup.find_all(['div', 'article'], class_=['entry-content', 'post-content', 'content'])

        for area in content_areas:
            images = area.find_all('img')
            for img in images:
                src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
                if src:
                    # تحويل إلى رابط كامل
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = urljoin(base_url, src)

                    # التحقق من صحة الرابط
                    if self.is_valid_image_url(src) and src not in image_urls:
                        image_urls.append(src)

        # إذا لم نجد صور في المحتوى، ابحث في كامل الصفحة
        if len(image_urls) < 3:
            all_images = soup.find_all('img')
            for img in all_images:
                src = img.get('src') or img.get('data-src')
                if src:
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = urljoin(base_url, src)

                    if self.is_valid_image_url(src) and src not in image_urls:
                        image_urls.append(src)
                        if len(image_urls) >= 10:  # حد أقصى
                            break

        return image_urls[:10]  # أول 10 صور

    def is_valid_image_url(self, url: str) -> bool:
        """التحقق من صحة رابط الصورة"""
        if not url or len(url) < 10:
            return False

        # تجاهل الصور الصغيرة أو الأيقونات
        if any(word in url.lower() for word in ['icon', 'logo', 'avatar', 'thumb', '16x16', '32x32']):
            return False

        # التحقق من امتداد الصورة
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
        return any(ext in url.lower() for ext in image_extensions)

    def extract_versions(self, soup: BeautifulSoup) -> str:
        """استخراج إصدارات Minecraft"""
        page_text = soup.get_text()

        # البحث عن أرقام الإصدارات
        version_pattern = r'1\.\d+(?:\.\d+)*'
        versions = re.findall(version_pattern, page_text)

        if versions:
            unique_versions = sorted(list(set(versions)), reverse=True)
            return ', '.join(unique_versions[:3])

        return "1.20+"

    def extract_download_info(self, soup: BeautifulSoup, base_url: str) -> Dict[str, Any]:
        """استخراج معلومات التحميل"""
        download_info = {'download_url': '', 'size': ''}

        # البحث عن رابط التحميل
        download_links = soup.find_all('a', href=True)
        for link in download_links:
            href = link.get('href', '').lower()
            text = link.get_text().lower()

            if any(word in href for word in ['download', 'mediafire', 'drive.google', 'dropbox']):
                download_info['download_url'] = link['href']
                break
            elif any(word in text for word in ['download', 'تحميل']):
                download_info['download_url'] = link['href']
                break

        # البحث عن حجم الملف
        size_pattern = r'(\d+(?:\.\d+)?)\s*(KB|MB|GB|kb|mb|gb)'
        size_match = re.search(size_pattern, soup.get_text())
        if size_match:
            download_info['size'] = f"{size_match.group(1)} {size_match.group(2).upper()}"

        return download_info

    def extract_creator_info(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """استخراج معلومات المطور"""
        creator_info = {
            'creator_name': '',
            'creator_contact_info': '',
            'creator_social_channels': []
        }

        # البحث عن اسم المطور
        author_selectors = ['.author', '.post-author', '.by-author', '.creator']
        for selector in author_selectors:
            element = soup.select_one(selector)
            if element:
                creator_info['creator_name'] = self.clean_text(element.get_text())
                break

        # البحث عن روابط التواصل الاجتماعي
        social_links = soup.find_all('a', href=True)
        for link in social_links:
            href = link.get('href', '').lower()
            if any(platform in href for platform in ['youtube', 'discord', 'twitter', 'instagram']):
                platform = self.get_social_platform(href)
                creator_info['creator_social_channels'].append(f"{platform}: {link['href']}")

        return creator_info

    def get_social_platform(self, url: str) -> str:
        """تحديد منصة التواصل الاجتماعي"""
        url_lower = url.lower()
        if 'youtube' in url_lower:
            return 'YouTube'
        elif 'discord' in url_lower:
            return 'Discord'
        elif 'twitter' in url_lower:
            return 'Twitter'
        elif 'instagram' in url_lower:
            return 'Instagram'
        else:
            return 'Other'

    def create_custom_description(self, mod_data: Dict[str, Any], soup: BeautifulSoup) -> str:
        """إنشاء وصف مخصص للمود بالعربية والإنجليزية"""
        mod_name = mod_data.get('name', 'Unknown Mod')
        category = mod_data.get('category', 'Addon')

        # استخراج الميزات من الصفحة
        features = self.extract_features(soup)

        # إنشاء وصف إنجليزي
        english_desc = self.create_english_description(mod_name, category, features)

        # إنشاء وصف عربي
        arabic_desc = self.create_arabic_description(mod_name, category, features)

        # دمج الوصفين
        combined_desc = f"{english_desc}\n\n{arabic_desc}"

        return combined_desc

    def extract_features(self, soup: BeautifulSoup) -> List[str]:
        """استخراج ميزات المود من الصفحة"""
        features = []

        # البحث في القوائم
        lists = soup.find_all(['ul', 'ol'])
        for ul in lists:
            items = ul.find_all('li')
            for li in items:
                text = self.clean_text(li.get_text())
                if text and len(text) > 10 and len(text) < 100:
                    # تجاهل النصوص غير المفيدة
                    skip_words = ['download', 'discord', 'visit', 'click', 'link']
                    if not any(word in text.lower() for word in skip_words):
                        features.append(text)

        # البحث في الفقرات للميزات
        paragraphs = soup.find_all('p')
        for p in paragraphs:
            text = self.clean_text(p.get_text())
            # البحث عن جمل تصف الميزات
            if any(keyword in text.lower() for keyword in ['adds', 'includes', 'features', 'new', 'custom']):
                if len(text) > 20 and len(text) < 200:
                    features.append(text)

        return features[:5]  # أول 5 ميزات

    def create_english_description(self, mod_name: str, category: str, features: List[str]) -> str:
        """إنشاء وصف إنجليزي"""
        # تحديد نوع المحتوى
        content_type = "addon" if category == "Addons" else category.lower()

        # بداية الوصف
        intro = f"{mod_name} is an innovative Minecraft {content_type} that enhances your gaming experience with exciting new content."

        # إضافة الميزات
        features_text = ""
        if features:
            features_text = " This mod includes: " + ", ".join(features[:3]) + "."

        # خاتمة
        outro = f" Perfect for players who want to expand their Minecraft world with unique {content_type} features."

        return intro + features_text + outro

    def create_arabic_description(self, mod_name: str, category: str, features: List[str]) -> str:
        """إنشاء وصف عربي"""
        # تحديد نوع المحتوى بالعربية
        if category == "Addons":
            content_type = "إضافة"
        elif category == "Shaders":
            content_type = "شيدر"
        elif category == "Texture Pack":
            content_type = "حزمة نسيج"
        else:
            content_type = "مود"

        # بداية الوصف
        intro = f"{mod_name} هو {content_type} مبتكر لماين كرافت يحسن تجربة اللعب بمحتوى جديد ومثير."

        # إضافة الميزات
        features_text = ""
        if features:
            features_text = f" يتضمن هذا ال{content_type}: " + "، ".join(features[:3]) + "."

        # خاتمة
        outro = f" مثالي للاعبين الذين يريدون توسيع عالم ماين كرافت بميزات {content_type} فريدة."

        return intro + features_text + outro

def scrape_mcpedl_with_selenium(url: str) -> Optional[Dict[str, Any]]:
    """استخراج بيانات MCPEDL باستخدام Selenium"""
    if not SELENIUM_AVAILABLE:
        print("Selenium غير متوفر")
        return None

    scraper = MCPEDLSeleniumScraper()

    try:
        # جلب الصفحة
        soup = scraper.fetch_page_with_selenium(url)

        if not soup:
            return None

        # استخراج البيانات باستخدام محسن خاص بـ Selenium
        enhanced_scraper = EnhancedMCPEDLExtractor()

        mod_data = {
            'name': enhanced_scraper.extract_title(soup),
            'description': enhanced_scraper.extract_description(soup),
            'category': enhanced_scraper.extract_category(soup),
            'image_urls': enhanced_scraper.extract_images(soup, url),
            'version': enhanced_scraper.extract_versions(soup),
            'source_url': url
        }

        # معلومات التحميل
        download_info = enhanced_scraper.extract_download_info(soup, url)
        mod_data.update(download_info)

        # معلومات المطور
        creator_info = enhanced_scraper.extract_creator_info(soup)
        mod_data.update(creator_info)

        # التحقق من البيانات الأساسية
        if not mod_data['name']:
            print("لم يتم العثور على اسم المود")
            return None

        # تحسين الوصف إذا كان عاماً جداً
        if len(mod_data['description']) < 100 or 'enhance your gameplay' in mod_data['description']:
            mod_data['description'] = enhanced_scraper.create_custom_description(mod_data, soup)

        print(f"تم استخراج بيانات المود بنجاح باستخدام Selenium: {mod_data['name']}")
        print(f"عدد الصور المستخرجة: {len(mod_data.get('image_urls', []))}")
        print(f"طول الوصف: {len(mod_data.get('description', ''))} حرف")

        return mod_data

    finally:
        scraper.close()

# دالة للاختبار
def test_selenium_scraper():
    """اختبار مستخرج Selenium"""
    if not SELENIUM_AVAILABLE:
        print("❌ Selenium غير متوفر للاختبار")
        return False

    test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"

    print(f"🧪 اختبار Selenium مع: {test_url}")

    result = scrape_mcpedl_with_selenium(test_url)

    if result:
        print("✅ نجح اختبار Selenium!")
        print(f"اسم المود: {result.get('name', 'غير متوفر')}")
        return True
    else:
        print("❌ فشل اختبار Selenium")
        return False

if __name__ == "__main__":
    test_selenium_scraper()
